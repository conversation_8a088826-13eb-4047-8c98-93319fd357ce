.. Copyright 2016 The Cartographer Authors

.. Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

..      http://www.apache.org/licenses/LICENSE-2.0

.. Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

===========
Public Data
===========

2D Cartographer Backpack – Deutsches Museum
===========================================

This data was collected using a 2D LIDAR backpack at the
`Deutsches Museum <https://en.wikipedia.org/wiki/Deutsches_Museum>`_.
Each bag contains data from an IMU, data from a horizontal LIDAR intended for 2D
SLAM, and data from an additional vertical (i.e. push broom) LIDAR.

License
-------

Copyright 2016 The Cartographer Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Data
----

=================================================================================================================================== ======== ====== ===== ============================
`ROS Bag <http://wiki.ros.org/Bags>`_                                                                                               Duration Size   Floor Known Issues
=================================================================================================================================== ======== ====== ===== ============================
`b0-2014-07-11-10-58-16.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-07-11-10-58-16.bag>`_ 149 s    38 MB  1. OG
`b0-2014-07-11-11-00-49.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-07-11-11-00-49.bag>`_ 513 s    135 MB 1. OG
`b0-2014-07-21-12-42-53.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-07-21-12-42-53.bag>`_ 244 s    64 MB  1. OG
`b0-2014-07-21-12-49-19.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-07-21-12-49-19.bag>`_ 344 s    93 MB  EG    1 gap in vertical laser data
`b0-2014-07-21-12-55-35.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-07-21-12-55-35.bag>`_ 892 s    237 MB EG
`b0-2014-07-21-13-11-35.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-07-21-13-11-35.bag>`_ 615 s    162 MB EG
`b0-2014-08-14-13-23-01.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-08-14-13-23-01.bag>`_ 768 s    204 MB 1. OG
`b0-2014-08-14-13-36-48.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-08-14-13-36-48.bag>`_ 331 s    87 MB  1. OG
`b0-2014-10-07-12-13-36.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-10-07-12-13-36.bag>`_ 470 s    125 MB 1. OG
`b0-2014-10-07-12-34-42.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-10-07-12-34-42.bag>`_ 491 s    127 MB 1. OG
`b0-2014-10-07-12-43-25.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-10-07-12-43-25.bag>`_ 288 s    77 MB  1. OG
`b0-2014-10-07-12-50-07.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b0-2014-10-07-12-50-07.bag>`_ 815 s    215 MB 1. OG
`b1-2014-09-25-10-11-12.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b1-2014-09-25-10-11-12.bag>`_ 1829 s   480 MB EG
`b1-2014-10-02-14-08-42.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b1-2014-10-02-14-08-42.bag>`_ 930 s    245 MB 1. OG
`b1-2014-10-02-14-33-25.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b1-2014-10-02-14-33-25.bag>`_ 709 s    181 MB 1. OG
`b1-2014-10-07-12-12-04.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b1-2014-10-07-12-12-04.bag>`_ 737 s    194 MB 1. OG
`b1-2014-10-07-12-34-51.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b1-2014-10-07-12-34-51.bag>`_ 766 s    198 MB 1. OG
`b2-2014-11-24-14-20-50.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-11-24-14-20-50.bag>`_ 679 s    177 MB 1. OG
`b2-2014-11-24-14-33-46.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-11-24-14-33-46.bag>`_ 1285 s   330 MB 1. OG
`b2-2014-12-03-10-14-13.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-03-10-14-13.bag>`_ 1051 s   275 MB 1. OG
`b2-2014-12-03-10-33-51.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-03-10-33-51.bag>`_ 356 s    89 MB  1. OG
`b2-2014-12-03-10-40-04.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-03-10-40-04.bag>`_ 453 s    119 MB 1. OG
`b2-2014-12-12-13-51-02.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-12-13-51-02.bag>`_ 1428 s   368 MB 1. OG
`b2-2014-12-12-14-18-43.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-12-14-18-43.bag>`_ 1164 s   301 MB 1. OG
`b2-2014-12-12-14-41-29.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-12-14-41-29.bag>`_ 168 s    46 MB  1. OG
`b2-2014-12-12-14-48-22.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-12-14-48-22.bag>`_ 243 s    65 MB  1. OG
`b2-2014-12-17-14-33-12.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-17-14-33-12.bag>`_ 1061 s   277 MB 1. OG
`b2-2014-12-17-14-53-26.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-17-14-53-26.bag>`_ 246 s    62 MB  1. OG
`b2-2014-12-17-14-58-13.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2014-12-17-14-58-13.bag>`_ 797 s    204 MB EG
`b2-2015-02-16-12-26-11.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-02-16-12-26-11.bag>`_ 901 s    236 MB 1. OG
`b2-2015-02-16-12-43-57.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-02-16-12-43-57.bag>`_ 1848 s   475 MB 1. OG
`b2-2015-04-14-14-16-36.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-04-14-14-16-36.bag>`_ 1353 s   349 MB 1. OG
`b2-2015-04-14-14-39-59.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-04-14-14-39-59.bag>`_ 670 s    172 MB 1. OG
`b2-2015-04-28-13-01-40.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-04-28-13-01-40.bag>`_ 618 s    162 MB 1. OG
`b2-2015-04-28-13-17-23.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-04-28-13-17-23.bag>`_ 2376 s   613 MB 1. OG
`b2-2015-05-12-12-29-05.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-05-12-12-29-05.bag>`_ 942 s    240 MB 1. OG 2 gaps in laser data
`b2-2015-05-12-12-46-34.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-05-12-12-46-34.bag>`_ 2281 s   577 MB 1. OG 14 gaps in laser data
`b2-2015-05-26-13-15-25.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-05-26-13-15-25.bag>`_ 747 s    195 MB 1. OG
`b2-2015-06-09-14-31-16.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-06-09-14-31-16.bag>`_ 1297 s   336 MB 1. OG
`b2-2015-06-25-14-25-51.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-06-25-14-25-51.bag>`_ 1071 s   272 MB 1. OG
`b2-2015-07-07-11-27-05.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-07-07-11-27-05.bag>`_ 1390 s   362 MB 1. OG
`b2-2015-07-21-13-03-21.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-07-21-13-03-21.bag>`_ 894 s    239 MB 1. OG
`b2-2015-08-04-13-39-24.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-08-04-13-39-24.bag>`_ 809 s    212 MB 1. OG
`b2-2015-08-18-11-42-31.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-08-18-11-42-31.bag>`_ 588 s    155 MB UG
`b2-2015-08-18-11-55-04.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-08-18-11-55-04.bag>`_ 504 s    130 MB UG
`b2-2015-08-18-12-06-34.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-08-18-12-06-34.bag>`_ 1299 s   349 MB EG
`b2-2015-09-01-11-55-40.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-09-01-11-55-40.bag>`_ 1037 s   274 MB UG
`b2-2015-09-01-12-16-13.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-09-01-12-16-13.bag>`_ 918 s    252 MB EG
`b2-2015-09-15-14-19-11.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-09-15-14-19-11.bag>`_ 859 s    225 MB 1. OG
`b2-2015-11-24-14-12-27.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2015-11-24-14-12-27.bag>`_ 843 s    226 MB 1. OG
`b2-2016-01-19-14-10-47.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2016-01-19-14-10-47.bag>`_ 310 s    81 MB  1. OG
`b2-2016-02-02-14-01-56.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2016-02-02-14-01-56.bag>`_ 787 s    213 MB EG    1 gap in laser data
`b2-2016-03-01-14-09-37.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2016-03-01-14-09-37.bag>`_ 948 s    255 MB EG
`b2-2016-03-15-14-23-01.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2016-03-15-14-23-01.bag>`_ 810 s    215 MB EG
`b2-2016-04-05-14-44-52.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2016-04-05-14-44-52.bag>`_ 360 s    94 MB  1. OG
`b2-2016-04-27-12-31-41.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_2d/b2-2016-04-27-12-31-41.bag>`_ 881 s    234 MB 1. OG
=================================================================================================================================== ======== ====== ===== ============================

3D Cartographer Backpack – Deutsches Museum
===========================================

This data was collected using a 3D LIDAR backpack at the
`Deutsches Museum <https://en.wikipedia.org/wiki/Deutsches_Museum>`_.
Each bag contains data from an IMU and from two Velodyne VLP-16 LIDARs,
one mounted horizontally (i.e. spin axis up) and one vertically
(i.e. push broom).

License
-------

Copyright 2016 The Cartographer Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Data
----

==================================================================================================================================================== ======== ====== ================================================
`ROS Bag <http://wiki.ros.org/Bags>`_                                                                                                                Duration Size   Known Issues
==================================================================================================================================================== ======== ====== ================================================
`b3-2015-12-10-12-41-07.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2015-12-10-12-41-07.bag>`_                  1466 s   7.3 GB 1 large gap in data, no intensities
`b3-2015-12-10-13-10-17.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2015-12-10-13-10-17.bag>`_                  718 s    5.5 GB 1 gap in data, no intensities
`b3-2015-12-10-13-31-28.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2015-12-10-13-31-28.bag>`_                  720 s    5.2 GB 2 large gaps in data, no intensities
`b3-2015-12-10-13-55-20.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2015-12-10-13-55-20.bag>`_ 429 s    3.3 GB
`b3-2015-12-14-15-13-53.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2015-12-14-15-13-53.bag>`_                  916 s    7.1 GB no intensities
`b3-2016-01-19-13-26-24.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-01-19-13-26-24.bag>`_                  1098 s   8.1 GB no intensities
`b3-2016-01-19-13-50-11.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-01-19-13-50-11.bag>`_                  318 s    2.5 GB no intensities
`b3-2016-02-02-13-32-01.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-02-02-13-32-01.bag>`_                  47 s     366 MB no intensities
`b3-2016-02-02-13-33-30.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-02-02-13-33-30.bag>`_                  1176 s   9.0 GB no intensities
`b3-2016-02-09-13-17-39.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-02-09-13-17-39.bag>`_ 529 s    4.0 GB
`b3-2016-02-09-13-31-50.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-02-09-13-31-50.bag>`_                  801 s    6.1 GB no intensities
`b3-2016-02-10-08-08-26.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-02-10-08-08-26.bag>`_ 3371 s   25 GB
`b3-2016-03-01-13-39-41.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-03-01-13-39-41.bag>`_ 382 s    2.9 GB
`b3-2016-03-01-15-42-37.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-03-01-15-42-37.bag>`_                  3483 s   17 GB  6 large gaps in data, no intensities
`b3-2016-03-01-16-42-00.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-03-01-16-42-00.bag>`_                  313 s    2.4 GB no intensities
`b3-2016-03-02-10-09-32.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-03-02-10-09-32.bag>`_                  1150 s   6.6 GB 3 large gaps in data, no intensities
`b3-2016-04-05-13-54-42.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-04-05-13-54-42.bag>`_                  829 s    6.1 GB no intensities
`b3-2016-04-05-14-14-00.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-04-05-14-14-00.bag>`_ 1221 s   9.1 GB
`b3-2016-04-05-15-51-36.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-04-05-15-51-36.bag>`_ 30 s     231 MB
`b3-2016-04-05-15-52-20.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-04-05-15-52-20.bag>`_                  377 s    2.7 GB no intensities
`b3-2016-04-05-16-00-55.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-04-05-16-00-55.bag>`_                  940 s    6.9 GB no intensities
`b3-2016-04-27-12-25-00.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-04-27-12-25-00.bag>`_ 2793 s   23 GB
`b3-2016-04-27-12-56-11.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-04-27-12-56-11.bag>`_ 2905 s   21 GB
`b3-2016-05-10-12-56-33.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/with_intensities/b3-2016-05-10-12-56-33.bag>`_ 1767 s   13 GB
`b3-2016-06-07-12-42-49.bag <https://storage.googleapis.com/cartographer-public-data/bags/backpack_3d/b3-2016-06-07-12-42-49.bag>`_                  596 s    3.9 GB 3 gaps in horizontal laser data, no intensities
==================================================================================================================================================== ======== ====== ================================================

MiR
===========================================

This data was collected using `MiR100 <http://www.mobile-industrial-robots.com/de/products/mir100/>`_.
An additional Logitech Webcam C930e Full HD camera was attached on top to
collect images for landmark detection.

License
-------

Copyright 2018 The Cartographer Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Data
----

==================================================================================================================================================== ======== =======
`ROS Bag <http://wiki.ros.org/Bags>`_                                                                                                                Duration Size
==================================================================================================================================================== ======== =======
`landmarks_demo_uncalibrated.bag <https://storage.googleapis.com/cartographer-public-data/bags/mir/landmarks_demo_uncalibrated.bag>`_                   180 s 41.7 MB
==================================================================================================================================================== ======== =======

PR2 – Willow Garage
===================

This is the Willow Garage data set, described in:

* "An Object-Based Semantic World Model for Long-Term Change Detection and
  Semantic Querying.", by Julian Mason and Bhaskara Marthi, IROS 2012.

More details about these data can be found in:

* "Unsupervised Discovery of Object Classes with a Mobile Robot", by Julian
  Mason, Bhaskara Marthi, and Ronald Parr. ICRA 2014.
* "Object Discovery with a Mobile Robot" by Julian Mason. PhD Thesis, 2013.

License
-------

Copyright (c) 2011, Willow Garage
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
* Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
* Neither the name of the <organization> nor the
  names of its contributors may be used to endorse or promote products
  derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Data
----

===================================================================================================================== =======================
`ROS Bag <http://wiki.ros.org/Bags>`_                                                                                 Known Issues
===================================================================================================================== =======================
`2011-08-03-16-16-43.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-03-16-16-43.bag>`_ Missing base laser data
`2011-08-03-20-03-22.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-03-20-03-22.bag>`_
`2011-08-04-12-16-23.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-04-12-16-23.bag>`_
`2011-08-04-14-27-40.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-04-14-27-40.bag>`_
`2011-08-04-23-46-28.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-04-23-46-28.bag>`_
`2011-08-05-09-27-53.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-05-09-27-53.bag>`_
`2011-08-05-12-58-41.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-05-12-58-41.bag>`_
`2011-08-05-23-19-43.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-05-23-19-43.bag>`_
`2011-08-08-09-48-17.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-08-09-48-17.bag>`_
`2011-08-08-14-26-55.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-08-14-26-55.bag>`_
`2011-08-08-23-29-37.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-08-23-29-37.bag>`_
`2011-08-09-08-49-52.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-09-08-49-52.bag>`_
`2011-08-09-14-32-35.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-09-14-32-35.bag>`_
`2011-08-09-22-31-30.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-09-22-31-30.bag>`_
`2011-08-10-09-36-26.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-10-09-36-26.bag>`_
`2011-08-10-14-48-32.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-10-14-48-32.bag>`_
`2011-08-11-01-31-15.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-11-01-31-15.bag>`_
`2011-08-11-08-36-01.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-11-08-36-01.bag>`_
`2011-08-11-14-27-41.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-11-14-27-41.bag>`_
`2011-08-11-22-03-37.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-11-22-03-37.bag>`_
`2011-08-12-09-06-48.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-12-09-06-48.bag>`_
`2011-08-12-16-39-48.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-12-16-39-48.bag>`_
`2011-08-12-22-46-34.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-12-22-46-34.bag>`_
`2011-08-15-17-22-26.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-15-17-22-26.bag>`_
`2011-08-15-21-26-26.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-15-21-26-26.bag>`_
`2011-08-16-09-20-08.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-16-09-20-08.bag>`_
`2011-08-16-18-40-52.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-16-18-40-52.bag>`_
`2011-08-16-20-59-00.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-16-20-59-00.bag>`_
`2011-08-17-15-51-51.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-17-15-51-51.bag>`_
`2011-08-17-21-17-05.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-17-21-17-05.bag>`_
`2011-08-18-20-33-16.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-18-20-33-16.bag>`_
`2011-08-18-20-52-30.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-18-20-52-30.bag>`_
`2011-08-19-10-12-20.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-19-10-12-20.bag>`_
`2011-08-19-14-17-55.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-19-14-17-55.bag>`_
`2011-08-19-21-35-17.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-19-21-35-17.bag>`_
`2011-08-22-10-02-27.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-22-10-02-27.bag>`_
`2011-08-22-14-53-33.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-22-14-53-33.bag>`_
`2011-08-23-01-11-53.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-23-01-11-53.bag>`_
`2011-08-23-09-21-17.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-23-09-21-17.bag>`_
`2011-08-24-09-52-14.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-24-09-52-14.bag>`_
`2011-08-24-15-01-39.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-24-15-01-39.bag>`_
`2011-08-24-19-47-10.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-24-19-47-10.bag>`_
`2011-08-25-09-31-05.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-25-09-31-05.bag>`_
`2011-08-25-20-14-56.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-25-20-14-56.bag>`_
`2011-08-25-20-38-39.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-25-20-38-39.bag>`_
`2011-08-26-09-58-19.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-26-09-58-19.bag>`_
`2011-08-29-15-48-07.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-29-15-48-07.bag>`_
`2011-08-29-21-14-07.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-29-21-14-07.bag>`_
`2011-08-30-08-55-28.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-30-08-55-28.bag>`_
`2011-08-30-20-49-42.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-30-20-49-42.bag>`_
`2011-08-30-21-17-56.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-30-21-17-56.bag>`_
`2011-08-31-20-29-19.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-31-20-29-19.bag>`_
`2011-08-31-20-44-19.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-08-31-20-44-19.bag>`_
`2011-09-01-08-21-33.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-01-08-21-33.bag>`_
`2011-09-02-09-20-25.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-02-09-20-25.bag>`_
`2011-09-06-09-04-41.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-06-09-04-41.bag>`_
`2011-09-06-13-20-36.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-06-13-20-36.bag>`_
`2011-09-08-13-14-39.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-08-13-14-39.bag>`_
`2011-09-09-13-22-57.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-09-13-22-57.bag>`_
`2011-09-11-07-34-22.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-11-07-34-22.bag>`_
`2011-09-11-09-43-46.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-11-09-43-46.bag>`_
`2011-09-12-14-18-56.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-12-14-18-56.bag>`_
`2011-09-12-14-47-01.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-12-14-47-01.bag>`_
`2011-09-13-10-23-31.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-13-10-23-31.bag>`_
`2011-09-13-13-44-21.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-13-13-44-21.bag>`_
`2011-09-14-10-19-20.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-14-10-19-20.bag>`_
`2011-09-15-08-32-46.bag <https://storage.googleapis.com/cartographer-public-data/bags/pr2/2011-09-15-08-32-46.bag>`_
===================================================================================================================== =======================

Magazino
========

Datasets recorded on `Magazino robots <https://www.magazino.eu/?lang=en>`_.

See the `cartographer_magazino <https://github.com/magazino/cartographer_magazino>`_
repository for an integration of Magazino robot data for Cartographer.

See the ``LICENSE`` file in ``cartographer_magazino`` for details on the dataset
license. 

Data
----

=================================================================================================================================== ======== ======== ============
`ROS Bag <http://wiki.ros.org/Bags>`_                                                                                               Duration Size     Known Issues
=================================================================================================================================== ======== ======== ============
`hallway_return.bag <https://storage.googleapis.com/cartographer-public-data/bags/toru/hallway_return.bag>`_                        350 s    102.8 MB
`hallway_localization.bag <https://storage.googleapis.com/cartographer-public-data/bags/toru/hallway_localization.bag>`_            137 s    40.4 MB
=================================================================================================================================== ======== ======== ============
