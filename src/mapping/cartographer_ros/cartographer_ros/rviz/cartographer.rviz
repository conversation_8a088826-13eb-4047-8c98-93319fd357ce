Panels:
  - Class: rviz_common/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /LaserScan1/Topic1
      Splitter Ratio: 0.3916349709033966
    Tree Height: 347
  - Class: rviz_common/Selection
    Name: Selection
  - Class: rviz_common/Tool Properties
    Expanded:
      - /Publish Point1
      - /2D Pose Estimate1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz_common/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz_default_plugins/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz_default_plugins/TF
      Enabled: true
      Frame Timeout: 15
      Frames:
        All Enabled: true
        base_footprint:
          Value: true
        base_link:
          Value: true
        base_scan:
          Value: true
        caster_back_link:
          Value: true
        imu_link:
          Value: true
        map:
          Value: true
        odom:
          Value: true
        wheel_left_link:
          Value: true
        wheel_right_link:
          Value: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: true
      Tree:
        map:
          odom:
            base_footprint:
              base_link:
                base_scan:
                  {}
                caster_back_link:
                  {}
                imu_link:
                  {}
                wheel_left_link:
                  {}
                wheel_right_link:
                  {}
      Update Interval: 0
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz_default_plugins/LaserScan
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4439
      Min Color: 0; 0; 0
      Min Intensity: 105
      Name: LaserScan
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.019999999552965164
      Style: Boxes
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /scan
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Angle Tolerance: 0.10000000149011612
      Class: rviz_default_plugins/Odometry
      Covariance:
        Orientation:
          Alpha: 0.5
          Color: 255; 255; 127
          Color Style: Unique
          Frame: Local
          Offset: 1
          Scale: 1
          Value: true
        Position:
          Alpha: 0.30000001192092896
          Color: 204; 51; 204
          Scale: 1
          Value: true
        Value: false
      Enabled: false
      Keep: 100
      Name: Odometry
      Position Tolerance: 0.10000000149011612
      Shape:
        Alpha: 1
        Axes Length: 1
        Axes Radius: 0.10000000149011612
        Color: 255; 25; 0
        Head Length: 0.30000001192092896
        Head Radius: 0.10000000149011612
        Shaft Length: 1
        Shaft Radius: 0.05000000074505806
        Value: Arrow
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /odom
      Value: false
    - Alpha: 0.699999988079071
      Class: rviz_default_plugins/Map
      Color Scheme: map
      Draw Behind: false
      Enabled: true
      Name: Map
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /map
      Update Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /map_updates
      Use Timestamp: false
      Value: true
    - Class: rviz_common/Group
      Displays:
        - Class: rviz_common/Group
          Displays:
            - Alpha: 0.699999988079071
              Class: rviz_default_plugins/Map
              Color Scheme: costmap
              Draw Behind: true
              Enabled: true
              Name: Map
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /global_costmap/costmap
              Update Topic:
                Depth: 5
                Durability Policy: Volatile
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /global_costmap/costmap_updates
              Use Timestamp: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz_default_plugins/Path
              Color: 255; 0; 0
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: Path
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /global_plan
              Value: true
          Enabled: true
          Name: Global Map
        - Class: rviz_common/Group
          Displays:
            - Alpha: 1
              Class: rviz_default_plugins/Polygon
              Color: 25; 255; 0
              Enabled: true
              Name: Polygon
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /local_costmap/footprint
              Value: true
            - Alpha: 0.699999988079071
              Class: rviz_default_plugins/Map
              Color Scheme: costmap
              Draw Behind: false
              Enabled: true
              Name: Map
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /local_costmap/costmap
              Update Topic:
                Depth: 5
                Durability Policy: Volatile
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /local_costmap/costmap_updates
              Use Timestamp: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz_default_plugins/Path
              Color: 255; 255; 0
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: Path
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /local_plan
              Value: true
          Enabled: true
          Name: Local Map
        - Alpha: 1
          Arrow Length: 0.05000000074505806
          Axes Length: 0.30000001192092896
          Axes Radius: 0.009999999776482582
          Class: rviz_default_plugins/PoseArray
          Color: 0; 192; 0
          Enabled: true
          Head Length: 0.07000000029802322
          Head Radius: 0.029999999329447746
          Name: PoseArray
          Shaft Length: 0.23000000417232513
          Shaft Radius: 0.009999999776482582
          Shape: Arrow (Flat)
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /particlecloud
          Value: true
      Enabled: false
      Name: Navigation
    - Class: rviz_common/Group
      Displays:
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 0.18203988671302795
            Min Value: 0.18195410072803497
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 0; 255; 0
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 4096
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: scan_matched_points2
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.009999999776482582
          Style: Boxes
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /scan_matched_points2
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Class: rviz_default_plugins/MarkerArray
          Enabled: false
          Name: Trajectories
          Namespaces:
            {}
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /trajectory_node_list
          Value: false
        - Class: rviz_default_plugins/MarkerArray
          Enabled: false
          Name: Constraints
          Namespaces:
            {}
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /constraint_list
          Value: false
        - Class: rviz_default_plugins/MarkerArray
          Enabled: false
          Name: Landmark Poses
          Namespaces:
            {}
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /landmark_poses_list
          Value: false
      Enabled: true
      Name: Cartographer
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Fixed Frame: map
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz_default_plugins/MoveCamera
    - Class: rviz_default_plugins/Select
    - Class: rviz_default_plugins/FocusCamera
    - Class: rviz_default_plugins/Measure
      Line color: 128; 128; 0
    - Class: rviz_default_plugins/SetGoal
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /move_base_simple/goal
    - Class: rviz_default_plugins/PublishPoint
      Single click: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /clicked_point
    - Class: rviz_default_plugins/SetInitialPose
      Covariance x: 0.25
      Covariance y: 0.25
      Covariance yaw: 0.06853891909122467
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: initialpose
  Transformation:
    Current:
      Class: rviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Angle: 0
      Class: rviz_default_plugins/TopDownOrtho
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Scale: 119.26066589355469
      Target Frame: <Fixed Frame>
      Value: TopDownOrtho (rviz_default_plugins)
      X: 0.0023878198117017746
      Y: -0.17037495970726013
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 576
  Hide Left Dock: false
  Hide Right Dock: true
  QMainWindow State: 000000ff00000000fd00000004000000000000017a000001e6fc0200000008fb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073010000003d000001e6000000c900fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261000000010000010f00000236fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073000000003d00000236000000a400fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000004420000003efc0100000002fb0000000800540069006d00650100000000000004420000000000000000fb0000000800540069006d00650100000000000004500000000000000000000002db000001e600000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 1115
  X: 164
  Y: 106
