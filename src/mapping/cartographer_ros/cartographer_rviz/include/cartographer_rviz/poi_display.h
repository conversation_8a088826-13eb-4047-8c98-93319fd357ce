#ifndef CARTOGRAPHER_RVIZ_POI_DISPLAY_H_
#define CARTOGRAPHER_RVIZ_POI_DISPLAY_H_

#include <memory>
#include <string>
#include <map>

#include "rviz_common/display.hpp"
#include "rviz_common/properties/string_property.hpp"
#include "rviz_common/properties/float_property.hpp"
#include "rviz_common/properties/color_property.hpp"
#include "rviz_common/properties/bool_property.hpp"
#include "rviz_common/interaction/selection_manager.hpp"
#include "rviz_common/viewport_mouse_event.hpp"

#include "interactive_markers/interactive_marker_server.hpp"
#include "visualization_msgs/msg/interactive_marker.hpp"
#include "visualization_msgs/msg/interactive_marker_feedback.hpp"
#include "visualization_msgs/msg/marker.hpp"

#include "cartographer_ros_msgs/msg/poi.hpp"
#include "cartographer_ros_msgs/srv/add_poi.hpp"
#include "cartographer_ros_msgs/srv/remove_poi.hpp"
#include "cartographer_ros_msgs/srv/update_poi.hpp"
#include "cartographer_ros_msgs/srv/list_poi.hpp"
#include "cartographer_ros_msgs/srv/get_poi.hpp"

#include "rclcpp/rclcpp.hpp"

namespace cartographer_rviz {

class POIDisplay : public rviz_common::Display {
  Q_OBJECT
 public:
  POIDisplay();
  virtual ~POIDisplay();

 protected:
  virtual void onInitialize() override;
  virtual void update(float wall_dt, float ros_dt) override;
  virtual void reset() override;

 private Q_SLOTS:
  void updateVisualization();

 private:
  void createPOIVisual(const cartographer_ros_msgs::msg::POI& poi);
  void updatePOIVisual(const cartographer_ros_msgs::msg::POI& poi);
  void removePOIVisual(const std::string& id);

  void handleMouseEvent(rviz_common::ViewportMouseEvent& event);
  void handlePOISelection(const std::shared_ptr<const visualization_msgs::msg::InteractiveMarkerFeedback>& feedback);
  void handlePOIMenuSelection(const std::shared_ptr<const visualization_msgs::msg::InteractiveMarkerFeedback>& feedback);
  void showPOIEditDialog(const std::string& poi_id, const std::string& field_name);

  // ROS service clients
  rclcpp::Client<cartographer_ros_msgs::srv::AddPOI>::SharedPtr add_poi_client_;
  rclcpp::Client<cartographer_ros_msgs::srv::RemovePOI>::SharedPtr remove_poi_client_;
  rclcpp::Client<cartographer_ros_msgs::srv::UpdatePOI>::SharedPtr update_poi_client_;
  rclcpp::Client<cartographer_ros_msgs::srv::ListPOI>::SharedPtr list_pois_client_;
  rclcpp::Client<cartographer_ros_msgs::srv::GetPOI>::SharedPtr get_poi_client_;

  // RViz properties
  rviz_common::properties::StringProperty* poi_type_property_;
  rviz_common::properties::FloatProperty* poi_scale_property_;
  rviz_common::properties::ColorProperty* poi_color_property_;
  rviz_common::properties::BoolProperty* show_labels_property_;
  rviz_common::properties::BoolProperty* enable_interaction_property_;
  rviz_common::properties::BoolProperty* auto_refresh_property_;

  // Interactive marker server for POI manipulation
  std::shared_ptr<interactive_markers::InteractiveMarkerServer> marker_server_;

  // Timer for refreshing POI list
  rclcpp::TimerBase::SharedPtr refresh_timer_;

  // Map of POI ID to visual representation
  std::map<std::string, cartographer_ros_msgs::msg::POI> poi_visuals_;

  // Flag to indicate if visualization needs update
  bool needs_update_;

  void refreshPOIs();
  void processPOIListResponse(const std::shared_ptr<cartographer_ros_msgs::srv::ListPOI::Response>& response);

};

}  // namespace cartographer_rviz

#endif  // CARTOGRAPHER_RVIZ_POI_DISPLAY_H_