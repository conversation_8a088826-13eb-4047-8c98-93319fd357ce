/*
 * Copyright 2016 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

material cartographer_ros/Submap
{
  technique
  {
    pass
    {
      vertex_program_ref cartographer_ros/glsl120/submap.vert {}

      fragment_program_ref cartographer_ros/glsl120/submap.frag
      {
        param_named u_submap int 0
        param_named u_alpha float 1.0
      }
    }
  }
}
