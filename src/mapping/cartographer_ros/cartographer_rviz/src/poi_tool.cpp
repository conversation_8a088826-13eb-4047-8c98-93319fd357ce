#include "poi_tool.h"

#include <OgreRay.h>
#include <OgreVector3.h>
#include <OgreCamera.h>
#include <OgreViewport.h>
#include <OgreSceneManager.h>
#include <OgrePlane.h>

#include "rviz_common/display_context.hpp"
#include "rviz_common/viewport_mouse_event.hpp"
#include "rviz_common/load_resource.hpp"
#include "rviz_common/render_panel.hpp"
#include "rviz_common/view_manager.hpp"
#include "rviz_rendering/render_window.hpp"

#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "tf2/utils.h"

namespace cartographer_rviz {

POITool::POITool() {
  shortcut_key_ = 'p';
}

POITool::~POITool() = default;

void POITool::onInitialize() {
  // Get ROS node
  auto node = context_->getRosNodeAbstraction().lock()->get_raw_node();
  
  // Initialize ROS service clients
  add_poi_client_ = node->create_client<cartographer_ros_msgs::srv::AddPOI>("add_poi");
  remove_poi_client_ = node->create_client<cartographer_ros_msgs::srv::RemovePOI>("remove_poi");
  list_pois_client_ = node->create_client<cartographer_ros_msgs::srv::ListPOI>("list_poi");
  
  // Set cursor
  std_cursor_ = rviz_common::getDefaultCursor();
  hit_cursor_ = rviz_common::makeIconCursor("package://rviz_default_plugins/icons/crosshair.svg");
}

int POITool::processMouseEvent(rviz_common::ViewportMouseEvent& event) {
  if (event.type == QEvent::MouseButtonPress) {
    if (event.left()) {
      return processMouseClick(event);
    } else if (event.right()) {
      return processRightClick(event);
    }
  }
  return Render;
}

int POITool::processMouseClick(rviz_common::ViewportMouseEvent& event) {
  // Get the 3D position from mouse click using simplified coordinate conversion
  Ogre::Vector3 intersection;
  
  // Simple conversion from screen coordinates to world coordinates
  // This assumes a 2D map on the ground plane (z=0)
  auto render_panel = context_->getViewManager()->getRenderPanel();
  
  if (render_panel) {
    // In RViz2, get camera through ViewManager -> ViewController -> getCamera()
    auto view_manager = context_->getViewManager();
    auto view_controller = view_manager->getCurrent();
    if (view_controller) {
      auto camera = view_controller->getCamera();
      if (camera) {
      // Create a ray from camera through the mouse position
      Ogre::Ray mouse_ray = camera->getCameraToViewportRay(
        static_cast<float>(event.x) / render_panel->width(),
        static_cast<float>(event.y) / render_panel->height());
      
      // Intersect with ground plane (z = 0)
      Ogre::Plane ground_plane(Ogre::Vector3::UNIT_Z, 0.0f);
      auto result = mouse_ray.intersects(ground_plane);
      
      if (result.first) {
        intersection = mouse_ray.getPoint(result.second);
    
      // Create new POI at clicked position
      cartographer_ros_msgs::msg::POI poi;
      poi.id = "poi_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
          std::chrono::system_clock::now().time_since_epoch()).count());
      poi.name = "New POI";
      poi.type = "default";
      poi.pose.x = intersection.x;
      poi.pose.y = intersection.y;
      poi.pose.theta = 0.0;

      // Send add POI request asynchronously
      if (add_poi_client_->service_is_ready()) {
        auto request = std::make_shared<cartographer_ros_msgs::srv::AddPOI::Request>();
        request->poi = poi;

        RCLCPP_INFO(rclcpp::get_logger("POITool"), 
                    "Adding POI '%s' at position (%.2f, %.2f)", 
                    poi.name.c_str(), poi.pose.x, poi.pose.y);
        
        // Use async call to avoid blocking the UI
        auto future = add_poi_client_->async_send_request(request);
        
        // Store the future for later checking if needed
        // In a production system, you might want to track these futures
        // and provide user feedback when the operation completes
        
          } else {
            RCLCPP_WARN(rclcpp::get_logger("POITool"), "Add POI service not ready");
          }
      } else {
        RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to intersect with ground plane");
      }
    } else {
      RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to get camera from view controller");
      }
    } else {
      RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to get current view controller");
    }
  } else {
    RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to get render panel");
  }
  
  return Render | Finished;
}

void POITool::activate() {
  context_->getViewManager()->getRenderPanel()->setCursor(hit_cursor_);
}

void POITool::deactivate() {
  context_->getViewManager()->getRenderPanel()->setCursor(std_cursor_);
}

int POITool::processRightClick(rviz_common::ViewportMouseEvent& event) {
  // Get the 3D position from mouse click
  Ogre::Vector3 intersection;
  
  auto render_panel = context_->getViewManager()->getRenderPanel();
  
  if (render_panel) {
    auto view_manager = context_->getViewManager();
    auto view_controller = view_manager->getCurrent();
    if (view_controller) {
      auto camera = view_controller->getCamera();
      if (camera) {
        // Create a ray from camera through the mouse position
        Ogre::Ray mouse_ray = camera->getCameraToViewportRay(
          static_cast<float>(event.x) / render_panel->width(),
          static_cast<float>(event.y) / render_panel->height());
        
        // Intersect with ground plane (z = 0)
        Ogre::Plane ground_plane(Ogre::Vector3::UNIT_Z, 0.0f);
        auto result = mouse_ray.intersects(ground_plane);
        
        if (result.first) {
          intersection = mouse_ray.getPoint(result.second);

          findPOIAtPositionAsync(intersection, [this](const std::string& poi_id, const Ogre::Vector3& position) {
            if (!poi_id.empty()) {
              // Remove the POI
              if (remove_poi_client_->service_is_ready()) {
                auto request = std::make_shared<cartographer_ros_msgs::srv::RemovePOI::Request>();
                request->id = poi_id;
                
                RCLCPP_INFO(rclcpp::get_logger("POITool"), 
                            "Removing POI '%s' at position (%.2f, %.2f)", 
                            poi_id.c_str(), position.x, position.y);
                
                // Use async call to avoid blocking the UI
                remove_poi_client_->async_send_request(request);
              } else {
                RCLCPP_WARN(rclcpp::get_logger("POITool"), "Remove POI service not ready");
              }
            } else {
              RCLCPP_INFO(rclcpp::get_logger("POITool"), 
                          "No POI found near position (%.2f, %.2f)", 
                          position.x, position.y);
            }
          });
        } else {
          RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to intersect with ground plane");
        }
      } else {
        RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to get camera from view controller");
      }
    } else {
      RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to get current view controller");
    }
  } else {
    RCLCPP_WARN(rclcpp::get_logger("POITool"), "Failed to get render panel");
  }
  
  return Render | Finished;
}

void POITool::findPOIAtPositionAsync(const Ogre::Vector3& position, 
                                    std::function<void(const std::string&, const Ogre::Vector3&)> callback) {
  if (!list_pois_client_->service_is_ready()) {
    RCLCPP_WARN(rclcpp::get_logger("POITool"), "List POI service not ready");
    callback("", position);
    return;
  }
  
  auto request = std::make_shared<cartographer_ros_msgs::srv::ListPOI::Request>();
  
  list_pois_client_->async_send_request(request, 
    [this, position, callback](rclcpp::Client<cartographer_ros_msgs::srv::ListPOI>::SharedFuture future) {
      try {
        auto response = future.get();
        const double max_distance = 0.5;
        double min_distance = max_distance;
        std::string closest_poi_id = "";
        
        for (const auto& poi : response->pois) {
          double distance = std::sqrt(
            std::pow(poi.pose.x - position.x, 2) + 
            std::pow(poi.pose.y - position.y, 2));
          
          if (distance < min_distance) {
            min_distance = distance;
            closest_poi_id = poi.id;
          }
        }
        
        callback(closest_poi_id, position);
      } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("POITool"), 
                    "Exception while getting POI list: %s", e.what());
        callback("", position);
      }
    });
}

}  // namespace cartographer_rviz

#include <pluginlib/class_list_macros.hpp>
PLUGINLIB_EXPORT_CLASS(cartographer_rviz::POITool, rviz_common::Tool)

