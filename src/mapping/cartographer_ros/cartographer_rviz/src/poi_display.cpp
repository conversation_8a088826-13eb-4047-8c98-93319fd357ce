#include "cartographer_rviz/poi_display.h"

#include <memory>
#include <string>
#include <vector>
#include <algorithm>
#include <thread>
#include <chrono>

#include "OgreSceneNode.h"
#include "OgreSceneManager.h"
#include "OgreEntity.h"
#include "OgreMaterialManager.h"
#include "OgreManualObject.h"
#include "OgreVector3.h"
#include "OgreColourValue.h"


#include "rviz_common/display_context.hpp"
#include "rviz_common/frame_manager_iface.hpp"
#include "rviz_common/properties/color_property.hpp"
#include "rviz_common/properties/float_property.hpp"
#include "rviz_common/properties/string_property.hpp"
#include "rviz_common/properties/enum_property.hpp"
#include "rviz_common/properties/bool_property.hpp"
#include "rviz_common/interaction/selection_manager.hpp"
#include "rviz_common/viewport_mouse_event.hpp"
#include "rviz_common/tool.hpp"
#include "rviz_common/render_panel.hpp"
#include "rviz_common/view_manager.hpp"


#include "interactive_markers/interactive_marker_server.hpp"
#include "visualization_msgs/msg/interactive_marker.hpp"
#include "visualization_msgs/msg/interactive_marker_control.hpp"
#include "visualization_msgs/msg/interactive_marker_feedback.hpp"
#include "visualization_msgs/msg/marker.hpp"

#include "tf2/LinearMath/Quaternion.h"
#include "tf2/LinearMath/Matrix3x3.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "tf2/utils.h"

#include "cartographer_ros_msgs/msg/poi.hpp"
#include "cartographer_ros_msgs/srv/add_poi.hpp"
#include "cartographer_ros_msgs/srv/remove_poi.hpp"
#include "cartographer_ros_msgs/srv/update_poi.hpp"
#include "cartographer_ros_msgs/srv/get_poi.hpp"
#include "cartographer_ros_msgs/srv/list_poi.hpp"

#include <QInputDialog>
#include <QLineEdit>
#include <QString>

namespace cartographer_rviz {

POIDisplay::POIDisplay() 
    : needs_update_(false) {
  // Initialize properties
  poi_type_property_ = new rviz_common::properties::StringProperty(
      "POI Type", "default",
      "Type of POI to create when clicking",
      this, SLOT(updateVisualization()));

  poi_scale_property_ = new rviz_common::properties::FloatProperty(
      "POI Scale", 1.0f,
      "Scale of POI visualization markers",
      this, SLOT(updateVisualization()));
  poi_scale_property_->setMin(0.1f);
  poi_scale_property_->setMax(5.0f);

  poi_color_property_ = new rviz_common::properties::ColorProperty(
      "POI Color", QColor(255, 85, 0),
      "Color of POI visualization markers",
      this, SLOT(updateVisualization()));

  show_labels_property_ = new rviz_common::properties::BoolProperty(
      "Show Labels", true,
      "Show POI name labels",
      this, SLOT(updateVisualization()));

  enable_interaction_property_ = new rviz_common::properties::BoolProperty(
      "Enable Interaction", true,
      "Enable POI creation and editing via mouse",
      this, SLOT(updateVisualization()));

  auto_refresh_property_ = new rviz_common::properties::BoolProperty(
      "Auto Refresh", true,
      "Automatically refresh POI list periodically",
      this, SLOT(updateVisualization()));
}

POIDisplay::~POIDisplay() {
  // Clean up resources
  reset();
}

void POIDisplay::onInitialize() {
  // Get ROS node
  auto node = context_->getRosNodeAbstraction().lock()->get_raw_node();
  
  // Initialize ROS service clients
  add_poi_client_ = node->create_client<cartographer_ros_msgs::srv::AddPOI>("add_poi");
  remove_poi_client_ = node->create_client<cartographer_ros_msgs::srv::RemovePOI>("remove_poi");
  update_poi_client_ = node->create_client<cartographer_ros_msgs::srv::UpdatePOI>("update_poi");
  get_poi_client_ = node->create_client<cartographer_ros_msgs::srv::GetPOI>("get_poi");
  list_pois_client_ = node->create_client<cartographer_ros_msgs::srv::ListPOI>("list_poi");
  
  RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), "POI service clients initialized");
  
  // Wait for service to be available with timeout
  if (!list_pois_client_->wait_for_service(std::chrono::seconds(5))) {
    RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "POI list service not available after 5 seconds, will retry during operation");
  } else {
    RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), "POI list service is ready");
  }

  // Initialize interactive marker server
  marker_server_ = std::make_shared<interactive_markers::InteractiveMarkerServer>(
      "poi_interactive_markers", node);
  
  RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), "Interactive Marker Server initialized");

  // Initialize refresh timer with longer interval to reduce performance impact
  refresh_timer_ = node->create_wall_timer(
      std::chrono::seconds(5),
      std::bind(&POIDisplay::refreshPOIs, this));

  // Load initial POIs
  needs_update_ = true;

  RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), "Node name: %s, namespace: %s",
              node->get_name(), node->get_namespace());
}

void POIDisplay::update(float wall_dt, float ros_dt) {
  // Check if visualization needs update
  if (needs_update_) {
    updateVisualization();
    needs_update_ = false;
    
    if (marker_server_) {
      marker_server_->applyChanges();
    }
  }
}

void POIDisplay::reset() {
  // Clear all POI visuals
  if (marker_server_) {
    marker_server_->clear();
    marker_server_->applyChanges();
  }
  poi_visuals_.clear();
  needs_update_ = false;
}

void POIDisplay::updateVisualization() {
  RCLCPP_DEBUG(rclcpp::get_logger("POIDisplay"), "Updating POI visualization");
  if (!marker_server_ || !isEnabled()) {
    return;
  }

  auto request = std::make_shared<cartographer_ros_msgs::srv::ListPOI::Request>();

  if (!list_pois_client_->service_is_ready()) {
    RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "POI list service not ready, will retry later");
    return;
  }
  
  RCLCPP_DEBUG(rclcpp::get_logger("POIDisplay"), "Requesting POI list from service");

  auto response_callback = [this](rclcpp::Client<cartographer_ros_msgs::srv::ListPOI>::SharedFuture future) {
    try {
      auto response = future.get();
      if (!response) {
        RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), "Received null response from POI list service");
        return;
      }
      
      if (!response->success) {
        RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "POI list service failed: %s", response->message.c_str());
        return;
      }
      
      this->processPOIListResponse(response);
      
    } catch (const std::exception& e) {
      RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), "Exception in POI list callback: %s", e.what());
    }
  };
  
  list_pois_client_->async_send_request(request, response_callback);
}

void POIDisplay::processPOIListResponse(const std::shared_ptr<cartographer_ros_msgs::srv::ListPOI::Response>& response) {
  RCLCPP_DEBUG(rclcpp::get_logger("POIDisplay"), "Successfully received %zu POIs from service", response->pois.size());

  std::set<std::string> current_poi_ids;
  for (const auto& poi : response->pois) {
    current_poi_ids.insert(poi.id);
    
    // Check if POI exists and needs update
    auto it = poi_visuals_.find(poi.id);
    if (it == poi_visuals_.end()) {
      // New POI, create visual
      createPOIVisual(poi);
    } else {
      // Existing POI, check if position changed
      auto& existing_poi = it->second;
      if (std::abs(existing_poi.pose.x - poi.pose.x) > 0.01 ||
          std::abs(existing_poi.pose.y - poi.pose.y) > 0.01 ||
          std::abs(existing_poi.pose.theta - poi.pose.theta) > 0.01 ||
          existing_poi.name != poi.name) {
        // Position or name changed, update visual
        updatePOIVisual(poi);
      }
    }
  }

  // Remove POIs that no longer exist
  auto visual_it = poi_visuals_.begin();
  while (visual_it != poi_visuals_.end()) {
    if (current_poi_ids.find(visual_it->first) == current_poi_ids.end()) {
      // POI no longer exists, remove visual
      removePOIVisual(visual_it->first);
      visual_it = poi_visuals_.erase(visual_it);
    } else {
      ++visual_it;
    }
  }

  // Apply all changes
  marker_server_->applyChanges();
}

void POIDisplay::refreshPOIs() {
  if (!list_pois_client_->service_is_ready()) {
    RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "List POI service not ready");
    return;
  }
  
  auto request = std::make_shared<cartographer_ros_msgs::srv::ListPOI::Request>();
  
  list_pois_client_->async_send_request(request,
    [this](rclcpp::Client<cartographer_ros_msgs::srv::ListPOI>::SharedFuture future) {
      try {
        auto response = future.get();
        processPOIListResponse(response);
      } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), 
                    "Exception while getting POI list: %s", e.what());
      }
    });
}

void POIDisplay::createPOIVisual(const cartographer_ros_msgs::msg::POI& poi) {
  // Create interactive marker
  visualization_msgs::msg::InteractiveMarker int_marker;
  int_marker.header.frame_id = "map";
  int_marker.header.stamp = rclcpp::Time(0);
  
  int_marker.name = poi.id;
  int_marker.description = poi.name;
  
  float base_scale = poi_scale_property_->getFloat();
  int_marker.scale = base_scale;

  int_marker.pose.position.x = poi.pose.x;
  int_marker.pose.position.y = poi.pose.y;
  int_marker.pose.position.z = 0.0;
  tf2::Quaternion q;
  q.setRPY(0, 0, poi.pose.theta);
  int_marker.pose.orientation = tf2::toMsg(q);

  visualization_msgs::msg::InteractiveMarkerControl marker_control;
  marker_control.always_visible = true;
  marker_control.interaction_mode = visualization_msgs::msg::InteractiveMarkerControl::BUTTON;  
  marker_control.name = "button_control";
  marker_control.orientation_mode = visualization_msgs::msg::InteractiveMarkerControl::INHERIT;
  
  marker_control.orientation.w = 1.0;
  marker_control.orientation.x = 0.0;
  marker_control.orientation.y = 0.0;
  marker_control.orientation.z = 0.0;

  visualization_msgs::msg::Marker poi_marker;
  poi_marker.type = visualization_msgs::msg::Marker::SPHERE;
  
  float marker_size = base_scale * 0.3f;
  poi_marker.scale.x = poi_marker.scale.y = poi_marker.scale.z = marker_size;
  
  poi_marker.color.r = poi_color_property_->getColor().redF();
  poi_marker.color.g = poi_color_property_->getColor().greenF();
  poi_marker.color.b = poi_color_property_->getColor().blueF();
  poi_marker.color.a = 1.0;
  
  poi_marker.ns = "poi_markers";
  poi_marker.id = std::hash<std::string>{}(poi.id);
  poi_marker.action = visualization_msgs::msg::Marker::ADD;

  poi_marker.pose.position.x = 0.0;
  poi_marker.pose.position.y = 0.0;
  poi_marker.pose.position.z = 0.0;
  poi_marker.pose.orientation.w = 1.0;

  marker_control.markers.push_back(poi_marker);
  int_marker.controls.push_back(marker_control);

  visualization_msgs::msg::InteractiveMarkerControl move_control;
  move_control.name = "move_3d";
  move_control.interaction_mode = visualization_msgs::msg::InteractiveMarkerControl::MOVE_3D;
  move_control.always_visible = true; 
  int_marker.controls.push_back(move_control);

  visualization_msgs::msg::InteractiveMarkerControl move_plane_control;
  move_plane_control.name = "move_plane";
  move_plane_control.interaction_mode = visualization_msgs::msg::InteractiveMarkerControl::MOVE_PLANE;
  move_plane_control.orientation.w = 1;
  move_plane_control.orientation.x = 0;
  move_plane_control.orientation.y = 0;
  move_plane_control.orientation.z = 1;
  move_plane_control.always_visible = true;
  int_marker.controls.push_back(move_plane_control);

  visualization_msgs::msg::InteractiveMarkerControl rotate_control;
  rotate_control.name = "rotate_z";
  rotate_control.interaction_mode = visualization_msgs::msg::InteractiveMarkerControl::ROTATE_AXIS;
  rotate_control.orientation.w = 1;
  rotate_control.orientation.x = 0;
  rotate_control.orientation.y = 0;
  rotate_control.orientation.z = 1;
  rotate_control.always_visible = true;
  int_marker.controls.push_back(rotate_control);

  // Add text label if enabled
  if (show_labels_property_->getBool()) {
    visualization_msgs::msg::InteractiveMarkerControl label_control;
    label_control.always_visible = true;
    label_control.interaction_mode = visualization_msgs::msg::InteractiveMarkerControl::NONE;
    label_control.name = "label";
    label_control.orientation_mode = visualization_msgs::msg::InteractiveMarkerControl::INHERIT;

    visualization_msgs::msg::Marker label_marker;
    label_marker.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
    label_marker.text = poi.name;
    
    float label_size = base_scale * 0.2f;
    label_marker.scale.z = label_size;
    
    label_marker.color.r = 1.0;
    label_marker.color.g = 1.0;
    label_marker.color.b = 1.0;
    label_marker.color.a = 1.0;
    
    label_marker.pose.position.x = 0.0;
    label_marker.pose.position.y = 0.0;
    label_marker.pose.position.z = marker_size * 0.6f + 0.1f;
    label_marker.pose.orientation.w = 1.0;
    
    label_marker.header.stamp = rclcpp::Time(0);
    label_marker.ns = "poi_labels";
    label_marker.id = std::hash<std::string>{}(poi.id) + 1000;
    label_marker.action = visualization_msgs::msg::Marker::ADD;

    label_control.markers.push_back(label_marker);
    int_marker.controls.push_back(label_control);
  }

  // Add context menu for editing POI properties
  visualization_msgs::msg::MenuEntry edit_name_entry;
  edit_name_entry.id = 1;
  edit_name_entry.parent_id = 0;
  edit_name_entry.title = "Edit Name";
  edit_name_entry.command_type = visualization_msgs::msg::MenuEntry::FEEDBACK;
  int_marker.menu_entries.push_back(edit_name_entry);

  visualization_msgs::msg::MenuEntry edit_type_entry;
  edit_type_entry.id = 2;
  edit_type_entry.parent_id = 0;
  edit_type_entry.title = "Edit Type";
  edit_type_entry.command_type = visualization_msgs::msg::MenuEntry::FEEDBACK;
  int_marker.menu_entries.push_back(edit_type_entry);

  visualization_msgs::msg::MenuEntry delete_entry;
  delete_entry.id = 3;
  delete_entry.parent_id = 0;
  delete_entry.title = "Delete POI";
  delete_entry.command_type = visualization_msgs::msg::MenuEntry::FEEDBACK;
  int_marker.menu_entries.push_back(delete_entry);

  // Add marker to server
  RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), 
              "Creating POI visual - ID: %s, Name: %s, Pos: (%.2f, %.2f, %.2f), Scale: %.2f", 
              poi.id.c_str(), poi.name.c_str(), poi.pose.x, poi.pose.y, poi.pose.theta,
              base_scale);
  
  if (marker_server_) {
    marker_server_->insert(int_marker);
    marker_server_->setCallback(int_marker.name,
                              std::bind(&POIDisplay::handlePOISelection, this, std::placeholders::_1));
    
    marker_server_->applyChanges();
  }

  // Store POI data for reference
  poi_visuals_[poi.id] = poi;
  RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), "POI visual created and stored: %s", poi.id.c_str());
}

void POIDisplay::updatePOIVisual(const cartographer_ros_msgs::msg::POI& poi) {
  auto it = poi_visuals_.find(poi.id);
  if (it != poi_visuals_.end()) {
    marker_server_->erase(poi.id);
    createPOIVisual(poi);
    marker_server_->applyChanges();
  }
}

void POIDisplay::removePOIVisual(const std::string& id) {
  auto it = poi_visuals_.find(id);
  if (it != poi_visuals_.end()) {
    marker_server_->erase(id);
    marker_server_->applyChanges();
  }
}

// Note: Mouse event handling should be implemented in a separate Tool class
// Display classes in RViz2 are not designed to handle mouse events directly
// This method is kept for backward compatibility but should not be used
void POIDisplay::handleMouseEvent(rviz_common::ViewportMouseEvent& event) {
  // Mouse event handling is now properly handled by POITool
  // This function is kept for compatibility but should not be used
  RCLCPP_DEBUG(rclcpp::get_logger("POIDisplay"), 
               "Mouse events should be handled by POITool, not POIDisplay");
}

void POIDisplay::handlePOISelection(
    const std::shared_ptr<const visualization_msgs::msg::InteractiveMarkerFeedback>& feedback) {
  if (feedback->event_type == visualization_msgs::msg::InteractiveMarkerFeedback::POSE_UPDATE) {
    auto it = poi_visuals_.find(feedback->marker_name);
    if (it != poi_visuals_.end()) {
      auto poi = it->second;
      poi.pose.x = feedback->pose.position.x;
      poi.pose.y = feedback->pose.position.y;
      poi.pose.theta = tf2::getYaw(feedback->pose.orientation);

      auto update_request = std::make_shared<cartographer_ros_msgs::srv::UpdatePOI::Request>();
      update_request->poi = poi;

      auto update_future = update_poi_client_->async_send_request(update_request);
      if (update_future.wait_for(std::chrono::milliseconds(2000)) == std::future_status::timeout) {
        RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "Timeout updating POI, operation cancelled");
        return;
      }

      auto update_response = update_future.get();
      if (!update_response->success) {
        RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), "Failed to update POI: %s",
                     update_response->message.c_str());
      }
    }
  } else if (feedback->event_type == visualization_msgs::msg::InteractiveMarkerFeedback::MENU_SELECT) {
    handlePOIMenuSelection(feedback);
  }
}

void POIDisplay::handlePOIMenuSelection(
    const std::shared_ptr<const visualization_msgs::msg::InteractiveMarkerFeedback>& feedback) {
  switch (feedback->menu_entry_id) {
    case 1: // Edit Name
      showPOIEditDialog(feedback->marker_name, "name");
      break;
    case 2: // Edit Type
      showPOIEditDialog(feedback->marker_name, "type");
      break;
    case 3: // Delete POI
      {
        auto request = std::make_shared<cartographer_ros_msgs::srv::RemovePOI::Request>();
        request->id = feedback->marker_name;
        
        if (remove_poi_client_->service_is_ready()) {
          auto future = remove_poi_client_->async_send_request(request);
          RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), "Deleting POI: %s", feedback->marker_name.c_str());
        } else {
          RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "Remove POI service not ready");
        }
      }
      break;
    default:
      RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "Unknown menu entry ID: %d", feedback->menu_entry_id);
      break;
  }
}

void POIDisplay::showPOIEditDialog(const std::string& poi_id, const std::string& field_name) {
  // Get current POI data
  auto it = poi_visuals_.find(poi_id);
  if (it == poi_visuals_.end()) {
    RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), "POI not found: %s", poi_id.c_str());
    return;
  }

  auto poi = it->second;
  QString current_value;
  QString dialog_title;
  
  if (field_name == "name") {
    current_value = QString::fromStdString(poi.name);
    dialog_title = "Edit POI Name";
  } else if (field_name == "type") {
    current_value = QString::fromStdString(poi.type);
    dialog_title = "Edit POI Type";
  } else {
    RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), "Unknown field name: %s", field_name.c_str());
    return;
  }

  bool ok;
  QString new_value = QInputDialog::getText(
    nullptr, dialog_title,
    QString("Enter new %1:").arg(QString::fromStdString(field_name)),
    QLineEdit::Normal, current_value, &ok);

  if (ok && !new_value.isEmpty()) {
    // Update POI data
    if (field_name == "name") {
      poi.name = new_value.toStdString();
    } else if (field_name == "type") {
      poi.type = new_value.toStdString();
    }

    // Send update request
    auto request = std::make_shared<cartographer_ros_msgs::srv::UpdatePOI::Request>();
    request->poi = poi;

    if (update_poi_client_->service_is_ready()) {
      RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), 
                 "Updating POI %s %s to: %s", 
                 poi_id.c_str(), field_name.c_str(), new_value.toStdString().c_str());
      
      update_poi_client_->async_send_request(request,
        [this, poi, field_name, new_value = new_value.toStdString()](
            rclcpp::Client<cartographer_ros_msgs::srv::UpdatePOI>::SharedFuture future) {
          try {
            auto response = future.get();
            if (!response->success) {
              RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), 
                          "Failed to update POI %s: %s", 
                          poi.id.c_str(), response->message.c_str());
            } else {
              RCLCPP_INFO(rclcpp::get_logger("POIDisplay"), 
                         "Successfully updated POI %s %s to: %s", 
                         poi.id.c_str(), field_name.c_str(), new_value.c_str());
              
              poi_visuals_[poi.id] = poi;
              
              if (field_name == "name" && marker_server_) {
                updatePOIVisual(poi);
              }
            }
          } catch (const std::exception& e) {
            RCLCPP_ERROR(rclcpp::get_logger("POIDisplay"), 
                        "Exception while updating POI: %s", e.what());
          }
        });
    } else {
      RCLCPP_WARN(rclcpp::get_logger("POIDisplay"), "Update POI service not ready");
    }
  }
}

}  // namespace cartographer_rviz

#include <pluginlib/class_list_macros.hpp>
PLUGINLIB_EXPORT_CLASS(cartographer_rviz::POIDisplay, rviz_common::Display)











