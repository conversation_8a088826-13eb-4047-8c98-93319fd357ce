#ifndef CARTOGRAPHER_RVIZ_POI_TOOL_H
#define CARTOGRAPHER_RVIZ_POI_TOOL_H

#include <memory>
#include <chrono>
#include <functional>

#include <OgreVector3.h>
#include "rviz_common/tool.hpp"
#include "rclcpp/rclcpp.hpp"
#include "cartographer_ros_msgs/srv/add_poi.hpp"
#include "cartographer_ros_msgs/srv/remove_poi.hpp"
#include "cartographer_ros_msgs/srv/list_poi.hpp"
#include "cartographer_ros_msgs/msg/poi.hpp"

namespace cartographer_rviz {

class POITool : public rviz_common::Tool {
Q_OBJECT

public:
  POITool();
  ~POITool() override;

  void onInitialize() override;

  int processMouseEvent(rviz_common::ViewportMouseEvent& event) override;

  void activate() override;
  void deactivate() override;

private:
  int processMouseClick(rviz_common::ViewportMouseEvent& event);
  int processRightClick(rviz_common::ViewportMouseEvent& event);
  std::string findPOIAtPosition(const Ogre::Vector3& position);
  void findPOIAtPositionAsync(const Ogre::Vector3& position, 
                             std::function<void(const std::string&, const Ogre::Vector3&)> callback);

  // ROS service clients
  rclcpp::Client<cartographer_ros_msgs::srv::AddPOI>::SharedPtr add_poi_client_;
  rclcpp::Client<cartographer_ros_msgs::srv::RemovePOI>::SharedPtr remove_poi_client_;
  rclcpp::Client<cartographer_ros_msgs::srv::ListPOI>::SharedPtr list_pois_client_;
  
  // Cursors
  QCursor std_cursor_;
  QCursor hit_cursor_;
};

}  // namespace cartographer_rviz

#endif  // CARTOGRAPHER_RVIZ_POI_TOOL_H

