<!--
  Copyright 2016 The Cartographer Authors

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<library path="cartographer_rviz">
  <class name="cartographer_rviz/SubmapsDisplay"
         type="cartographer_rviz::SubmapsDisplay"
         base_class_type="rviz_common::Display">
    <description>
      Displays submaps as a unified map in RViz.
      https://github.com/cartographer-project/cartographer_ros
    </description>
  </class>
  <class name="cartographer_rviz/POIDisplay"
         type="cartographer_rviz::POIDisplay"
         base_class_type="rviz_common::Display">
    <description>
      Displays Points of Interest (POIs) in RViz with interactive editing capabilities.
      Supports adding, editing, and managing POIs through mouse interaction and ROS services.
    </description>
  </class>
  <class name="cartographer_rviz/POITool"
         type="cartographer_rviz::POITool"
         base_class_type="rviz_common::Tool">
    <description>
      Tool for adding Points of Interest (POIs) by clicking on the map.
      Press 'P' to activate this tool, then click on the map to add POIs.
    </description>
  </class>
</library>
