<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>slam_toolbox</name>
  <version>2.6.9</version>
  <description>
     This package provides a sped up improved slam karto with updated SDK and visualization and modification toolsets
  </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>LGPL</license>
  <author><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>pluginlib</build_depend>
  <build_depend>eigen</build_depend>
  <build_depend>message_filters</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>rclcpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_sensor_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>std_srvs</build_depend>
  <build_depend>boost</build_depend>
  <build_depend>interactive_markers</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>suitesparse</build_depend>
  <build_depend>liblapack-dev</build_depend>
  <build_depend>libceres-dev</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>
  <build_depend>tbb</build_depend>
  <build_depend>libqt5-core</build_depend>
  <build_depend>libqt5-widgets</build_depend>
  <build_depend>qtbase5-dev</build_depend>
  <build_depend>builtin_interfaces</build_depend>
  <build_depend>rosidl_default_generators</build_depend>
  <depend>rviz_common</depend>
  <depend>rviz_default_plugins</depend>
  <depend>rviz_ogre_vendor</depend>
  <depend>rviz_rendering</depend>
  <depend>interfaces</depend>
  <exec_depend>eigen</exec_depend>
  <exec_depend>pluginlib</exec_depend>
  <exec_depend>message_filters</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>tf2_sensor_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>std_srvs</exec_depend>
  <exec_depend>boost</exec_depend>
  <exec_depend>interactive_markers</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>suitesparse</exec_depend>
  <exec_depend>liblapack-dev</exec_depend>
  <exec_depend>libceres-dev</exec_depend>
  <exec_depend>tf2_geometry_msgs</exec_depend>
  <exec_depend>tbb</exec_depend>
  <exec_depend>libqt5-core</exec_depend>
  <exec_depend>libqt5-widgets</exec_depend>
  <exec_depend>nav2_map_server</exec_depend>
  <exec_depend>builtin_interfaces</exec_depend>
  <exec_depend>rosidl_default_generators</exec_depend>

  <exec_depend>libqt5-core</exec_depend>
  <exec_depend>libqt5-gui</exec_depend>
  <exec_depend>libqt5-opengl</exec_depend>
  <exec_depend>libqt5-widgets</exec_depend>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>ament_cmake_flake8</test_depend>
  <test_depend>ament_cmake_cpplint</test_depend>
  <test_depend>ament_cmake_uncrustify</test_depend>
  <test_depend>ament_lint_auto</test_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
    <slam_toolbox plugin="${prefix}/solver_plugins.xml" />
    <rviz_common plugin="${prefix}/rviz_plugins.xml"/>
  </export>

</package>
