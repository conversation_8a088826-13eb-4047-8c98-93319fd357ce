<library path="ceres_solver_plugin">
  <class 
    name="solver_plugins::CeresSolver" 
    type="solver_plugins::CeresSolver" 
    base_class_type="karto::ScanSolver">
    <description> Ceres Optimizer for Slam Toolbox </description>
  </class>
</library>

<!-- <library path="libGTSAM_solver_plugin">
  <class type="solver_plugins::GTSAMSolver" base_class_type="karto::ScanSolver">
    <description> GTSAM Optimizer for karto </description>
  </class>
</library> -->

<!-- <library path="spa_solver_plugin">
  <class type="solver_plugins::SpaSolver" base_class_type="karto::ScanSolver">
    <description> SPA Optimizer for karto </description>
  </class>
</library> -->

<!-- <library path="g2O_solver_plugin">
  <class type="solver_plugins::G2OSolver" base_class_type="karto::ScanSolver">
    <description> G2O Optimizer for karto </description>
  </class>
</library> -->
