
slam_toolbox:
  ros__parameters:

    # Plugin params
    solver_plugin: solver_plugins::CeresSolver
    ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
    ceres_preconditioner: SCHUR_JACOBI
    ceres_trust_strategy: LEVENBERG_MARQUARDT
    ceres_dogleg_type: TRADITIONAL_DOGLEG
    ceres_loss_function: None

    # ROS Parameters
    odom_frame: odom
    map_frame: map
    base_frame: base_link
    scan_topic: "/fused_scan"
    single_scan_topic: "/fusion_scan"

    use_fusion_scan: false
    use_map_saver: true
    mode: mapping

    # lifelong params
    lifelong_search_use_tree: false
    lifelong_minimum_score: 0.1
    lifelong_iou_match: 0.85
    lifelong_node_removal_score: 0.04
    lifelong_overlap_score_scale: 0.06
    lifelong_constraint_multiplier: 0.08
    lifelong_nearby_penalty: 0.001
    lifelong_candidates_scale: 0.03

    # if you'd like to immediately start continuing a map at a given pose
    # or at the dock, but they are mutually exclusive, if pose is given
    # will use pose
    #map_file_name: test_steve
    #map_start_pose: [0.0, 0.0, 0.0]
    #map_start_at_dock: true

    debug_logging: false
    throttle_scans: 1
    transform_publish_period: 0.02 #if 0 never publishes odometry
    map_update_interval: 0.5
    resolution: 0.05
    min_laser_range: 0.0 #for rastering images
    max_laser_range: 20.0 #for rastering images
    minimum_time_interval: 0.5
    transform_timeout: 0.2
    tf_buffer_duration: 10.
    stack_size_to_use: 40000000 #// program needs a larger stack size to serialize large maps

    # General Parameters
    use_scan_matching: true
    use_scan_barycenter: true
    minimum_travel_distance: 0.2
    minimum_travel_heading: 0.174
    scan_buffer_size: 70
    scan_buffer_maximum_scan_distance: 20.0
    link_match_minimum_response_fine: 0.6  
    link_scan_maximum_distance: 10.0
    loop_search_maximum_distance: 8.0
    do_loop_closing: true 
    loop_match_minimum_chain_size: 10           
    loop_match_maximum_variance_coarse: 0.16 
    loop_match_minimum_response_coarse: 0.3   
    loop_match_minimum_response_fine: 0.8

    # Correlation Parameters - Correlation Parameters
    correlation_search_space_dimension: 0.3
    correlation_search_space_resolution: 0.01
    correlation_search_space_smear_deviation: 0.03 

    # Correlation Parameters - Loop Closure Parameters
    loop_search_space_dimension: 8.0
    loop_search_space_resolution: 0.05
    loop_search_space_smear_deviation: 0.03

    # Scan Matcher Parameters
    distance_variance_penalty: 0.09      
    angle_variance_penalty: 0.1225    

    fine_search_angle_offset: 0.00349     
    coarse_search_angle_offset: 0.52  
    coarse_angle_resolution: 0.0349        
    minimum_angle_penalty: 0.7
    minimum_distance_penalty: 0.4
    use_response_expansion: false
    min_pass_through: 2
    occupancy_threshold: 0.1
