/*
 * slam_toolbox
 * Copyright Work Modifications (c) 2018, Simbe Robotics, Inc.
 * Copyright Work Modifications (c) 2019, <PERSON>
 *
 * THE WORK (AS DEFINED BELOW) IS PROVIDED UNDER THE TERMS OF THIS CREATIVE
 * COMMONS PUBLIC LICENSE ("CCPL" OR "LICENSE"). THE WORK IS PROTECTED BY
 * COPYRIGHT AND/OR OTHER APPLICABLE LAW. ANY USE OF THE WORK OTHER THAN AS
 * AUTHORIZED UNDER THIS LICENSE OR COPYRIGHT LAW IS PROHIBITED.
 *
 * BY EXERCISING ANY RIGHTS TO THE WORK PROVIDED HERE, YOU ACCEPT AND AGREE TO
 * BE BOUND BY THE TERMS OF THIS LICENSE. THE LICENSOR GRANTS YOU THE RIGHTS
 * CONTAINED HERE IN CONSIDERATION OF YOUR ACCEPTANCE OF SUCH TERMS AND
 * CONDITIONS.
 *
 */

/* Author: <PERSON> */

#include <memory>
#include "slam_toolbox/slam_toolbox_async.hpp"

namespace slam_toolbox
{

/*****************************************************************************/
AsynchronousSlamToolbox::AsynchronousSlamToolbox(rclcpp::NodeOptions options)
: SlamToolbox(options)
/*****************************************************************************/
{
}

/*****************************************************************************/
void AsynchronousSlamToolbox::laserCallback(
  interfaces::msg::FusionAndPerScan::ConstSharedPtr scan)
/*****************************************************************************/
{
  // store scan header
  scan_header = scan->header;
  // no odom info
  Pose2 pose;
  if (!pose_helper_->getOdomPose(pose, scan->header.stamp)) {
    RCLCPP_WARN(get_logger(), "Failed to compute odom pose");
    return;
  }

  // ensure the laser can be used
  auto laser_scan_ptr = LaserScanWrapper::toSharedPtr(scan->fused_scan);
  LaserRangeFinder * laser = getLaser(laser_scan_ptr);

  if (!laser) {
    RCLCPP_WARN(get_logger(), "Failed to create laser device for"
      " %s; discarding scan", scan->header.frame_id.c_str());
    return;
  }

  // if not paused, process scan
  if (shouldProcessScan(laser_scan_ptr, pose)) {
    auto front_scan = LaserScanWrapper::toSharedPtr(scan->front_scan);
    auto rear_scan = LaserScanWrapper::toSharedPtr(scan->rear_scan);
    LaserRangeFinder * front_laser = getLaser(front_scan);
    LaserRangeFinder * rear_laser = getLaser(rear_scan);
    mapper_utils::SMapper::AlignedScans aligned_scans(
    LaserScanWrapper::toSharedPtr(scan->front_scan),
    LaserScanWrapper::toSharedPtr(scan->rear_scan),
    front_laser->GetName(),
    rear_laser->GetName(),
    scan->header.stamp);
    smapper_->addAlignedScans(aligned_scans);
    addScan(laser, laser_scan_ptr, pose);
  }
}

/*****************************************************************************/
bool AsynchronousSlamToolbox::deserializePoseGraphCallback(
  const std::shared_ptr<rmw_request_id_t> request_header,
  const std::shared_ptr<slam_toolbox::srv::DeserializePoseGraph::Request> req,
  std::shared_ptr<slam_toolbox::srv::DeserializePoseGraph::Response> resp)
/*****************************************************************************/
{
  if (req->match_type == procType::LOCALIZE_AT_POSE) {
    RCLCPP_WARN(get_logger(), "Requested a localization deserialization "
      "in non-localization mode.");
    return false;
  }

  return SlamToolbox::deserializePoseGraphCallback(request_header, req, resp);
}

}  // namespace slam_toolbox

#include "rclcpp_components/register_node_macro.hpp"

// Register the component with class_loader.
// This acts as a sort of entry point, allowing the component to be discoverable when its library
// is being loaded into a running process.
RCLCPP_COMPONENTS_REGISTER_NODE(slam_toolbox::AsynchronousSlamToolbox)
