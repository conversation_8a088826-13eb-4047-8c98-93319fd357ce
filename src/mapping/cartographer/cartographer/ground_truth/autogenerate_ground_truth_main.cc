/*
 * Copyright 2016 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cmath>
#include <fstream>
#include <string>

#include "cartographer/common/port.h"
#include "cartographer/ground_truth/autogenerate_ground_truth.h"
#include "cartographer/ground_truth/proto/relations.pb.h"
#include "cartographer/io/proto_stream.h"
#include "cartographer/io/proto_stream_deserializer.h"
#include "cartographer/mapping/proto/pose_graph.pb.h"
#include "cartographer/transform/transform.h"
#include "gflags/gflags.h"
#include "glog/logging.h"

DEFINE_string(pose_graph_filename, "",
              "Proto stream file containing the pose graph used to generate "
              "ground truth data.");
DEFINE_string(output_filename, "", "File to write the ground truth proto to.");
DEFINE_double(min_covered_distance, 100.,
              "Minimum covered distance in meters before a loop closure is "
              "considered a candidate for autogenerated ground truth.");
DEFINE_double(outlier_threshold_meters, 0.15,
              "Distance in meters beyond which constraints are considered "
              "outliers.");
DEFINE_double(outlier_threshold_radians, 0.02,
              "Distance in radians beyond which constraints are considered "
              "outliers.");

namespace cartographer {
namespace ground_truth {
namespace {

void Run(const std::string& pose_graph_filename,
         const std::string& output_filename, const double min_covered_distance,
         const double outlier_threshold_meters,
         const double outlier_threshold_radians) {
  LOG(INFO) << "Reading pose graph from '" << pose_graph_filename << "'...";
  mapping::proto::PoseGraph pose_graph =
      io::DeserializePoseGraphFromFile(pose_graph_filename);

  LOG(INFO) << "Autogenerating ground truth relations...";
  const proto::GroundTruth ground_truth =
      GenerateGroundTruth(pose_graph, min_covered_distance,
                          outlier_threshold_meters, outlier_threshold_radians);
  LOG(INFO) << "Writing " << ground_truth.relation_size() << " relations to '"
            << output_filename << "'.";
  {
    std::ofstream output_stream(output_filename,
                                std::ios_base::out | std::ios_base::binary);
    CHECK(ground_truth.SerializeToOstream(&output_stream))
        << "Could not serialize ground truth data.";
    output_stream.close();
    CHECK(output_stream) << "Could not write ground truth data.";
  }
}

}  // namespace
}  // namespace ground_truth
}  // namespace cartographer

int main(int argc, char** argv) {
  google::InitGoogleLogging(argv[0]);
  FLAGS_logtostderr = true;
  google::SetUsageMessage(
      "\n\n"
      "This program semi-automatically generates ground truth data from a\n"
      "pose graph proto.\n"
      "\n"
      "The input should contain a single trajectory and should have been\n"
      "manually assessed to be correctly loop closed. Small local distortions\n"
      "are acceptable if they are tiny compared to the errors we want to\n"
      "assess using the generated ground truth data.\n"
      "\n"
      "All loop closure constraints separated by long covered distance are\n"
      "included in the output. Outliers are removed.\n");
  google::ParseCommandLineFlags(&argc, &argv, true);

  if (FLAGS_pose_graph_filename.empty() || FLAGS_output_filename.empty()) {
    google::ShowUsageWithFlagsRestrict(argv[0], "autogenerate_ground_truth");
    return EXIT_FAILURE;
  }
  ::cartographer::ground_truth::Run(
      FLAGS_pose_graph_filename, FLAGS_output_filename,
      FLAGS_min_covered_distance, FLAGS_outlier_threshold_meters,
      FLAGS_outlier_threshold_radians);
}
