// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "cartographer/mapping/proto/cell_limits_2d.proto";
import "cartographer/transform/proto/transform.proto";

package cartographer.mapping.proto;

message MapLimits {
  double resolution = 1;
  cartographer.transform.proto.Vector2d max = 2;
  CellLimits cell_limits = 3;
}
