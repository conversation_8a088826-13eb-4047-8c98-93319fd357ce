// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

message ProbabilityGridRangeDataInserterOptions2D {
  // Probability change for a hit (this will be converted to odds and therefore
  // must be greater than 0.5).
  double hit_probability = 1;

  // Probability change for a miss (this will be converted to odds and therefore
  // must be less than 0.5).
  double miss_probability = 2;

  // If 'false', free space will not change the probabilities in the occupancy
  // grid.
  bool insert_free_space = 3;
}
