// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.scan_matching.proto;

import "cartographer/common/proto/ceres_solver_options.proto";

message IntensityCostFunctionOptions {
  double weight = 1;
  double huber_scale = 2;
  // Ignore ranges with intensity above this threshold.
  float intensity_threshold = 3;
}

// NEXT ID: 8
message CeresScanMatcherOptions3D {
  // Scaling parameters for each occupied space cost functor.
  repeated double occupied_space_weight = 1;
  double translation_weight = 2;
  double rotation_weight = 3;

  // Whether only to allow changes to yaw, keeping roll/pitch constant.
  bool only_optimize_yaw = 5;

  // Configure the Ceres solver. See the Ceres documentation for more
  // information: https://code.google.com/p/ceres-solver/
  common.proto.CeresSolverOptions ceres_solver_options = 6;

  // Scaling parameters for each intensity cost functor.
  repeated IntensityCostFunctionOptions intensity_cost_function_options = 7;
}
