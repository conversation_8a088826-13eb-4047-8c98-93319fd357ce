// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "cartographer/mapping/proto/pose_graph_options.proto";

package cartographer.mapping.proto;

message MapBuilderOptions {
  bool use_trajectory_builder_2d = 1;
  bool use_trajectory_builder_3d = 2;

  // Number of threads to use for background computations.
  int32 num_background_threads = 3;
  PoseGraphOptions pose_graph_options = 4;
  // Sort sensor input independently for each trajectory.
  bool collate_by_trajectory = 5;
}
