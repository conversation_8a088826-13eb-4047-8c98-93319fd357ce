/*
 * Copyright 2018 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef CARTOGRAPHER_METRICS_REGISTER_H_
#define CARTOGRAPHER_METRICS_REGISTER_H_

#include "cartographer/metrics/family_factory.h"

namespace cartographer {
namespace metrics {

void RegisterAllMetrics(FamilyFactory *registry);

}  // namespace metrics
}  // namespace cartographer

#endif  // CARTOGRAPHER_METRICS_REGISTER_H_
