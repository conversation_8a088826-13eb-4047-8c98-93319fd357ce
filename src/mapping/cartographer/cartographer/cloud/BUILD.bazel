# Copyright 2018 The Cartographer Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Top-level proto and C++ targets for Cartographer's gRPC server.

licenses(["notice"])  # Apache 2.0

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "protos",
    srcs = glob(
        [
            "**/*.proto",
        ],
    ),
    deps = [
        "//cartographer:protos",
        "@com_google_protobuf//:empty_proto",
    ],
)

cc_proto_library(
    name = "cc_protos",
    deps = [":protos"],
)

# TODO(rodrigoq): This special rule name is required by cc_grpc_library. This
# makes :protos look like it was created by
#   cc_grpc_library(proto_only=True, ...)
proto_library(
    name = "_cc_protos_only",
    deps = [
        ":protos",
        "//cartographer:protos",
        "@com_google_protobuf//:empty_proto",
    ],
)

cc_library(
    name = "cartographer_grpc",
    srcs = glob(
        [
            "**/*.cc",
        ],
        exclude = [
            "**/*_main.cc",
            "**/*_test.cc",
        ],
    ),
    hdrs = glob([
        "**/*.h",
    ]),
    copts = ["-Wno-sign-compare"],
    defines = ["USE_PROMETHEUS=1"],
    includes = ["."],
    deps = [
        ":cc_protos",
        "//cartographer",
        "@com_github_googlecartographer_async_grpc//async_grpc",
        "@com_github_grpc_grpc//:grpc++",
        "@com_github_jupp0r_prometheus_cpp//core",
        "@com_github_jupp0r_prometheus_cpp//pull",
        "@com_google_glog//:glog",
        "@com_google_protobuf//:cc_wkt_protos",
    ],
)

cc_binary(
    name = "cartographer_grpc_server",
    srcs = ["map_builder_server_main.cc"],
    deps = [
        ":cartographer_grpc",
        "@com_github_gflags_gflags//:gflags",
        "@com_google_glog//:glog",
    ],
)

[cc_test(
    name = src.replace("/", "_").replace(".cc", ""),
    srcs = [src],
    data = ["//:configuration_files"],
    # TODO(gaschler): Fix UplinkServerRestarting test for Bazel.
    args = ["--gtest_filter=-ClientServerTestByGridType/ClientServerTestByGridType.LocalSlam2DUplinkServerRestarting*"],
    flaky = True,  # :internal_client_server_test sometimes fails.
    # Tests cannot run concurrently as some of them open the same port.
    tags = ["exclusive"],
    deps = [
        ":cartographer_grpc",
        "//cartographer:cartographer_test_library",
        "@com_google_googletest//:gtest_main",
    ],
) for src in glob(
    ["**/*_test.cc"],
)]
