// Copyright 2017 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "cartographer/mapping/proto/map_builder_options.proto";

package cartographer.cloud.proto;

message MapBuilderServerOptions {
  string server_address = 1;
  int32 num_grpc_threads = 2;
  int32 num_event_threads = 3;
  cartographer.mapping.proto.MapBuilderOptions map_builder_options = 4;
  string uplink_server_address = 5;
  int32 upload_batch_size = 6;
  bool enable_ssl_encryption = 7;
  bool enable_google_auth = 9;
}
