# Copyright 2018 The Cartographer Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Do an optimized build by default, or otherwise <PERSON><PERSON><PERSON> cannot run
# real-time and certain tests will timeout.
build -c opt

# By default, an optimized C++ build with <PERSON><PERSON> will build each library twice,
# with and without -fPIC. --force_pic avoids the unnecessary actions and
# reduces build time.
build --force_pic

