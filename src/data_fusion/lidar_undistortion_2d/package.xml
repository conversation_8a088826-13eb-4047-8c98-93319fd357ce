<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>lidar_undistortion_2d</name>
  <version>0.0.0</version>
  <description>The lidar_undistortion_2d package for ROS2</description>
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>TODO</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>sensor_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>angles</depend>
  <depend>libpcl-all-dev</depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
