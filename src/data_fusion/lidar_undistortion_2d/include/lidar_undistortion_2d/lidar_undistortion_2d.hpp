#pragma once
#include <angles/angles.h>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <tf2/utils.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <vector>
#include <string>
#include <map>

using namespace std::chrono_literals;

struct LidarConfig {
  std::string sub_topic;
  std::string pub_topic;
  std::string frame;
  double scan_time_gain;
  rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr sub;
  rclcpp::Publisher<sensor_msgs::msg::LaserScan>::SharedPtr pub;
};

class LidarMotionCalibrator : public rclcpp::Node {
  public:
    explicit LidarMotionCalibrator(const rclcpp::NodeOptions &options = rclcpp::NodeOptions());
    ~LidarMotionCalibrator();

  private:
    void ScanCallBack(const sensor_msgs::msg::LaserScan::SharedPtr scan_msg, const size_t lidar_index);

    bool getLaserPose(geometry_msgs::msg::PoseStamped &odom_pose, rclcpp::Time dt, const std::string& lidar_frame);

    void publishScan(const sensor_msgs::msg::LaserScan::SharedPtr scan_msg,
                     const std::vector<float> &ranges,
                     const std::vector<double> &angles,
                     const std::vector<float> &intensities,
                     const size_t lidar_index);

    bool Lidar_Calibration(std::vector<float> &ranges,
                           std::vector<double> &angles, rclcpp::Time startTime,
                           rclcpp::Time endTime,
                           const std::string& lidar_frame);

    void Lidar_MotionCalibration(geometry_msgs::msg::PoseStamped frame_base_pose,
                            geometry_msgs::msg::PoseStamped frame_start_pose,
                            geometry_msgs::msg::PoseStamped frame_end_pose,
                            std::vector<float> &ranges,
                            std::vector<double> &angles, int startIndex,
                            int &beam_number);

    void visual_cloud_scan(const std::vector<float> &ranges_,
                           const std::vector<double> &angles_, unsigned char r_,
                           unsigned char g_, unsigned char b_);

    std::string odom_frame_;
    std::vector<LidarConfig> lidar_configs_;

    std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
    std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
};