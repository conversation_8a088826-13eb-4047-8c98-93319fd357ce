#include "lidar_undistortion_2d/lidar_undistortion_2d.hpp"

LidarMotionCalibrator::LidarMotionCalibrator(const rclcpp::NodeOptions &options)
: Node("lidar_undistortion_2d_node", options) 
{
    this->declare_parameter("odom_frame", "odom"); 
    this->declare_parameter("lidar_count", 1);
    
    odom_frame_ = this->get_parameter("odom_frame").as_string();
    int lidar_count = this->get_parameter("lidar_count").as_int();
    
    tf_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
    tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
    
    for (int i = 0; i < lidar_count; i++) {
        std::string prefix = "lidar" + std::to_string(i) + ".";
        
        this->declare_parameter(prefix + "scan_sub_topic", "/scan" + std::to_string(i));
        this->declare_parameter(prefix + "scan_pub_topic", "/scan_undistortion" + std::to_string(i));
        this->declare_parameter(prefix + "lidar_frame", "laser" + std::to_string(i));
        this->declare_parameter(prefix + "lidar_scan_time_gain", 1.0);
        
        LidarConfig config;
        config.sub_topic = this->get_parameter(prefix + "scan_sub_topic").as_string();
        config.pub_topic = this->get_parameter(prefix + "scan_pub_topic").as_string();
        config.frame = this->get_parameter(prefix + "lidar_frame").as_string();
        config.scan_time_gain = this->get_parameter(prefix + "lidar_scan_time_gain").as_double();
        
        config.sub = this->create_subscription<sensor_msgs::msg::LaserScan>(
            config.sub_topic, 10,
            [this, i](const sensor_msgs::msg::LaserScan::SharedPtr msg) {
                this->ScanCallBack(msg, i);
            });
            
        config.pub = this->create_publisher<sensor_msgs::msg::LaserScan>(
            config.pub_topic, 10);
            
        lidar_configs_.push_back(config);
        
        RCLCPP_INFO(this->get_logger(), "Initialized lidar %d: sub=%s, pub=%s, frame=%s", 
                   i, config.sub_topic.c_str(), config.pub_topic.c_str(), config.frame.c_str());
    }

    RCLCPP_INFO(this->get_logger(), "Lidar undistortion node initialized with %d lidars", lidar_count);
}

LidarMotionCalibrator::~LidarMotionCalibrator() {}

void LidarMotionCalibrator::ScanCallBack(
    const sensor_msgs::msg::LaserScan::SharedPtr scan_msg, const size_t lidar_index) 
{
    if (lidar_index >= lidar_configs_.size()) {
        RCLCPP_ERROR(this->get_logger(), "Invalid lidar index: %zu", lidar_index);
        return;
    }
    
    const LidarConfig& config = lidar_configs_[lidar_index];
    
    rclcpp::Time startTime = scan_msg->header.stamp;

    int beamNum = scan_msg->ranges.size();
    rclcpp::Time endTime = startTime + rclcpp::Duration::from_seconds(
                                           scan_msg->time_increment *
                                           config.scan_time_gain * beamNum);

    std::vector<double> angles;
    angles.reserve(beamNum);
    std::vector<float> ranges;
    ranges.reserve(beamNum);
    std::vector<float> intensities;
    intensities.reserve(beamNum);

    for (int alpha = 0; alpha < beamNum; alpha++) 
    {
        double lidar_dist = scan_msg->ranges[alpha];

        if (std::isnan(lidar_dist) || std::isinf(lidar_dist) || lidar_dist < scan_msg->range_min || lidar_dist > scan_msg->range_max)
            lidar_dist = 0.0;

        intensities.push_back(scan_msg->intensities[alpha]);
        ranges.push_back(lidar_dist);
        angles.push_back(scan_msg->angle_min + scan_msg->angle_increment * alpha);
    }

    if(!Lidar_Calibration(ranges, angles, startTime, endTime, config.frame))
    {
        RCLCPP_WARN(this->get_logger(), "Lidar %zu Calibration Failed, Publish the raw lidar data", lidar_index);
        publishScan(scan_msg, ranges, angles, intensities, lidar_index);
        return;
    }

    publishScan(scan_msg, ranges, angles, intensities, lidar_index);
}

void LidarMotionCalibrator::publishScan(
    const sensor_msgs::msg::LaserScan::SharedPtr scan_msg,
    const std::vector<float> &ranges, const std::vector<double> &angles,
    const std::vector<float> &intensities,
    const size_t lidar_index) 
{
    if (lidar_index >= lidar_configs_.size()) {
        RCLCPP_ERROR(this->get_logger(), "Invalid lidar index: %zu", lidar_index);
        return;
    }
    
    sensor_msgs::msg::LaserScan scan_undistorted = *scan_msg;
    scan_undistorted.ranges.assign(scan_msg->ranges.size(), 0.0);
    scan_undistorted.intensities.assign(scan_msg->intensities.size(), 0.0);

    for (size_t i = 0; i < ranges.size(); i++) 
    {
        double angle = angles[i];

        int k = round((angle - scan_msg->angle_min) / scan_msg->angle_increment);

        if (k >= 0 && k < scan_msg->ranges.size()) 
        {
            scan_undistorted.ranges[k] = ranges[i];
            scan_undistorted.intensities[k] = intensities[i];
        }
    }

    lidar_configs_[lidar_index].pub->publish(scan_undistorted);
}

bool LidarMotionCalibrator::getLaserPose(
    geometry_msgs::msg::PoseStamped &odom_pose, rclcpp::Time dt, const std::string& lidar_frame) 
{
    geometry_msgs::msg::TransformStamped transform;

    try 
    {
        if (!tf_buffer_->canTransform(odom_frame_, lidar_frame, dt, 50ms)) 
        {
            RCLCPP_WARN(this->get_logger(), "TF not available from %s to %s at time %f", 
                        odom_frame_.c_str(), lidar_frame.c_str(), dt.seconds());
            return false;
        }

        transform = tf_buffer_->lookupTransform(odom_frame_, lidar_frame, dt);

        odom_pose.header.stamp = dt;
        odom_pose.header.frame_id = odom_frame_;

        odom_pose.pose.position.x = transform.transform.translation.x;
        odom_pose.pose.position.y = transform.transform.translation.y;
        odom_pose.pose.position.z = transform.transform.translation.z;

        odom_pose.pose.orientation = transform.transform.rotation;

        return true;
    } 
    catch (tf2::TransformException &ex) 
    {
        RCLCPP_ERROR(this->get_logger(), "Transform error: %s to %s: %s", 
                    odom_frame_.c_str(), lidar_frame.c_str(), ex.what());
        return false;
    }
}

bool LidarMotionCalibrator::Lidar_Calibration(std::vector<float> &ranges,
                                              std::vector<double> &angles,
                                              rclcpp::Time startTime,
                                              rclcpp::Time endTime,
                                              const std::string& lidar_frame) 
{
    int beamNumber = ranges.size();
    if (beamNumber != angles.size()) 
    {
        RCLCPP_ERROR(this->get_logger(), "Error:ranges not match to the angles");
        return false;
    }

    int interpolation_time_duration = 5 * 1000; //5ms

    geometry_msgs::msg::PoseStamped frame_start_pose;
    geometry_msgs::msg::PoseStamped frame_mid_pose;
    geometry_msgs::msg::PoseStamped frame_base_pose;
    geometry_msgs::msg::PoseStamped frame_end_pose;

    int64_t start_time_us = startTime.nanoseconds() / 1000;
    int64_t end_time_us = endTime.nanoseconds() / 1000;
    double time_inc = static_cast<double>(end_time_us - start_time_us) /
                      beamNumber;

    int start_index = 0;

    if (!getLaserPose(frame_start_pose, startTime, lidar_frame)) 
    {
        RCLCPP_WARN(this->get_logger(), "Can not get Start Pose for frame %s, Can not compensate distortion", lidar_frame.c_str());
        return false;
    }

    if (!getLaserPose(frame_end_pose, endTime, lidar_frame)) 
    {
        RCLCPP_WARN(this->get_logger(), "Can not get End Pose for frame %s, Can not compensate distortion", lidar_frame.c_str());
        return false;
    }

    int cnt = 0;
    frame_base_pose = frame_start_pose;

    for (int i = 0; i < beamNumber; i++) 
    {
        double mid_time_us = start_time_us + time_inc * (i - start_index);

        if (mid_time_us - start_time_us > interpolation_time_duration || (i == beamNumber - 1)) 
        {
            cnt++;

            rclcpp::Time mid_time =
                rclcpp::Time(mid_time_us * 1000);

            if (!getLaserPose(frame_mid_pose, mid_time, lidar_frame)) 
            {
                RCLCPP_WARN(this->get_logger(), "Can not get Mid %d Pose for frame %s, Can not compensate distortion", cnt, lidar_frame.c_str());
                return false;
            }

            int interp_count = i - start_index + 1;

            Lidar_MotionCalibration(
                frame_base_pose, 
                frame_start_pose,
                frame_mid_pose,   
                ranges,           
                angles,           
                start_index,      
                interp_count);    

            start_time_us = mid_time_us;
            start_index = i + 1;
            frame_start_pose = frame_mid_pose;
        }
    }
    return true;
}

void LidarMotionCalibrator::Lidar_MotionCalibration(
    geometry_msgs::msg::PoseStamped frame_base_pose,
    geometry_msgs::msg::PoseStamped frame_start_pose,
    geometry_msgs::msg::PoseStamped frame_end_pose, 
    std::vector<float> &ranges,
    std::vector<double> &angles, 
    int startIndex, 
    int &beam_number) 
{
    const double beam_step = 1.0 / (beam_number - 1);

    double base_angle = tf2::getYaw(frame_base_pose.pose.orientation);
    double start_angle = tf2::getYaw(frame_start_pose.pose.orientation);
    double end_angle = tf2::getYaw(frame_end_pose.pose.orientation);

    tf2::Quaternion base_quaternion, start_quaternion, end_quaternion,
        cur_quaternion;
    tf2::fromMsg(frame_base_pose.pose.orientation, base_quaternion);
    tf2::fromMsg(frame_start_pose.pose.orientation, start_quaternion);
    tf2::fromMsg(frame_end_pose.pose.orientation, end_quaternion);

    tf2::Vector3 base_point(frame_base_pose.pose.position.x,
                            frame_base_pose.pose.position.y, 0.0);

    tf2::Vector3 start_point(frame_start_pose.pose.position.x,
                             frame_start_pose.pose.position.y, 0.0);

    tf2::Vector3 end_point(frame_end_pose.pose.position.x,
                           frame_end_pose.pose.position.y, 0.0);

    tf2::Transform base_transform;
    base_transform.setOrigin(base_point);
    base_transform.setRotation(base_quaternion);

    for (int index = 0; index < beam_number; ++index) 
    {
        const double lidar_range = ranges[startIndex + index];
        const double lidar_angle = angles[startIndex + index];

        tf2::Transform frame_cur_pose;
        cur_quaternion = start_quaternion.slerp(end_quaternion, index * beam_step);
        tf2::Vector3 cur_point = start_point.lerp(end_point, index * beam_step);

        frame_cur_pose.setOrigin(cur_point);
        frame_cur_pose.setRotation(cur_quaternion);

        if (lidar_range > 0.001 && !std::isinf(lidar_range)) 
        {
            tf2::Vector3 lidar_point(lidar_range * cos(lidar_angle), lidar_range * sin(lidar_angle), 0.0);
            tf2::Vector3 corrected_lidar_point = (base_transform.inverse() * frame_cur_pose) * lidar_point;

            ranges[startIndex + index] = hypot(corrected_lidar_point.x(), corrected_lidar_point.y());
            angles[startIndex + index] = atan2(corrected_lidar_point.y(), corrected_lidar_point.x());
        } 
        else 
        {
            double tmp_angle = tf2::getYaw(cur_quaternion) + lidar_angle;
            tmp_angle = angles::normalize_angle(tmp_angle);

            angles[startIndex + index] = angles::normalize_angle(tmp_angle - base_angle);
        }
    }
}

int main(int argc, char *argv[]) 
{
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<LidarMotionCalibrator>());
    rclcpp::shutdown();
    return 0;
}