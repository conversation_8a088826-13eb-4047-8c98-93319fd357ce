#include "multi_lidar_data_sync/multi_lidar_data_sync.hpp"
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h>

MultiLidarDataSync::MultiLidarDataSync(const rclcpp::NodeOptions &options)
: Node("multi_lidar_data_sync_node", options) 
{
  this->declare_parameter("main_scan_topic", "/main_scan");
  this->declare_parameter("sub_scan_topic", "/sub_scan1");
  this->declare_parameter("scan_pub_topic", "/fusion_scan");
  this->declare_parameter("main_lidar_frame", "main_laser");
  this->declare_parameter("sub_lidar_frame", "sub_laser1");
  this->declare_parameter("odom_frame", "odom");
  this->declare_parameter("base_frame", "base_link");
  this->declare_parameter("lidar_scan_time_gain", 1.0);
  this->declare_parameter("max_sub_lidars", 2);
  this->declare_parameter("extrinsic_matrices", std::vector<double>(16, 0.0));

  main_scan_topic_ = this->get_parameter("main_scan_topic").as_string();
  sub_scan_topic_ = this->get_parameter("sub_scan_topic").as_string();
  scan_pub_topic_ = this->get_parameter("scan_pub_topic").as_string();
  
  main_lidar_frame_ = this->get_parameter("main_lidar_frame").as_string();
  sub_lidar_frame_ = this->get_parameter("sub_lidar_frame").as_string();
  odom_frame_ = this->get_parameter("odom_frame").as_string();
  base_frame_ = this->get_parameter("base_frame").as_string();
  lidar_scan_time_gain_ = this->get_parameter("lidar_scan_time_gain").as_double();
  max_sub_lidars_ = this->get_parameter("max_sub_lidars").as_int();

  tf_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
  tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);

  scan_pub_ = this->create_publisher<sensor_msgs::msg::LaserScan>(scan_pub_topic_, 10);

  main_scan_sub_ = std::make_shared<message_filters::Subscriber<sensor_msgs::msg::LaserScan>>(this, main_scan_topic_);
  sub_scan_sub_= std::make_shared<message_filters::Subscriber<sensor_msgs::msg::LaserScan>>(this, sub_scan_topic_);

  sync_ = std::make_shared<message_filters::Synchronizer<MySyncPolicy>>(
    MySyncPolicy(10), *main_scan_sub_, *sub_scan_sub_);

  sync_->registerCallback(
      std::bind(&MultiLidarDataSync::SyncCallback, this, 
                std::placeholders::_1, std::placeholders::_2));

  RCLCPP_INFO(this->get_logger(), "Multi-lidar data synchronization node initialized");
  RCLCPP_INFO(this->get_logger(), "Main lidar topic: %s, frame: %s", main_scan_topic_.c_str(), main_lidar_frame_.c_str());
  RCLCPP_INFO(this->get_logger(), "Sub-lidar topic: %s, frame: %s", sub_scan_topic_.c_str(), sub_lidar_frame_.c_str());
  RCLCPP_INFO(this->get_logger(), "Scan publisher topic: %s", scan_pub_topic_.c_str());
}

MultiLidarDataSync::~MultiLidarDataSync() {}

void MultiLidarDataSync::transformLaserToPose(const sensor_msgs::msg::LaserScan::ConstSharedPtr& scan,
                                              pcl::PointCloud<pcl::PointXYZI> &cloud)
{
    cloud.reserve(scan->ranges.size());
    for (size_t i = 0; i < scan->ranges.size(); ++i) {
      float range = scan->ranges[i];
      if (std::isnan(range) || range < scan->range_min || range > scan->range_max || std::isinf(range)) {
        range = std::numeric_limits<float>::infinity();
        continue;
      }
      
      float angle = scan->angle_min + i * scan->angle_increment;
      pcl::PointXYZI point;
      point.x = range * cos(angle);
      point.y = range * sin(angle);
      point.z = 0.0;
      point.intensity = scan->intensities[i];
      cloud.push_back(point);
    }
}

void MultiLidarDataSync::SyncCallback(
    const sensor_msgs::msg::LaserScan::ConstSharedPtr& main_scan,
    const sensor_msgs::msg::LaserScan::ConstSharedPtr& sub_scan1) 
{
  RCLCPP_DEBUG(this->get_logger(), "Received synchronized laser scans");
  
  std::vector<pcl::PointCloud<pcl::PointXYZI>> all_clouds;
  all_clouds.reserve(2);
  
  rclcpp::Time main_time = main_scan->header.stamp;

  pcl::PointCloud<pcl::PointXYZI> main_cloud;
  transformLaserToPose(main_scan, main_cloud);
  all_clouds.push_back(main_cloud);

  if (sub_scan1) {
    pcl::PointCloud<pcl::PointXYZI> sub1_cloud;
    transformLaserToPose(sub_scan1, sub1_cloud);
    pcl::PointCloud<pcl::PointXYZI> sub1_cloud_in_main_frame;
    transformLaserToMainFrameWithOdom(sub1_cloud, sub1_cloud_in_main_frame, sub_scan1->header.stamp, main_time);
    all_clouds.push_back(sub1_cloud_in_main_frame);
  }
  
  mergeLaserScans(main_scan, all_clouds);
  auto now = this->now();
  auto diff = now - main_scan->header.stamp;
  if (diff.seconds() > 0.2) {
    RCLCPP_WARN(this->get_logger(), "Fusion scan is delayed, now:%f, main scan:%d.%d, sub scan:%d.%d",
      now.seconds(), main_scan->header.stamp.sec, main_scan->header.stamp.nanosec, 
      sub_scan1->header.stamp.sec, sub_scan1->header.stamp.nanosec);
  }
}

void MultiLidarDataSync::transformLaserToMainFrameWithOdom(
                                    const pcl::PointCloud<pcl::PointXYZI> &sub_cloud,
                                    pcl::PointCloud<pcl::PointXYZI> &transformed_cloud,
                                    const rclcpp::Time& sub_time,
                                    const rclcpp::Time& main_time)
{ 
    Eigen::Matrix4f extrinsic_matrix = Eigen::Matrix4f::Identity();
    try
    {
        auto tmp_extrinsic_matrix = tf_buffer_->lookupTransform(main_lidar_frame_, sub_lidar_frame_, tf2::TimePointZero);
        extrinsic_matrix = transformStampedToEigenMatrix(tmp_extrinsic_matrix);
    }
    catch (const tf2::TransformException &ex)
    {
        RCLCPP_WARN(this->get_logger(), "Could not transform: %s", ex.what());
    }
    pcl::PointCloud<pcl::PointXYZI> sub_cloud_in_main_frame;
    pcl::transformPointCloud(sub_cloud, sub_cloud_in_main_frame, extrinsic_matrix);
  
    Eigen::Matrix4f sub_pose = Eigen::Matrix4f::Identity();
    if (!getLaserPose(sub_pose, sub_time, main_lidar_frame_)) {
        RCLCPP_WARN(this->get_logger(), "Cannot get sub-lidar pose at its timestamp, using original scan data");
        transformed_cloud = sub_cloud_in_main_frame;
        return;
    }

    Eigen::Matrix4f main_pose = Eigen::Matrix4f::Identity();
    if (!getLaserPose(main_pose, main_time, main_lidar_frame_)) {
        RCLCPP_WARN(this->get_logger(), "Cannot get main lidar pose at target timestamp, using original scan data");
        transformed_cloud = sub_cloud_in_main_frame;
        return;
    }

    Eigen::Matrix4f total_transform;
    total_transform = main_pose.inverse() * sub_pose;
    pcl::transformPointCloud(sub_cloud_in_main_frame, transformed_cloud, total_transform);
}

bool MultiLidarDataSync::getLaserPose(
    Eigen::Matrix4f &odom_pose, rclcpp::Time dt, const std::string& lidar_frame) 
{
  geometry_msgs::msg::TransformStamped transform;

  try 
  {
    // // 等待变换可用
    // if (!tf_buffer_->canTransform(odom_frame_, lidar_frame, dt, 50ms)) 
    // {
    //   RCLCPP_INFO(this->get_logger(), "LidarMotion-Can not Wait Transform() for frame %s", lidar_frame.c_str());
    //   return false;
    // }

    transform = tf_buffer_->lookupTransform(odom_frame_, lidar_frame, dt);
    odom_pose = transformStampedToEigenMatrix(transform);
    return true;
  } 
  catch (tf2::TransformException &ex) 
  {
    RCLCPP_ERROR(this->get_logger(), "LidarMotion: Transform error for frame %s: %s", 
        lidar_frame.c_str(), ex.what());
    return false;
  }
}

bool MultiLidarDataSync::mergeLaserScans(
    const sensor_msgs::msg::LaserScan::ConstSharedPtr main_scan,
    const std::vector<pcl::PointCloud<pcl::PointXYZI>> &all_clouds)
{
    sensor_msgs::msg::LaserScan merged_scan = *main_scan;
    merged_scan.header.frame_id = base_frame_;

    rclcpp::Time startTime = main_scan->header.stamp;
    int beamNum = main_scan->ranges.size();
    rclcpp::Time endTime = startTime + rclcpp::Duration::from_seconds(
                                            main_scan->time_increment *
                                            lidar_scan_time_gain_ * beamNum);
    merged_scan.header.stamp = endTime;

    for (size_t i = 0; i < merged_scan.ranges.size(); ++i) {
        merged_scan.ranges[i] = std::numeric_limits<float>::infinity();
        merged_scan.intensities[i] = 0.0;
    }

    pcl::PointCloud<pcl::PointXYZI> wait_merge_cloud;
    for (const auto& cloud : all_clouds) {
        wait_merge_cloud += cloud;
    }

    Eigen::Matrix4f start_pose = Eigen::Matrix4f::Identity();
    if (!getLaserPose(start_pose, startTime, main_lidar_frame_)) {
        RCLCPP_WARN(this->get_logger(), "Cannot get start lidar pose at its timestamp, using original scan data");
        start_pose = Eigen::Matrix4f::Identity();
    }

    Eigen::Matrix4f end_pose = Eigen::Matrix4f::Identity();
    if (!getLaserPose(end_pose, endTime, main_lidar_frame_)) {
        RCLCPP_WARN(this->get_logger(), "Cannot get end lidar pose at target timestamp, using original scan data");
        end_pose = Eigen::Matrix4f::Identity();
    }

    Eigen::Matrix4f timestamp_transform;
    timestamp_transform = end_pose.inverse() * start_pose;

    try {
        geometry_msgs::msg::TransformStamped laser_to_base_transform = 
            tf_buffer_->lookupTransform(base_frame_, main_lidar_frame_, merged_scan.header.stamp);

        Eigen::Matrix4f transform_matrix = Eigen::Matrix4f::Identity();
        transform_matrix = transformStampedToEigenMatrix(laser_to_base_transform);

        Eigen::Matrix4f total_transform;
        total_transform = transform_matrix * timestamp_transform;

        pcl::PointCloud<pcl::PointXYZI> transformed_cloud;
        pcl::transformPointCloud(wait_merge_cloud, transformed_cloud, total_transform);

        for (const auto& point : transformed_cloud) {
            float range = hypot(point.x, point.y);
            double angle = atan2(point.y, point.x);
            
            while (angle < merged_scan.angle_min) angle += 2 * M_PI;
            while (angle > merged_scan.angle_max) angle -= 2 * M_PI;
            
            if (angle < merged_scan.angle_min || angle > merged_scan.angle_max) {
                continue;
            }
            
            int index = static_cast<int>((angle - merged_scan.angle_min) / merged_scan.angle_increment);
            
            if (index >= 0 && index < static_cast<int>(merged_scan.ranges.size())) {
                if (range < merged_scan.ranges[index]) {
                    merged_scan.ranges[index] = range;
                    merged_scan.intensities[index] = point.intensity;
                }
            }
        }

        scan_pub_->publish(merged_scan);
        return true;
    }
    catch (tf2::TransformException &ex) {
        RCLCPP_ERROR(this->get_logger(), "Transform error to base_link: %s", ex.what());
        return false;
    }
}

Eigen::Matrix4f MultiLidarDataSync::transformStampedToEigenMatrix(const geometry_msgs::msg::TransformStamped& transform)
{
    Eigen::Matrix4f transform_matrix = Eigen::Matrix4f::Identity();

    tf2::Quaternion q(
        transform.transform.rotation.x,
        transform.transform.rotation.y,
        transform.transform.rotation.z,
        transform.transform.rotation.w
    );

    tf2::Matrix3x3 m(q);
    double roll, pitch, yaw;
    m.getRPY(roll, pitch, yaw);

    Eigen::Matrix3f rotation_matrix;
    rotation_matrix = Eigen::AngleAxisf(yaw, Eigen::Vector3f::UnitZ()) *
                      Eigen::AngleAxisf(pitch, Eigen::Vector3f::UnitY()) *
                      Eigen::AngleAxisf(roll, Eigen::Vector3f::UnitX());

    transform_matrix.block<3, 3>(0, 0) = rotation_matrix;

    transform_matrix(0, 3) = transform.transform.translation.x;
    transform_matrix(1, 3) = transform.transform.translation.y;
    transform_matrix(2, 3) = transform.transform.translation.z;

    return transform_matrix;
}

int main(int argc, char *argv[]) 
{
  rclcpp::init(argc, argv);
  rclcpp::spin(std::make_shared<MultiLidarDataSync>());
  rclcpp::shutdown();
  return 0;
}