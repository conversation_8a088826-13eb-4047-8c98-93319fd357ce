#pragma once
#include <angles/angles.h>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <tf2/utils.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <message_filters/subscriber.h>
#include <message_filters/synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <vector>
#include <string>
#include <Eigen/Dense>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h>

using namespace std::chrono_literals;

class MultiLidarDataSync : public rclcpp::Node {
public:
  explicit MultiLidarDataSync(const rclcpp::NodeOptions &options = rclcpp::NodeOptions());
  ~MultiLidarDataSync();

private:
  void SyncCallback(const sensor_msgs::msg::LaserScan::ConstSharedPtr& main_scan,
                   const sensor_msgs::msg::LaserScan::ConstSharedPtr& sub_scan1);
  
  bool getLaserPose(Eigen::Matrix4f &odom_pose, 
                    rclcpp::Time dt, 
                    const std::string& lidar_frame);

  void publishScan(const sensor_msgs::msg::LaserScan::SharedPtr scan_msg,
                   const std::vector<float> &ranges,
                   const std::vector<double> &angles,
                   const std::vector<float> &intensities,
                   const geometry_msgs::msg::PoseStamped &start_pose,
                   const geometry_msgs::msg::PoseStamped &end_pose);

  void transformLaserToMainFrameWithOdom(
      const pcl::PointCloud<pcl::PointXYZI> &sub_cloud,
      pcl::PointCloud<pcl::PointXYZI> &transformed_cloud,
      const rclcpp::Time& sub_time,
      const rclcpp::Time& main_time);
  
  bool mergeLaserScans(const sensor_msgs::msg::LaserScan::ConstSharedPtr main_scan,
                       const std::vector<pcl::PointCloud<pcl::PointXYZI>> &all_clouds);

  Eigen::Matrix4f transformStampedToEigenMatrix(const geometry_msgs::msg::TransformStamped& transform);

  void transformLaserToPose(const sensor_msgs::msg::LaserScan::ConstSharedPtr& scan,
                            pcl::PointCloud<pcl::PointXYZI> &cloud);

  std::string main_scan_topic_;
  std::string sub_scan_topic_;
  std::string scan_pub_topic_;
  std::string main_lidar_frame_;
  std::string sub_lidar_frame_;
  std::string odom_frame_;
  std::string base_frame_;
  double lidar_scan_time_gain_;
  int max_sub_lidars_;

  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  std::shared_ptr<tf2_ros::TransformListener> tf_listener_;

  std::shared_ptr<message_filters::Subscriber<sensor_msgs::msg::LaserScan>> main_scan_sub_;
  std::shared_ptr<message_filters::Subscriber<sensor_msgs::msg::LaserScan>> sub_scan_sub_;
  
  typedef message_filters::sync_policies::ApproximateTime<
    sensor_msgs::msg::LaserScan,  
    sensor_msgs::msg::LaserScan> MySyncPolicy;
  
  std::shared_ptr<message_filters::Synchronizer<MySyncPolicy>> sync_;
  rclcpp::Publisher<sensor_msgs::msg::LaserScan>::SharedPtr scan_pub_;
};