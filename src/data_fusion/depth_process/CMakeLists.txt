cmake_minimum_required(VERSION 3.8)
project(depth_process)

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(PCL REQUIRED)
find_package(OpenCV REQUIRED)
find_package(pcl_conversions REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(interfaces REQUIRED)

# Create executable
add_executable(depth_filter_node
    src/depth_filter_main.cpp
    src/filters/depth_filter_node.cpp
    src/config.cpp
    src/filters/image_filter.cpp
    src/filters/point_cloud_filter.cpp
)

add_executable(cliff_detector_node
    src/cliif_detector_main.cpp
    src/cliff_detect/cliff_detector_node.cpp
    src/cliff_detect/cliff_detector.cpp
    src/config.cpp
)

ament_target_dependencies(depth_filter_node
  rclcpp
  sensor_msgs
  cv_bridge
  OpenCV
  pcl_conversions
  interfaces
)

ament_target_dependencies(cliff_detector_node
  rclcpp
  sensor_msgs
  cv_bridge
  OpenCV
  pcl_conversions
)

target_link_libraries(depth_filter_node
  ${PCL_LIBRARIES}
  yaml-cpp
  interfaces::health_provider
)

target_link_libraries(cliff_detector_node
  ${PCL_LIBRARIES}
  yaml-cpp
)

target_include_directories(depth_filter_node PUBLIC
  ${PCL_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_SOURCE_DIR}/include/filters
)

target_include_directories(cliff_detector_node PUBLIC
  ${PCL_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_SOURCE_DIR}/include/cliff_detect
)

install(TARGETS
  depth_filter_node
  cliff_detector_node 
  DESTINATION lib/${PROJECT_NAME}
)

install(DIRECTORY
  launch
  cfg
  DESTINATION share/${PROJECT_NAME}
)

ament_package()