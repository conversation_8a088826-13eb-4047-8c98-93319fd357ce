#pragma once

#include <vector>
#include "sensor_msgs/msg/image.hpp"
#include "sensor_msgs/msg/camera_info.hpp"
#include <opencv2/opencv.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include "config.h"

namespace rp::depth_camera::process {

class CliffDetector
{
public:
    static constexpr float GROUND_HEIGHT = 0.0f;
public:
    explicit CliffDetector(const CliffDetectorConfig &cliff_detector_config);

public:
    pcl::PointCloud<pcl::PointXYZ>::Ptr detectCliff(const sensor_msgs::msg::Image::ConstSharedPtr & image);
    void updateCameraInfo(const sensor_msgs::msg::CameraInfo& camera_info);

private:
    struct Intrinsics {
        float fx;
        float fy;
        float cx;
        float cy;
    };

private:
    std::atomic_bool initialized_;
    Intrinsics intrinsics_;
    CliffDetectorConfig config_;
};

} 

