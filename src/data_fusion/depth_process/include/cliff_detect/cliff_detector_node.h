#pragma once

#include <memory>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <sensor_msgs/msg/camera_info.hpp>
#include <cv_bridge/cv_bridge.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <string>
#include <map>
#include <vector>
#include <atomic>

#include "config.h"
#include "cliff_detect/cliff_detector.h"

namespace rp::depth_camera::process {

    class CliffDetectorNode : public rclcpp::Node
    {
    public:
        CliffDetectorNode();
        bool start();

    private:     
        void depthCallback(const sensor_msgs::msg::Image::ConstSharedPtr depth_msg);
        void cameraInfoCallback(const sensor_msgs::msg::CameraInfo::ConstSharedPtr info_msg);
        bool setupSubscribers();

    private:
        CliffDetectorConfig cliff_detector_config_;
        std::shared_ptr<CliffDetector> cliff_detector_;
        rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr cliff_cloud_pub_;
        rclcpp::Subscription<sensor_msgs::msg::Image>::SharedPtr depth_sub_;
        rclcpp::Subscription<sensor_msgs::msg::CameraInfo>::SharedPtr camera_info_sub_;
        bool is_running_ = false;
        std::atomic_bool camera_info_received_;
    };
}

