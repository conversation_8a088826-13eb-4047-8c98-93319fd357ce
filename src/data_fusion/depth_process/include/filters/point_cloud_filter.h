#pragma once

#include <memory>
#include <string>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/passthrough.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <std_msgs/msg/header.hpp>
#include <rclcpp/rclcpp.hpp>
#include "config.h"

namespace rp::depth_camera::process {
    
    class PointCloudFilter {
    public:
        explicit PointCloudFilter(const PointCloudFilterConfig& config);
        void processPointCloud(pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud);
            
    private:
        PointCloudFilterConfig config_;

        std::shared_ptr<pcl::VoxelGrid<pcl::PointXYZ>> voxel_filter_;
        std::shared_ptr<pcl::PassThrough<pcl::PointXYZ>> passthrough_filter_x_;
        std::shared_ptr<pcl::PassThrough<pcl::PointXYZ>> passthrough_filter_y_;
        std::shared_ptr<pcl::PassThrough<pcl::PointXYZ>> passthrough_filter_z_;
        
        std::shared_ptr<pcl::SACSegmentation<pcl::PointXYZ>> ground_segmentation_;
        std::shared_ptr<pcl::ExtractIndices<pcl::PointXYZ>> cloud_extractor_;
    };
} 