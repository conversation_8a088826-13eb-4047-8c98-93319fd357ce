#pragma once

#include <memory>
#include <string>
#include <opencv2/opencv.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <rclcpp/rclcpp.hpp>

#include "config.h"

namespace rp::depth_camera::process {

    struct ImageFilterConfig;

    class ImageFilter {
    public:
        explicit ImageFilter(const ImageFilterConfig& image_filter_config);
        void processImage(cv::Mat& image);

    private:
        void artifactFilter(cv::Mat& image);
        void cropFilter(cv::Mat& image);
        void noiseFilter(cv::Mat& image);

    private:
        ImageFilterConfig image_filter_config_;
    };
    
}



