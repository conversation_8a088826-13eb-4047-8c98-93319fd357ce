#pragma once

#include <memory>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <sensor_msgs/msg/camera_info.hpp>
#include <cv_bridge/cv_bridge.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <message_filters/subscriber.h>
#include <message_filters/synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <string>
#include <map>
#include <vector>
#include <atomic>
#include <chrono>

#include "config.h"
#include "filters/image_filter.h"
#include "filters/point_cloud_filter.h"
#include "health_provider/health_provider.h"

namespace rp::depth_camera::process {

    class DepthFilterNode : public rclcpp::Node
    {
    public:
        DepthFilterNode();
        bool start();

    private:
        struct DepthSubscriber {
            std::shared_ptr<message_filters::Subscriber<sensor_msgs::msg::Image>> depth_sub;
            std::shared_ptr<message_filters::Subscriber<sensor_msgs::msg::CameraInfo>> camera_info_sub;
            Eigen::Matrix4f intrinsics;
            Eigen::Matrix4f extrinsics;
            std::string description;
        };
        
        void syncDepthCallback2(const sensor_msgs::msg::Image::ConstSharedPtr img1,
                            const sensor_msgs::msg::Image::ConstSharedPtr img2);
                            
        void syncCameraInfoCallback2(const sensor_msgs::msg::CameraInfo::ConstSharedPtr info1,
                                const sensor_msgs::msg::CameraInfo::ConstSharedPtr info2);
                            
        void depthCallback(const sensor_msgs::msg::Image::ConstSharedPtr depth_msg);
                      
        void cameraInfoCallback(const sensor_msgs::msg::CameraInfo::ConstSharedPtr info_msg);
                        
        bool setupSubscribers();
        
        pcl::PointCloud<pcl::PointXYZ>::Ptr depthToPointCloud(
            const cv::Mat& depth_image, 
            const Eigen::Matrix4f& intrinsics, 
            const Eigen::Matrix4f& extrinsics);
            
        void publishPointCloud(
            pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud,
            const std_msgs::msg::Header& header);

        // Health monitoring functions
        void initializeHealthProvider();
        void checkDepthDataTimeout();
        void updateLastDepthTime();

    private:
        DepthCameraFilterConfig filter_config_;
        std::shared_ptr<ImageFilter> image_filter_;
        std::shared_ptr<PointCloudFilter> point_cloud_filter_;
        std::map<std::string, DepthSubscriber> device_map_; 
        std::shared_ptr<message_filters::Synchronizer<message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::Image, sensor_msgs::msg::Image>>> sync_depth2_;
        std::shared_ptr<message_filters::Synchronizer<message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::CameraInfo, sensor_msgs::msg::CameraInfo>>> sync_info2_;
        rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr cloud_pub_;
        bool is_running_ = false;
        
        std::atomic_bool camera_info_received_ = false;
        int point_cloud_step_ = 3;

        // Health monitoring members
        std::shared_ptr<rslamware::health::HealthProvider> health_provider_;
        rclcpp::TimerBase::SharedPtr health_check_timer_;
        std::chrono::steady_clock::time_point last_depth_time_;
        std::atomic_bool depth_data_received_ = false;
        double depth_timeout_threshold_ = 5.0;  // seconds
    };
}

