#pragma once
#include <vector>
#include <string>
#include <Eigen/Dense>
#include <yaml-cpp/yaml.h>

namespace rp::depth_camera::process {

    struct CropFilterConfig {
        int min_x;
        int min_y;
        int max_x;
        int max_y;
        bool enable;

        CropFilterConfig() : min_x(0), min_y(0), max_x(0), max_y(0), enable(false) {}
    };

    struct ArtifactFilterConfig {
        float min_depth;
        float max_depth;
        bool enable;

        ArtifactFilterConfig() : min_depth(0.0f), max_depth(0.0f), enable(false) {}
    };

    struct NoiseFilterConfig {
        bool enable;

        NoiseFilterConfig() : enable(false) {}
    };

    struct CameraDevice {
        bool enable;
        std::string description;
        std::string depth_image_topic_name;
        std::string camera_info_topic_name;
        Eigen::Matrix4f extrinsics;

        static CameraDevice fromYAML(const YAML::Node& node) {
            CameraDevice device;
            device.enable = node["enable"].as<bool>();
            device.description = node["description"].as<std::string>();
            device.depth_image_topic_name = node["depth_image_topic_name"].as<std::string>();
            device.camera_info_topic_name = node["camera_info_topic_name"].as<std::string>();
            std::vector<float> extrinsic_values = node["extrinsics"].as<std::vector<float>>();
            if (extrinsic_values.size() == 16) {
                device.extrinsics = Eigen::Map<Eigen::Matrix<float, 4, 4, Eigen::RowMajor>>(&extrinsic_values[0]);
            }
            return device;
        }

    };

    struct ImageFilterConfig {
        ArtifactFilterConfig artifact_filter;
        CropFilterConfig crop_filter;
        NoiseFilterConfig noise_filter;
    };
    
    struct GroundFilterConfig {
        bool enable;
        float max_z;
        float distance_threshold;
        float angle_threshold;

        GroundFilterConfig() : enable(false), max_z(0.1f), distance_threshold(0.1f), angle_threshold(0.1f) {}
    };

    struct PassthroughFilterConfig {
        bool enable;
        float min_x;
        float min_y;
        float min_z;
        float max_x;
        float max_y;
        float max_z;

        PassthroughFilterConfig() : enable(false), 
                                   min_x(0.5f), min_y(-1.0f), min_z(-1.5f),
                                   max_x(1.5f), max_y(1.0f), max_z(1.5f) {}
    };

    struct VoxelFilterConfig {
        bool enable;
        float leaf_size_x;
        float leaf_size_y; 
        float leaf_size_z;

        VoxelFilterConfig() : enable(false), 
                             leaf_size_x(0.01f), leaf_size_y(0.01f), leaf_size_z(0.01f) {}
    };

    struct PointCloudFilterConfig {
        VoxelFilterConfig voxel_filter;
        GroundFilterConfig ground_filter;
        PassthroughFilterConfig passthrough_filter;
    };

    struct DepthCameraFilterConfig {
        std::vector<CameraDevice> devices;
        ImageFilterConfig image_filter_config;
        PointCloudFilterConfig point_cloud_filter_config;
        std::string point_cloud_publisher_name;
    };

    struct CliffDetectorConfig {
        bool enable;
        int depth_image_step;
        float range_min;
        float range_max;
        float width_range;
        float height_variance_threshold;
        std::string depth_image_topic_name;
        std::string camera_info_topic_name;
        Eigen::Matrix4f extrinsics;
        std::string cliff_cloud_publisher_name;

        CliffDetectorConfig()
            : enable(false),
              depth_image_step(2),
              range_min(0.3f), 
              range_max(3.0f), 
              width_range(1.0f),
              height_variance_threshold(0.03f)
        {
            
        }
    };

    class Config {
    public:
        static bool loadFilterConfig(const std::string &config_path,
                             DepthCameraFilterConfig &filter_config);
        static bool loadCliffDetectorConfig(const std::string &config_path,
                             CliffDetectorConfig &cliff_detector_config);

    private:
        static bool loadCameraDevices(const YAML::Node& config, 
                                    std::vector<CameraDevice>& devices);
        static bool loadImageFilterConfig(const YAML::Node& config, 
                                   ImageFilterConfig& filter_config);
        static bool loadArtifactFilter(const YAML::Node& config, 
                                     ArtifactFilterConfig& artifact);
        static bool loadCropFilter(const YAML::Node& config, 
                                    CropFilterConfig& crop);
        static bool loadNoiseFilter(const YAML::Node& config, 
                                    NoiseFilterConfig& noise);
        static bool loadPointCloudFilterConfig(const YAML::Node &config,
                                    PointCloudFilterConfig &filter_config);
        static bool loadVoxelFilter(const YAML::Node& config,
                                   VoxelFilterConfig& voxel);
        static bool loadCliffDetectorConfig(const YAML::Node &config,
                                    CliffDetectorConfig &cliff_detector_config);
    };
}