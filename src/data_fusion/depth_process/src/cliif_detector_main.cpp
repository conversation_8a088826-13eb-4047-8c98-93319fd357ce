#include <rclcpp/rclcpp.hpp>
#include "cliff_detect/cliff_detector_node.h"
#include <pcl/console/print.h>

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    pcl::console::setVerbosityLevel(static_cast<pcl::console::VERBOSITY_LEVEL>(-1));
    auto node = std::make_shared<rp::depth_camera::process::CliffDetectorNode>();
    
    if (!node->start()) {
        RCLCPP_ERROR(node->get_logger(), "Failed to start cliff detector node!");
        rclcpp::shutdown();
        return 1;
    }
    
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
