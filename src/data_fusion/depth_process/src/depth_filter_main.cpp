#include <rclcpp/rclcpp.hpp>
#include "filters/depth_filter_node.h"
#include <pcl/console/print.h>

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    pcl::console::setVerbosityLevel(static_cast<pcl::console::VERBOSITY_LEVEL>(-1));
    auto node = std::make_shared<rp::depth_camera::process::DepthFilterNode>();
    
    if (!node->start()) {
        RCLCPP_ERROR(node->get_logger(), "Failed to start depth filter node!");
        rclcpp::shutdown();
        return 1;
    }
    
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
