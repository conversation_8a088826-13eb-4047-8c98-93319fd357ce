#include "cliff_detector.h"
#include "sensor_msgs/image_encodings.hpp"

namespace rp::depth_camera::process {

    CliffDetector::CliffDetector(const CliffDetectorConfig &config)
        : initialized_(false)
        , config_(config)
    {
        //
    }

    void CliffDetector::updateCameraInfo(const sensor_msgs::msg::CameraInfo& camera_info)
    {
        intrinsics_.fx = camera_info.k[0];
        intrinsics_.fy = camera_info.k[4];
        intrinsics_.cx = camera_info.k[2];
        intrinsics_.cy = camera_info.k[5];
        initialized_.store(true);
    }

    pcl::PointCloud<pcl::PointXYZ>::Ptr CliffDetector::detectCliff(const sensor_msgs::msg::Image::ConstSharedPtr & image)
    {
        if (image->encoding != sensor_msgs::image_encodings::TYPE_16UC1) {
            throw std::runtime_error("Depth image must be 16-bit unsigned type");
        }

        if (!initialized_.load()) {
            return nullptr;
        }

        pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZ>);
        const uint16_t* depth_data = reinterpret_cast<const uint16_t*>(image->data.data());

        std::vector<float> valid_heights;
        valid_heights.reserve(image->width * image->height / (config_.depth_image_step * config_.depth_image_step)); // 预估大小

        for (uint32_t v = 0; v < image->height; v += config_.depth_image_step) {
            for (uint32_t u = 0; u < image->width; u += config_.depth_image_step) {
                uint16_t depth_value = depth_data[v * image->width + u];
                if (depth_value == 0 || depth_value == 65535) {
                    continue;
                }
                float depth_meters = depth_value * 0.001f;
                float x = (u - intrinsics_.cx) * depth_meters / intrinsics_.fx;
                float y = (v - intrinsics_.cy) * depth_meters / intrinsics_.fy;
                float z = depth_meters;

                Eigen::Vector4f point_camera(x, y, z, 1.0f);
                Eigen::Vector4f point_world = config_.extrinsics * point_camera;
                pcl::PointXYZ point;
                point.x = point_world[0];
                point.y = point_world[1];
                point.z = point_world[2];

                if (point.x > config_.range_min && point.x < config_.range_max &&
                    std::abs(point.y) < config_.width_range &&
                    point.z < GROUND_HEIGHT)
                {
                    filtered_cloud->points.push_back(point);
                    valid_heights.push_back(point.z);
                }
            }
        }

        bool is_cliff = false;
        if (!filtered_cloud->points.empty()) {
            float mean_height = 0.0f;
            for(float h : valid_heights) {
                mean_height += h;
            }
            mean_height /= valid_heights.size();

            float height_variance = 0.0f;
            for(float h : valid_heights) {
                height_variance += (h - mean_height) * (h - mean_height);
            }
            height_variance /= valid_heights.size();
            if (height_variance > config_.height_variance_threshold) {
                is_cliff = true;
            }
        }

        if (!is_cliff) {
            return nullptr;
        }
        filtered_cloud->width = filtered_cloud->points.size();
        filtered_cloud->height = 1;
        filtered_cloud->is_dense = false; // 可能包含NaN点，但我们已经过滤了0和65535
        return filtered_cloud;
    }
} // namespace rp::depth_camera::process

