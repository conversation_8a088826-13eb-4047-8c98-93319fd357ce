#include "cliff_detect/cliff_detector_node.h"
#include <yaml-cpp/yaml.h>

namespace rp::depth_camera::process {

    CliffDetectorNode::CliffDetectorNode()
        : Node("cliff_detector_node")
        , camera_info_received_(false)
    {
        //
    }

    bool CliffDetectorNode::start()
    {
        if (is_running_) {
            RCLCPP_WARN(this->get_logger(), "Cliff detector node is already running!");
            return true;
        }

        std::string config_path;
        this->declare_parameter("config_path", "");
        this->get_parameter("config_path", config_path);
            
        if (config_path.empty()) {
            RCLCPP_ERROR(this->get_logger(), "Config path is empty!");
            return false;
        }

        if (!Config::loadCliffDetectorConfig(config_path, cliff_detector_config_)) {
            RCLCPP_ERROR(this->get_logger(), "Failed to load config!");
            return false;
        }

        cliff_detector_ = std::make_shared<CliffDetector>(cliff_detector_config_);

        if (!setupSubscribers()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to setup subscribers!");
            return false;
        }

        is_running_ = true;
        return true;
    }

    bool CliffDetectorNode::setupSubscribers()
    {
        depth_sub_ = this->create_subscription<sensor_msgs::msg::Image>(
            cliff_detector_config_.depth_image_topic_name, 10,
            std::bind(&CliffDetectorNode::depthCallback, this, std::placeholders::_1));
            
        camera_info_sub_ = this->create_subscription<sensor_msgs::msg::CameraInfo>(
            cliff_detector_config_.camera_info_topic_name, 10,
            std::bind(&CliffDetectorNode::cameraInfoCallback, this, std::placeholders::_1));

        cliff_cloud_pub_ = this->create_publisher<sensor_msgs::msg::PointCloud2>(cliff_detector_config_.cliff_cloud_publisher_name, 10);
        
        RCLCPP_INFO(this->get_logger(), "Cliff detector node subscribers setup successfully");
        return true;
    }

    void CliffDetectorNode::depthCallback(const sensor_msgs::msg::Image::ConstSharedPtr depth_msg)
    {
        if (!cliff_detector_config_.enable || !cliff_detector_) {
            return;
        }
        
        if (!camera_info_received_.load()) {
            RCLCPP_INFO(this->get_logger(), "Skipping depth image processing: camera info not received");
            return;
        }
        auto start_time = std::chrono::high_resolution_clock::now();
        pcl::PointCloud<pcl::PointXYZ>::Ptr cliff_points = cliff_detector_->detectCliff(depth_msg);
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        RCLCPP_DEBUG(this->get_logger(), "Cliff detector time: %ld ms", duration.count());
        if (cliff_points && !cliff_points->empty()) {
            sensor_msgs::msg::PointCloud2 cloud_msg;
            pcl::toROSMsg(*cliff_points, cloud_msg);
            cloud_msg.header = depth_msg->header;
            cloud_msg.header.frame_id = "base_link";
            cliff_cloud_pub_->publish(cloud_msg);
        }
    }

    void CliffDetectorNode::cameraInfoCallback(const sensor_msgs::msg::CameraInfo::ConstSharedPtr info_msg)
    {
        RCLCPP_INFO(this->get_logger(), "Received camera info");
        if (cliff_detector_) {
            cliff_detector_->updateCameraInfo(*info_msg);
        }
    
        RCLCPP_INFO(this->get_logger(), "Camera intrinsics: fx=%f, cx=%f, fy=%f, cy=%f",
            info_msg->k[0], info_msg->k[2], 
            info_msg->k[4], info_msg->k[5]);
            
        camera_info_received_.store(true);
        camera_info_sub_ = nullptr;
    }
    
}