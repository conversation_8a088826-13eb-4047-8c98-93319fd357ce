#include "filters/image_filter.h"

namespace rp::depth_camera::process {

    ImageFilter::ImageFilter(const ImageFilterConfig& image_filter_config)
        : image_filter_config_(image_filter_config)
    {
        //
    }

    void ImageFilter::processImage(cv::Mat& image) {
        if (image.empty()) {
            return;
        }
        
        if (image_filter_config_.artifact_filter.enable) {
            artifactFilter(image);
        }
        if (image_filter_config_.crop_filter.enable) {
            cropFilter(image);
        }
        if (image_filter_config_.noise_filter.enable) {
            noiseFilter(image);
        }
    }

    void ImageFilter::artifactFilter(cv::Mat& image)
    {
        if (image.type() != CV_16UC1) {
            RCLCPP_WARN(rclcpp::get_logger("image_filter"), "Depth image type is not CV_16UC1");
            return;
        }
        const ArtifactFilterConfig& artifact_config = image_filter_config_.artifact_filter;
        cv::Mat mask;
        cv::inRange(image, 
                    static_cast<int>(artifact_config.min_depth * 1000), 
                    static_cast<int>(artifact_config.max_depth * 1000), 
                    mask);
        image.setTo(0, ~mask);
    }

    void ImageFilter::cropFilter(cv::Mat& image)
    {
        if (image_filter_config_.crop_filter.enable) {
            int min_x = image_filter_config_.crop_filter.min_x;
            int min_y = image_filter_config_.crop_filter.min_y;
            int max_x = image_filter_config_.crop_filter.max_x;
            int max_y = image_filter_config_.crop_filter.max_y;
            
            min_x = std::max(0, std::min(min_x, image.cols - 1));
            min_y = std::max(0, std::min(min_y, image.rows - 1));
            max_x = std::max(0, std::min(max_x, image.cols - 1));
            max_y = std::max(0, std::min(max_y, image.rows - 1));
            
            cv::Mat mask = cv::Mat::zeros(image.size(), CV_16UC1);
            mask(cv::Range(min_y, max_y + 1), cv::Range(min_x, max_x + 1)) = 65535;
            image.setTo(0, mask == 0);
        }
    }

    void ImageFilter::noiseFilter(cv::Mat& image)
    {
        cv::medianBlur(image, image, 5);
    }
}
