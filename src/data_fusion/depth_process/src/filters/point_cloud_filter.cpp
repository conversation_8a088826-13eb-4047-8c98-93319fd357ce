#include "filters/point_cloud_filter.h"
#include <pcl/filters/passthrough.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/point_types.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/filters/voxel_grid.h>

namespace rp::depth_camera::process {

    PointCloudFilter::PointCloudFilter(const PointCloudFilterConfig& config)
        : config_(config)
    {
        if (config_.voxel_filter.enable) {
            voxel_filter_ = std::make_shared<pcl::VoxelGrid<pcl::PointXYZ>>();
            voxel_filter_->setLeafSize(config_.voxel_filter.leaf_size_x,
                                     config_.voxel_filter.leaf_size_y,
                                     config_.voxel_filter.leaf_size_z);
        }
        
        if (config_.passthrough_filter.enable) {
            passthrough_filter_x_ = std::make_shared<pcl::PassThrough<pcl::PointXYZ>>();
            passthrough_filter_x_->setFilterFieldName("x");
            passthrough_filter_x_->setFilterLimits(config_.passthrough_filter.min_x, 
                                                 config_.passthrough_filter.max_x);
            
            passthrough_filter_y_ = std::make_shared<pcl::PassThrough<pcl::PointXYZ>>();
            passthrough_filter_y_->setFilterFieldName("y");
            passthrough_filter_y_->setFilterLimits(config_.passthrough_filter.min_y, 
                                                 config_.passthrough_filter.max_y);
            
            passthrough_filter_z_ = std::make_shared<pcl::PassThrough<pcl::PointXYZ>>();
            passthrough_filter_z_->setFilterFieldName("z");
            passthrough_filter_z_->setFilterLimits(config_.passthrough_filter.min_z, 
                                                 config_.passthrough_filter.max_z);
        }
        
        if (config_.ground_filter.enable) {
            ground_segmentation_ = std::make_shared<pcl::SACSegmentation<pcl::PointXYZ>>();
            ground_segmentation_->setModelType(pcl::SACMODEL_PLANE);
            ground_segmentation_->setMethodType(pcl::SAC_RANSAC);
            ground_segmentation_->setDistanceThreshold(config_.ground_filter.distance_threshold);
            ground_segmentation_->setMaxIterations(1000);
            
            Eigen::Vector3f axis(0, 0, 1);
            ground_segmentation_->setAxis(axis);
            ground_segmentation_->setEpsAngle(config_.ground_filter.angle_threshold);
            
            cloud_extractor_ = std::make_shared<pcl::ExtractIndices<pcl::PointXYZ>>();
        }
    }
    
    void PointCloudFilter::processPointCloud(pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud)
    {
        if (!cloud || cloud->empty()) {
            return;
        }
        
        if (config_.voxel_filter.enable && voxel_filter_) {
            voxel_filter_->setInputCloud(cloud);
            voxel_filter_->filter(*cloud);
            
            if (cloud->empty()) {
                return;
            }
        }
        
        if (config_.passthrough_filter.enable) {
            if (passthrough_filter_x_) {
                passthrough_filter_x_->setInputCloud(cloud);
                passthrough_filter_x_->filter(*cloud);
            }
            
            if (passthrough_filter_y_) {
                passthrough_filter_y_->setInputCloud(cloud);
                passthrough_filter_y_->filter(*cloud);
            }
            
            if (passthrough_filter_z_) {
                passthrough_filter_z_->setInputCloud(cloud);
                passthrough_filter_z_->filter(*cloud);
            }
        }

        if (cloud->empty()) {
            return;
        }
        
        if (config_.ground_filter.enable && ground_segmentation_ && cloud_extractor_) {
            pcl::PointCloud<pcl::PointXYZ>::Ptr low_cloud(new pcl::PointCloud<pcl::PointXYZ>);
            pcl::PointIndices::Ptr low_indices(new pcl::PointIndices);
            
            for (size_t i = 0; i < cloud->points.size(); ++i) {
                if (cloud->points[i].z < config_.ground_filter.max_z) {
                    low_indices->indices.emplace_back(i);
                }
            }

            if (low_indices->indices.empty()) {
                return;
            }
            
            cloud_extractor_->setInputCloud(cloud);
            cloud_extractor_->setIndices(low_indices);
            cloud_extractor_->setNegative(false);
            cloud_extractor_->filter(*low_cloud);
            
            pcl::PointIndices::Ptr ground_indices(new pcl::PointIndices);
            pcl::ModelCoefficients::Ptr ground_coefficients(new pcl::ModelCoefficients);
            ground_segmentation_->setInputCloud(low_cloud);
            ground_segmentation_->segment(*ground_indices, *ground_coefficients);
            
            if (!ground_indices->indices.empty()) {
                pcl::PointIndices::Ptr original_ground_indices(new pcl::PointIndices);
                for (const auto& idx : ground_indices->indices) {
                    original_ground_indices->indices.push_back(low_indices->indices[idx]);
                }
                
                cloud_extractor_->setInputCloud(cloud);
                cloud_extractor_->setIndices(original_ground_indices);
                cloud_extractor_->setNegative(true);
                cloud_extractor_->filter(*cloud);

                /*if (!cloud->empty()) {
                    pcl::RadiusOutlierRemoval<pcl::PointXYZ> outrem;
                    outrem.setInputCloud(cloud);
                    outrem.setRadiusSearch(0.05);
                    outrem.setMinNeighborsInRadius(10);
                    outrem.filter(*cloud);
                }*/
            }
        }
    }
}