#include "filters/depth_filter_node.h"
#include <yaml-cpp/yaml.h>
#include "health.h"

namespace rp::depth_camera::process {

    DepthFilterNode::DepthFilterNode()
        : Node("depth_filter_node")
    {
        // Declare depth timeout parameter
        this->declare_parameter("depth_timeout_threshold", 5.0);
        depth_timeout_threshold_ = this->get_parameter("depth_timeout_threshold").as_double();

    }

    bool DepthFilterNode::start()
    {
        if (is_running_) {
            RCLCPP_WARN(this->get_logger(), "Depth filter node is already running!");
            return true;
        }

        std::string config_path;
        this->declare_parameter("config_path", "");
        this->get_parameter("config_path", config_path);
            
        if (config_path.empty()) {
            RCLCPP_ERROR(this->get_logger(), "Config path is empty!");
            return false;
        }

        if (!Config::loadFilterConfig(config_path, filter_config_)) {
            RCLCPP_ERROR(this->get_logger(), "Failed to load config!");
            return false;
        }

        image_filter_ = std::make_shared<ImageFilter>(filter_config_.image_filter_config);
        point_cloud_filter_ = std::make_shared<PointCloudFilter>(filter_config_.point_cloud_filter_config);

        for (const auto& device : filter_config_.devices) {
            if(!device.enable) {
                RCLCPP_INFO(this->get_logger(), "Device[%s] is disabled", device.description.c_str());
                continue;
            }

            DepthSubscriber depth_sub;
            depth_sub.extrinsics = device.extrinsics;
            depth_sub.description = device.description;
            
            depth_sub.depth_sub = std::make_shared<message_filters::Subscriber<sensor_msgs::msg::Image>>(
                this, device.depth_image_topic_name);
            
            depth_sub.camera_info_sub = std::make_shared<message_filters::Subscriber<sensor_msgs::msg::CameraInfo>>(
                this, device.camera_info_topic_name);
            
            device_map_[device.description] = depth_sub;
            
            RCLCPP_INFO(this->get_logger(), "Subscribed to depth topic: %s for device: %s", 
                device.depth_image_topic_name.c_str(), device.description.c_str());
            RCLCPP_INFO(this->get_logger(), "Subscribed to camera info topic: %s for device: %s", 
                device.camera_info_topic_name.c_str(), device.description.c_str());
        }

        if (device_map_.empty()) {
            RCLCPP_ERROR(this->get_logger(), "No depth subscribers configured!");
            return false;
        }
        if (device_map_.size() > 2) {
            RCLCPP_ERROR(this->get_logger(), "Too many depth subscribers configured! Maximum allowed is 2.");
            return false;
        }

        if (!setupSubscribers()) {
            RCLCPP_ERROR(this->get_logger(), "Setup depth subscribers failed!");
            return false;
        }

        // Initialize health monitoring
        initializeHealthProvider();

        is_running_ = true;
        return true;
    }

    bool DepthFilterNode::setupSubscribers()
    {
        if(device_map_.size() == 1) {
            const auto& pair = *device_map_.begin();
            const std::string& device_description = pair.first;
            DepthSubscriber& depth_sub = device_map_[device_description];
            
            depth_sub.depth_sub->registerCallback(
                std::bind(&DepthFilterNode::depthCallback, this, 
                    std::placeholders::_1));
            
            depth_sub.camera_info_sub->registerCallback(
                std::bind(&DepthFilterNode::cameraInfoCallback, this, 
                    std::placeholders::_1));
        }
        else if (device_map_.size() == 2) {
            std::vector<std::string> device_descriptions;
            for (const auto& pair : device_map_) {
                device_descriptions.push_back(pair.first);
            }
            
            using SyncPolicy2 = message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::Image, sensor_msgs::msg::Image>;
            sync_depth2_ = std::make_shared<message_filters::Synchronizer<SyncPolicy2>>(
                SyncPolicy2(10), 
                *device_map_[device_descriptions[0]].depth_sub, 
                *device_map_[device_descriptions[1]].depth_sub);
            
            sync_depth2_->registerCallback(
                std::bind(&DepthFilterNode::syncDepthCallback2, this, 
                    std::placeholders::_1, std::placeholders::_2));
            
            using SyncPolicyInfo2 = message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::CameraInfo, sensor_msgs::msg::CameraInfo>;
            sync_info2_ = std::make_shared<message_filters::Synchronizer<SyncPolicyInfo2>>(
                SyncPolicyInfo2(10),
                *device_map_[device_descriptions[0]].camera_info_sub,
                *device_map_[device_descriptions[1]].camera_info_sub);
            
            sync_info2_->registerCallback(
                std::bind(&DepthFilterNode::syncCameraInfoCallback2, this,
                    std::placeholders::_1, std::placeholders::_2));
            
            RCLCPP_INFO(this->get_logger(), "Synchronized depth images and camera info from two cameras: %s and %s", 
                device_descriptions[0].c_str(), device_descriptions[1].c_str());
        }
        
        RCLCPP_INFO(this->get_logger(), "Depth filter node started successfully");
        cloud_pub_ = this->create_publisher<sensor_msgs::msg::PointCloud2>(filter_config_.point_cloud_publisher_name, 10);
        return true;
    }

    pcl::PointCloud<pcl::PointXYZ>::Ptr DepthFilterNode::depthToPointCloud(
        const cv::Mat& depth_image, 
        const Eigen::Matrix4f& intrinsics, 
        const Eigen::Matrix4f& extrinsics)
    {
        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
        
        if (depth_image.empty() || depth_image.type() != CV_16UC1) {
            RCLCPP_ERROR(this->get_logger(), "Invalid depth image format");
            return cloud;
        }
        
        const float fx_inv = 1.0f / intrinsics(0, 0);
        const float fy_inv = 1.0f / intrinsics(1, 1);
        const float cx = intrinsics(0, 2);
        const float cy = intrinsics(1, 2);
        
        std::vector<Eigen::Vector4f> camera_points;
        size_t estimated_points = (depth_image.rows / point_cloud_step_) * (depth_image.cols / point_cloud_step_);
        camera_points.reserve(estimated_points);
        
        for (int v = 0; v < depth_image.rows; v += point_cloud_step_) {
            for (int u = 0; u < depth_image.cols; u += point_cloud_step_) {
                uint16_t depth_value = depth_image.at<uint16_t>(v, u);
                
                if (depth_value == 0 || depth_value == 65535) {
                    continue;
                }
                
                float depth_meters = depth_value * 0.001f;
                camera_points.emplace_back(
                    (u - cx) * depth_meters * fx_inv,
                    (v - cy) * depth_meters * fy_inv,
                    depth_meters,
                    1.0f
                );
            }
        }
        
        cloud->points.reserve(camera_points.size());
        cloud->height = 1;
        cloud->width = camera_points.size();
        cloud->is_dense = false;
        
        const Eigen::Matrix4f& transform = extrinsics;
        for (const auto& camera_point : camera_points) {
            Eigen::Vector4f world_point = transform * camera_point;
            pcl::PointXYZ point;
            point.x = world_point[0];
            point.y = world_point[1];
            point.z = world_point[2];
            cloud->points.push_back(point);
        }
        
        cloud->width = cloud->points.size();
        return cloud;

    }
    
    void DepthFilterNode::publishPointCloud(
        pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud,
        const std_msgs::msg::Header& header)
    {
        if (!cloud || cloud->empty()) {
            return;
        }
        if (point_cloud_filter_) {
            auto start_time = std::chrono::high_resolution_clock::now();
            point_cloud_filter_->processPointCloud(cloud);
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            RCLCPP_DEBUG(this->get_logger(), "Point cloud filtering time: %ld ms", duration.count());
        }
        sensor_msgs::msg::PointCloud2 cloud_msg;
        pcl::toROSMsg(*cloud, cloud_msg);
        cloud_msg.header = header;
        cloud_msg.header.frame_id = "base_link";
        
        cloud_pub_->publish(cloud_msg);
    }

    void DepthFilterNode::depthCallback(const sensor_msgs::msg::Image::ConstSharedPtr depth_msg)
    {
        // Update depth data timestamp for health monitoring
        updateLastDepthTime();

        if (!camera_info_received_.load()) {
            RCLCPP_INFO(this->get_logger(), "Skipping depth image processing: camera info not received");
            return;
        }

        RCLCPP_INFO_STREAM_ONCE(this->get_logger(), "Received depth image");
        
        cv_bridge::CvImageConstPtr cv_ptr;
        try {
            cv_ptr = cv_bridge::toCvShare(depth_msg, sensor_msgs::image_encodings::TYPE_16UC1);
        } catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "cv_bridge exception: %s", e.what());
            return;
        }
        
        cv::Mat processed_image = cv_ptr->image.clone();
        if (image_filter_) {
            auto start_time = std::chrono::high_resolution_clock::now();
            image_filter_->processImage(processed_image);
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            RCLCPP_DEBUG(this->get_logger(), "Depth image processing time: %ld ms", duration.count());
        }
        
        const auto& pair = *device_map_.begin();
        const std::string& device_description = pair.first;
        const auto& depth_sub = device_map_[device_description];
        
        auto depth2pcd_start_time = std::chrono::high_resolution_clock::now();
        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud = depthToPointCloud(processed_image, depth_sub.intrinsics, depth_sub.extrinsics);
        auto depth2pcd_end_time = std::chrono::high_resolution_clock::now();
        auto depth2pcd_duration = std::chrono::duration_cast<std::chrono::milliseconds>(depth2pcd_end_time - depth2pcd_start_time);
        RCLCPP_DEBUG(this->get_logger(), "depth2pcd processing time: %ld ms, now:%f, frame time:%d.%d", 
            depth2pcd_duration.count(), this->now().seconds(), depth_msg->header.stamp.sec,  depth_msg->header.stamp.nanosec);

        publishPointCloud(cloud, depth_msg->header);
    }
    
    void DepthFilterNode::cameraInfoCallback(const sensor_msgs::msg::CameraInfo::ConstSharedPtr info_msg)
    {
        RCLCPP_INFO(this->get_logger(), "Received camera info");
        
        auto pair = device_map_.begin();
        const std::string& device_description = pair->first;
        auto& depth_sub = pair->second;
        depth_sub.intrinsics = Eigen::Matrix4f::Identity();
        depth_sub.intrinsics(0,0) = info_msg->k[0];
        depth_sub.intrinsics(0,2) = info_msg->k[2];
        depth_sub.intrinsics(1,1) = info_msg->k[4];
        depth_sub.intrinsics(1,2) = info_msg->k[5];
            
        RCLCPP_INFO(this->get_logger(), "%s intrinsics: %f, %f, %f, %f", device_description.c_str(),
            depth_sub.intrinsics(0,0), depth_sub.intrinsics(0,2), 
            depth_sub.intrinsics(1,1), depth_sub.intrinsics(1,2));
        
        camera_info_received_.store(true);
        
        depth_sub.camera_info_sub->unsubscribe();
        RCLCPP_INFO(this->get_logger(), "Unsubscribed from camera info topic");
    }
    
    void DepthFilterNode::syncDepthCallback2(const sensor_msgs::msg::Image::ConstSharedPtr img1,
                        const sensor_msgs::msg::Image::ConstSharedPtr img2)
    {
        // Update depth data timestamp for health monitoring
        updateLastDepthTime();

        if (!camera_info_received_.load()) {
            RCLCPP_INFO(this->get_logger(), "Skipping synchronized depth image processing: camera info not synchronized yet");
            return;
        }

        RCLCPP_INFO_STREAM_ONCE(this->get_logger(), "Received synchronized depth images from two cameras!");

        auto start_time = std::chrono::high_resolution_clock::now();
        cv_bridge::CvImageConstPtr cv_ptr1, cv_ptr2;
        try {
            cv_ptr1 = cv_bridge::toCvShare(img1, sensor_msgs::image_encodings::TYPE_16UC1);
            cv_ptr2 = cv_bridge::toCvShare(img2, sensor_msgs::image_encodings::TYPE_16UC1);
        } catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "cv_bridge exception: %s", e.what());
            return;
        }
        
        cv::Mat processed_image1 = cv_ptr1->image.clone();
        cv::Mat processed_image2 = cv_ptr2->image.clone();
        
        std::vector<std::string> device_descriptions;
        for (const auto& pair : device_map_) {
            device_descriptions.push_back(pair.first);
        }
        
        auto future1 = std::async(std::launch::async, [&]() {
            if (image_filter_) {
                image_filter_->processImage(processed_image1);
            }
            return depthToPointCloud(
                processed_image1,
                device_map_[device_descriptions[0]].intrinsics,
                device_map_[device_descriptions[0]].extrinsics);
        });
        
        auto future2 = std::async(std::launch::async, [&]() {
            if (image_filter_) {
                image_filter_->processImage(processed_image2);
            }
            return depthToPointCloud(
                processed_image2,
                device_map_[device_descriptions[1]].intrinsics,
                device_map_[device_descriptions[1]].extrinsics);
        });
        
        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud1 = future1.get();
        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud2 = future2.get();

        pcl::PointCloud<pcl::PointXYZ>::Ptr merged_cloud(new pcl::PointCloud<pcl::PointXYZ>);
        *merged_cloud = *cloud1 + *cloud2;

        auto stamp1 = rclcpp::Time(img1->header.stamp);
        auto stamp2 = rclcpp::Time(img2->header.stamp);
        int64_t avg_time_ns = (stamp1.nanoseconds() + stamp2.nanoseconds()) / 2;
        rclcpp::Time avg_stamp(avg_time_ns);

        std_msgs::msg::Header new_header = img1->header;
        new_header.stamp = avg_stamp;
        publishPointCloud(merged_cloud, new_header);

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        if(duration.count() > 50) {
            RCLCPP_INFO(this->get_logger(), "Two depth callback cost time: total %ld ms", duration.count());
        }
    }
    
    void DepthFilterNode::syncCameraInfoCallback2(const sensor_msgs::msg::CameraInfo::ConstSharedPtr info1,
                            const sensor_msgs::msg::CameraInfo::ConstSharedPtr info2)
    {
        RCLCPP_INFO(this->get_logger(), "Received synchronized camera info from two cameras!");
        
        std::vector<std::string> device_descriptions;
        for (const auto& pair : device_map_) {
            device_descriptions.push_back(pair.first);
        }
        
        auto& depth_sub1 = device_map_[device_descriptions[0]];
        depth_sub1.intrinsics = Eigen::Matrix4f::Identity();
        depth_sub1.intrinsics(0,0) = info1->k[0];
        depth_sub1.intrinsics(0,2) = info1->k[2];
        depth_sub1.intrinsics(1,1) = info1->k[4];
        depth_sub1.intrinsics(1,2) = info1->k[5];
        
        auto& depth_sub2 = device_map_[device_descriptions[1]];
        depth_sub2.intrinsics = Eigen::Matrix4f::Identity();
        depth_sub2.intrinsics(0,0) = info2->k[0];
        depth_sub2.intrinsics(0,2) = info2->k[2];
        depth_sub2.intrinsics(1,1) = info2->k[4];
        depth_sub2.intrinsics(1,2) = info2->k[5];

        RCLCPP_INFO(this->get_logger(), "%s intrinsics: %f, %f, %f, %f", device_descriptions[0].c_str(),
            depth_sub1.intrinsics(0,0), depth_sub1.intrinsics(0,2), 
            depth_sub1.intrinsics(1,1), depth_sub1.intrinsics(1,2));

        RCLCPP_INFO(this->get_logger(), "%s intrinsics: %f, %f, %f, %f", device_descriptions[1].c_str(),
            depth_sub2.intrinsics(0,0), depth_sub2.intrinsics(0,2), 
            depth_sub2.intrinsics(1,1), depth_sub2.intrinsics(1,2));
        
        camera_info_received_.store(true);
        depth_sub1.camera_info_sub->unsubscribe();
        depth_sub2.camera_info_sub->unsubscribe();
        RCLCPP_INFO(this->get_logger(), "Updated camera intrinsics for both cameras and enabled depth synchronization");
    }

    void DepthFilterNode::initializeHealthProvider()
    {
        try {
            // Create health provider
            health_provider_ = std::make_shared<rslamware::health::HealthProvider>(
                shared_from_this(),
                "depth_process"
            );

            last_depth_time_ = std::chrono::steady_clock::now();

            // Create timer for health checking
            health_check_timer_ = this->create_wall_timer(
                std::chrono::seconds(2),  // Check every second
                std::bind(&DepthFilterNode::checkDepthDataTimeout, this)
            );

            RCLCPP_INFO(this->get_logger(), "Health provider initialized for depth filter");

        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Failed to initialize health provider: %s", e.what());
        }
    }

    void DepthFilterNode::updateLastDepthTime()
    {
        last_depth_time_ = std::chrono::steady_clock::now();
        depth_data_received_.store(true);
    }

    void DepthFilterNode::checkDepthDataTimeout()
    {
        if (!health_provider_) {
            return;
        }

        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_seconds = std::chrono::duration_cast<std::chrono::duration<double>>(
            current_time - last_depth_time_).count();

        if (!depth_data_received_.load()) {
            // No depth data received yet
            //wait 20 seconds for system initialization
            if (elapsed_seconds > 20.0) {
                health_provider_->reportHealth(SENSOR_ERROR_DEPTHCAM_START_FAILED, "depthcam start failed");
            }
            return;
        }

        if (elapsed_seconds > depth_timeout_threshold_) {
            health_provider_->reportHealth(SENSOR_ERROR_DEPTHCAM_DISCONNECTED, "depthcam disconnected");
        } else {
            // Clear any previous timeout errors
            health_provider_->removeHealth(SENSOR_ERROR_DEPTHCAM_DISCONNECTED);
        }
    }
}