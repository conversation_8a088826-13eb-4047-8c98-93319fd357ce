#include "config.h"
#include <rclcpp/rclcpp.hpp>

namespace rp::depth_camera::process {

    bool Config::loadFilterConfig(const std::string& config_path, DepthCameraFilterConfig& depth_filter_config) {
        try {
            YAML::Node config = YAML::LoadFile(config_path);
            if (!loadCameraDevices(config, depth_filter_config.devices)) {
                RCLCPP_ERROR(rclcpp::get_logger("Config"), "Failed to load camera devices!");
                return false;
            }
            if (!loadImageFilterConfig(config, depth_filter_config.image_filter_config)) {
                RCLCPP_ERROR(rclcpp::get_logger("Config"), "Failed to load image filter config!");
                return false;
            }
            if (!loadPointCloudFilterConfig(config, depth_filter_config.point_cloud_filter_config)) {
                RCLCPP_ERROR(rclcpp::get_logger("Config"), "Failed to load point cloud filter config!");
                return false;
            }
            if (!config["point_cloud_publisher_name"]) {
                RCLCPP_ERROR(rclcpp::get_logger("Config"), "Missing required field 'point_cloud_publisher_name' in config!");
                return false;
            }
            depth_filter_config.point_cloud_publisher_name = config["point_cloud_publisher_name"].as<std::string>(); 
            return true;
        } catch (const std::exception& e) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "Error loading config: %s", e.what());
            return false;
        }
    }

    bool Config::loadCliffDetectorConfig(const std::string &config_path, CliffDetectorConfig &cliff_detector_config) {
        try {
            YAML::Node config = YAML::LoadFile(config_path);
            if (!loadCliffDetectorConfig(config, cliff_detector_config)) {
                RCLCPP_ERROR(rclcpp::get_logger("Config"), "Failed to load cliff detector config!");
                return false;
            }
            return true;
        } catch (const std::exception& e) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "Error loading config: %s", e.what());
            return false;
        }
    }

    bool Config::loadCameraDevices(const YAML::Node& config,
                                 std::vector<CameraDevice>& devices) {
        if (!config["camera_devices"]) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "No camera_devices found in config!");
            return false;
        }
        devices.clear();
        for (const auto& device_node : config["camera_devices"]) {
            try {
                CameraDevice device = CameraDevice::fromYAML(device_node);
                devices.push_back(device);
                
                RCLCPP_INFO(rclcpp::get_logger("Config"), "Camera: %s, Depth Topic: %s, Info Topic: %s", 
                    device.description.c_str(), device.depth_image_topic_name.c_str(), device.camera_info_topic_name.c_str());
                
            } catch (const std::exception& e) {
                RCLCPP_ERROR(rclcpp::get_logger("Config"), "Failed to parse camera device: %s", e.what());
                return false;
            }
        }

        return true;
    }

    bool Config::loadImageFilterConfig(const YAML::Node& config,
                                ImageFilterConfig& filter_config) {
        if (!config["image_filter"]) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "No image_filter found in config!");
            return false;
        }

        auto filter_node = config["image_filter"];
        if (!loadArtifactFilter(filter_node, filter_config.artifact_filter)) {
            return false;
        }
        if (!loadCropFilter(filter_node, filter_config.crop_filter)) {
            return false;
        }
        if (!loadNoiseFilter(filter_node, filter_config.noise_filter)) {
            return false;
        }
        return true;
    }
    
    bool Config::loadPointCloudFilterConfig(const YAML::Node& config,
                                          PointCloudFilterConfig& filter_config) {
        if (!config["point_cloud_filter"]) {
            RCLCPP_WARN(rclcpp::get_logger("Config"), "No point_cloud_filter found in config, using defaults!");
            return true;
        }
        
        auto filter_node = config["point_cloud_filter"];
        
        if (filter_node["voxel_filter"]) {
            if (!loadVoxelFilter(filter_node, filter_config.voxel_filter)) {
                return false;
            }
        }
        
        if (filter_node["ground_filter"]) {
            auto ground_config = filter_node["ground_filter"];
            filter_config.ground_filter.enable = ground_config["enable"].as<bool>();
            filter_config.ground_filter.max_z = ground_config["max_z"].as<float>();
            filter_config.ground_filter.distance_threshold = ground_config["distance_threshold"].as<float>();
            filter_config.ground_filter.angle_threshold = ground_config["angle_threshold"].as<float>();
            
            RCLCPP_INFO(rclcpp::get_logger("Config"), 
                "Ground filter: enable=%d, max_z=%.3f, distance_threshold=%.3f, angle_threshold=%.3f",
                filter_config.ground_filter.enable, filter_config.ground_filter.max_z, filter_config.ground_filter.distance_threshold, 
                filter_config.ground_filter.angle_threshold);
        }
        
        if (filter_node["passthrough"]) {
            auto passthrough_config = filter_node["passthrough"];
            filter_config.passthrough_filter.enable = passthrough_config["enable"].as<bool>();
            
            if (passthrough_config["min_x"]) {
                filter_config.passthrough_filter.min_x = passthrough_config["min_x"].as<float>();
            }
            if (passthrough_config["max_x"]) {
                filter_config.passthrough_filter.max_x = passthrough_config["max_x"].as<float>();
            }
            
            if (passthrough_config["min_y"]) {
                filter_config.passthrough_filter.min_y = passthrough_config["min_y"].as<float>();
            }
            if (passthrough_config["max_y"]) {
                filter_config.passthrough_filter.max_y = passthrough_config["max_y"].as<float>();
            }
            
            if (passthrough_config["min_z"]) {
                filter_config.passthrough_filter.min_z = passthrough_config["min_z"].as<float>();
            }
            if (passthrough_config["max_z"]) {
                filter_config.passthrough_filter.max_z = passthrough_config["max_z"].as<float>();
            }
            
            RCLCPP_INFO(rclcpp::get_logger("Config"), 
                "Passthrough filter: enable=%d, x=[%.2f, %.2f], y=[%.2f, %.2f], z=[%.2f, %.2f]",
                filter_config.passthrough_filter.enable,  
                filter_config.passthrough_filter.min_x, filter_config.passthrough_filter.max_x,
                filter_config.passthrough_filter.min_y, filter_config.passthrough_filter.max_y,
                filter_config.passthrough_filter.min_z, filter_config.passthrough_filter.max_z);
        }
        
        return true;
    }

    bool Config::loadArtifactFilter(const YAML::Node& config,
                                  ArtifactFilterConfig& artifact) {
        if (!config["artifact_filter"]) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "No artifact filter config found!");
            return false;
        }

        auto artifact_config = config["artifact_filter"];
        artifact.enable = artifact_config["enable"].as<bool>();
        artifact.min_depth = artifact_config["min_depth"].as<float>();
        artifact.max_depth = artifact_config["max_depth"].as<float>();

        RCLCPP_INFO(rclcpp::get_logger("Config"), 
            "Artifact filter: enable=%d, min_depth=%.2f, max_depth=%.2f",
            artifact.enable, artifact.min_depth, artifact.max_depth);

        return true;
    }

    bool Config::loadCropFilter(const YAML::Node& config,
                              CropFilterConfig& crop) {
        if (!config["crop_filter"]) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "No crop filter config found!");
            return false;
        }

        auto crop_config = config["crop_filter"];
        crop.enable = crop_config["enable"].as<bool>();
        crop.min_x = crop_config["min_x"].as<int>();
        crop.max_x = crop_config["max_x"].as<int>();
        crop.min_y = crop_config["min_y"].as<int>();
        crop.max_y = crop_config["max_y"].as<int>();

        RCLCPP_INFO(rclcpp::get_logger("Config"), 
            "Crop filter: enable=%d, min_x=%d, max_x=%d, min_y=%d, max_y=%d",
            crop.enable, crop.min_x, crop.max_x, crop.min_y, crop.max_y);

        return true;
    }

    bool Config::loadNoiseFilter(const YAML::Node& config,
                               NoiseFilterConfig& noise) {
        if (!config["noise_filter"]) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "No noise filter config found!");
            return false;
        }

        auto noise_config = config["noise_filter"];
        noise.enable = noise_config["enable"].as<bool>();

        RCLCPP_INFO(rclcpp::get_logger("Config"), 
            "Noise filter: enable=%d",
            noise.enable);

        return true;
    }

    bool Config::loadCliffDetectorConfig(const YAML::Node &config, CliffDetectorConfig &cliff_detector_config) {
        if (!config["cliff_detector"]) {
            RCLCPP_ERROR(rclcpp::get_logger("Config"), "No cliff detector config found!");
            return false;
        }

        auto cliff_detector_config_node = config["cliff_detector"];
        cliff_detector_config.enable = cliff_detector_config_node["enable"].as<bool>();
        cliff_detector_config.depth_image_step = cliff_detector_config_node["depth_image_step"].as<int>();
        cliff_detector_config.range_min = cliff_detector_config_node["range_min"].as<float>();
        cliff_detector_config.range_max = cliff_detector_config_node["range_max"].as<float>();
        cliff_detector_config.width_range = cliff_detector_config_node["width_range"].as<float>();
        cliff_detector_config.height_variance_threshold = cliff_detector_config_node["height_variance_threshold"].as<float>();
        cliff_detector_config.depth_image_topic_name = 
            cliff_detector_config_node["depth_image_topic_name"].as<std::string>();
        cliff_detector_config.camera_info_topic_name = 
            cliff_detector_config_node["camera_info_topic_name"].as<std::string>();
        cliff_detector_config.cliff_cloud_publisher_name = 
            cliff_detector_config_node["cliff_cloud_publisher_name"].as<std::string>();
        
        if (cliff_detector_config_node["extrinsics"]) {
            std::vector<float> extrinsic_values = 
                cliff_detector_config_node["extrinsics"].as<std::vector<float>>();
            if (extrinsic_values.size() != 16) {
                RCLCPP_ERROR(rclcpp::get_logger("Config"), 
                    "Extrinsics matrix must have 16 elements!");
                return false;
            }
            cliff_detector_config.extrinsics = 
                Eigen::Map<Eigen::Matrix<float, 4, 4, Eigen::RowMajor>>(extrinsic_values.data());
        } else {
            cliff_detector_config.extrinsics.setIdentity();
            RCLCPP_WARN(rclcpp::get_logger("Config"), 
                "No extrinsics found, using identity matrix");
        }

        RCLCPP_INFO(rclcpp::get_logger("Config"), 
            "Cliff Detector Config loaded:\n"
            "  enable: %d\n"
            "  depth_image_step: %d\n"
            "  range: [%.2f, %.2f]\n"
            "  width_range: %.2f\n"
            "  height_variance_threshold: %.2f\n",
            cliff_detector_config.enable,
            cliff_detector_config.depth_image_step,
            cliff_detector_config.range_min,
            cliff_detector_config.range_max,
            cliff_detector_config.width_range,
            cliff_detector_config.height_variance_threshold);

        return true;
    }

    bool Config::loadVoxelFilter(const YAML::Node& config,
                               VoxelFilterConfig& voxel) {
        if (!config["voxel_filter"]) {
            RCLCPP_WARN(rclcpp::get_logger("Config"), "No voxel filter config found, using defaults!");
            return true;
        }

        auto voxel_config = config["voxel_filter"];
        voxel.enable = voxel_config["enable"].as<bool>();
        
        if (voxel_config["leaf_size_x"]) {
            voxel.leaf_size_x = voxel_config["leaf_size_x"].as<float>();
        }
        if (voxel_config["leaf_size_y"]) {
            voxel.leaf_size_y = voxel_config["leaf_size_y"].as<float>();
        }
        if (voxel_config["leaf_size_z"]) {
            voxel.leaf_size_z = voxel_config["leaf_size_z"].as<float>();
        }

        RCLCPP_INFO(rclcpp::get_logger("Config"), 
            "Voxel filter: enable=%d, leaf_size=[%.3f, %.3f, %.3f]",
            voxel.enable, voxel.leaf_size_x, voxel.leaf_size_y, voxel.leaf_size_z);

        return true;
    }
}
