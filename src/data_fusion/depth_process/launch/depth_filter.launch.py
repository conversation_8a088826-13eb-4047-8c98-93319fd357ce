from launch import LaunchDescription
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():
    pkg_dir = get_package_share_directory('depth_process')
    config_file = os.path.join(pkg_dir, 'cfg', 'depthcam.yaml')

    depth_filter_node = Node(
        package='depth_process',
        executable='depth_filter_node',
        name='depth_filter_node',
        parameters=[{
            'config_path': config_file
        }],
        prefix=['taskset -c 4-7 '],
        output='screen'
    )
    
    cliff_detector_node = Node(
        package='depth_process',
        executable='cliff_detector_node',
        name='cliff_detector_node',
        parameters=[{
            'config_path': config_file
        }],
        prefix=['taskset -c 4-7 '],
        output='screen'
    )

    return LaunchDescription([
        depth_filter_node,
        cliff_detector_node
    ])
