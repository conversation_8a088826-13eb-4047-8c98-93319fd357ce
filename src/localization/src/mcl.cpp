#include <localization/mcl.h>
#include <rclcpp/rclcpp.hpp>
#include "ament_index_cpp/get_package_share_directory.hpp"
#include <string>

namespace localization {

MCL::MCL(const rclcpp::NodeOptions & options)
    : nav2_util::LifecycleNode("mcl", "", options)
    , tfBaseLink2LaserValid_(false)
    , gotMap_(false)
    , gotScan_(false)
    , paramsInitialized_(false)
    , isFirstPose_(true)
{
    RCLCPP_INFO(get_logger(), "Creating");
}

MCL::~MCL()
{
    //
}

nav2_util::CallbackReturn
MCL::on_configure(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Configuring");
    
    // 初始化参数
    initParameters();
    
    // 初始化发布者和订阅者
    initPubSub();
    
    // 初始化 TF 相关
    initTransforms();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MCL::on_activate(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Activating");
    
    // 激活所有生命周期发布者
    posePub_->on_activate();
    particlesPub_->on_activate();
    if (publishUnknownScan_) {
        unknownScanPub_->on_activate();
    }
    if (publishResidualErrors_) {
        residualErrorsPub_->on_activate();
    }
    if (estimateReliability_) {
        reliabilityPub_->on_activate();
        reliabilityMarkerPub_->on_activate();
    }

    // 创建定时器执行主循环
    double localizationHz = getLocalizationHz();
    std::chrono::milliseconds timer_period{static_cast<int>(1000.0 / localizationHz)};
    worker_timer_ = create_wall_timer(
        timer_period,
        std::bind(&MCL::updateCallback, this)
    );
    
    // 创建与 lifecycle manager 的连接
    createBond();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MCL::on_deactivate(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Deactivating");
    worker_timer_->cancel();
    // 停用所有生命周期发布者
    posePub_->on_deactivate();
    particlesPub_->on_deactivate();
    if (publishUnknownScan_) {
        unknownScanPub_->on_deactivate();
    }
    if (publishResidualErrors_) {
        residualErrorsPub_->on_deactivate();
    }
    if (estimateReliability_) {
        reliabilityPub_->on_deactivate();
        reliabilityMarkerPub_->on_deactivate();
    }
    
    // 断开与 lifecycle manager 的连接
    destroyBond();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MCL::on_cleanup(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Cleaning up");
    
    // 清理资源
    posePub_.reset();
    particlesPub_.reset();
    unknownScanPub_.reset();
    residualErrorsPub_.reset();
    reliabilityPub_.reset();
    reliabilityMarkerPub_.reset();
    
    scanSub_.reset();
    odomSub_.reset();
    mapSub_.reset();
    initialPoseSub_.reset();
    if (useGLPoseSampler_) {
        glSampledPosesSub_.reset();
    }
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MCL::on_shutdown(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Shutting down");
    return nav2_util::CallbackReturn::SUCCESS;
}

void MCL::initParameters()
{
    // topic and frame names
    this->declare_parameter("scan_name", "/fusion_scan");
    this->declare_parameter("odom_name", "/odom");
    this->declare_parameter("map_name", "/map");
    this->declare_parameter("pose_name", "/amcl_pose");
    this->declare_parameter("particles_name", "/mcl_particles");
    this->declare_parameter("unknown_scan_name", "/unknown_scan");
    this->declare_parameter("residual_errors_name", "/residual_errors");
    this->declare_parameter("reliability_name", "/reliability");
    this->declare_parameter("reliability_marker_name", "/reliability_marker_name");
    this->declare_parameter("gl_sampled_poses_name", "/gl_sampled_poses");
    this->declare_parameter("laser_frame", "base_link");
    this->declare_parameter("base_link_frame", "base_link");
    this->declare_parameter("map_frame", "map");
    this->declare_parameter("odom_frame", "odom");
    this->declare_parameter("broadcast_tf", true);
    this->declare_parameter("use_odom_tf", true);

    // particle filter parameters
    this->declare_parameter("initial_pose_x", -7.09);
    this->declare_parameter("initial_pose_y", 4.53);
    this->declare_parameter("initial_pose_yaw", -1.21);
    this->declare_parameter("initial_noise_x", 0.02);
    this->declare_parameter("initial_noise_y", 0.02);
    this->declare_parameter("initial_noise_yaw", 0.01);
    this->declare_parameter("particle_num", 1000);
    this->declare_parameter("use_augmented_mcl", false);
    this->declare_parameter("alpha_slow", 0.001);
    this->declare_parameter("alpha_fast", 0.99);
    this->declare_parameter("add_random_particles_in_resampling", true);
    this->declare_parameter("random_particles_rate", 0.1);

    // motion model
    this->declare_parameter("use_omni_directional_model", false);
    this->declare_parameter("odom_noise_ddm", std::vector<double>{1.0, 0.5, 0.5, 1.5});
    this->declare_parameter("odom_noise_odm", std::vector<double>{4.0, 1.0, 1.0, 1.0, 4.0, 1.0, 1.0, 1.0, 8.0});
    this->declare_parameter("random_particles_noise", std::vector<double>{0.05, 0.05, 0.1});

    // measurement model
    this->declare_parameter("measurement_model_type", 2);
    this->declare_parameter("scan_step", 5);
    this->declare_parameter("z_hit", 0.9);
    this->declare_parameter("z_short", 0.2);
    this->declare_parameter("z_max", 0.05);
    this->declare_parameter("z_rand", 0.05);
    this->declare_parameter("var_hit", 0.08);
    this->declare_parameter("lambda_short", 1.0);
    this->declare_parameter("lambda_unknown", 0.01);
    this->declare_parameter("known_class_prior", 0.5);
    this->declare_parameter("unknown_scan_prob_threshold", 0.9);
    this->declare_parameter("reject_unknown_scan", false);
    this->declare_parameter("publish_unknown_scan", true);
    this->declare_parameter("publish_residual_errors", true);
    this->declare_parameter("resample_threshold_ess", 0.5);
    this->declare_parameter("resample_thresholds", std::vector<double>{0.2, 0.2, 0.2, 0.02, -99999.0});

    // reliability estimation
    this->declare_parameter("estimate_reliability", false);
    this->declare_parameter("rel_trans_ddm", std::vector<double>{0.0, 0.0});
    this->declare_parameter("rel_trans_odm", std::vector<double>{0.0, 0.0, 0.0});

    std::string pkg_share_dir = ament_index_cpp::get_package_share_directory("localization");
    std::string classifier_dir = pkg_share_dir + "/classifiers/MAE/";
    this->declare_parameter("mae_classifier_dir", classifier_dir);  // classifier_dir will be set externally
    this->declare_parameter("classifier_type", 0);

    // global localization
    this->declare_parameter("use_gl_pose_sampler", false);
    this->declare_parameter("fuse_gl_pose_sampler_only_unreliable", false);
    this->declare_parameter("gl_sampled_pose_time_th", 1.0);
    this->declare_parameter("gmm_positional_variance", 0.01);
    this->declare_parameter("gmm_angular_variance", 0.01);
    this->declare_parameter("pred_dist_unif_rate", 0.05);

    // other parameters
    this->declare_parameter("localization_hz", 15.0);
    this->declare_parameter("transform_tolerance", 1.0);

    this->declare_parameter("update_dist_thresh", 0.1);  // 5cm
    this->declare_parameter("update_yaw_thresh", 0.085); //5 degree to radian

    // 获取参数值
    this->get_parameter("scan_name", scanName_);
    this->get_parameter("odom_name", odomName_);
    this->get_parameter("map_name", mapName_);
    this->get_parameter("pose_name", poseName_);
    this->get_parameter("particles_name", particlesName_);
    this->get_parameter("unknown_scan_name", unknownScanName_);
    this->get_parameter("residual_errors_name", residualErrorsName_);
    this->get_parameter("reliability_name", reliabilityName_);
    this->get_parameter("reliability_marker_name", reliabilityMarkerName_);
    this->get_parameter("gl_sampled_poses_name", glSampledPosesName_);
    this->get_parameter("laser_frame", laserFrame_);
    this->get_parameter("base_link_frame", baseLinkFrame_);
    this->get_parameter("map_frame", mapFrame_);
    this->get_parameter("odom_frame", odomFrame_);
    this->get_parameter("broadcast_tf", broadcastTF_);
    this->get_parameter("use_odom_tf", useOdomTF_);

    this->get_parameter("initial_pose_x", initialPoseX_);
    this->get_parameter("initial_pose_y", initialPoseY_);
    this->get_parameter("initial_pose_yaw", initialPoseYaw_);
    this->get_parameter("initial_noise_x", initialNoiseX_);
    this->get_parameter("initial_noise_y", initialNoiseY_);
    this->get_parameter("initial_noise_yaw", initialNoiseYaw_);
    this->get_parameter("particle_num", particlesNum_);
    this->get_parameter("use_augmented_mcl", useAugmentedMCL_);
    RCLCPP_INFO(this->get_logger(), "use_augmented_mcl: %d", useAugmentedMCL_);
    this->get_parameter("alpha_slow", alphaSlow_);
    this->get_parameter("alpha_fast", alphaFast_);
    this->get_parameter("add_random_particles_in_resampling", addRandomParticlesInResampling_);
    this->get_parameter("random_particles_rate", randomParticlesRate_);
    this->get_parameter("random_particles_noise", randomParticlesNoise_);

    this->get_parameter("use_omni_directional_model", useOmniDirectionalModel_);
    this->get_parameter("odom_noise_ddm", odomNoiseDDM_);
    RCLCPP_INFO(this->get_logger(), "odom_noise_ddm: %f, %f, %f, %f", odomNoiseDDM_[0], odomNoiseDDM_[1], odomNoiseDDM_[2], odomNoiseDDM_[3]);
    this->get_parameter("odom_noise_odm", odomNoiseODM_);
    this->get_parameter("rel_trans_ddm", relTransDDM_);
    this->get_parameter("rel_trans_odm", relTransODM_);

    this->get_parameter("measurement_model_type", measurementModelType_);
    this->get_parameter("scan_step", scanStep_);
    this->get_parameter("z_hit", zHit_);
    this->get_parameter("z_short", zShort_);
    this->get_parameter("z_max", zMax_);
    this->get_parameter("z_rand", zRand_);
    this->get_parameter("var_hit", varHit_);
    this->get_parameter("lambda_short", lambdaShort_);
    this->get_parameter("lambda_unknown", lambdaUnknown_);
    this->get_parameter("known_class_prior", pKnownPrior_);
    this->get_parameter("unknown_scan_prob_threshold", unknownScanProbThreshold_);
    this->get_parameter("reject_unknown_scan", rejectUnknownScan_);
    RCLCPP_INFO(this->get_logger(), "reject_unknown_scan: %d", rejectUnknownScan_);
    this->get_parameter("publish_unknown_scan", publishUnknownScan_);
    this->get_parameter("publish_residual_errors", publishResidualErrors_);
    this->get_parameter("resample_threshold_ess", resampleThresholdESS_);
    this->get_parameter("resample_thresholds", resampleThresholds_);
    RCLCPP_INFO(this->get_logger(), "resample_thresholds: %f, %f, %f, %f, %f", resampleThresholds_[0], resampleThresholds_[1], resampleThresholds_[2], resampleThresholds_[3], resampleThresholds_[4]);

    this->get_parameter("estimate_reliability", estimateReliability_);
    RCLCPP_INFO(this->get_logger(), "estimate_reliability: %d", estimateReliability_);
    this->get_parameter("classifier_type", classifierType_);
    this->get_parameter("mae_classifier_dir", maeClassifierDir_);

    this->get_parameter("use_gl_pose_sampler", useGLPoseSampler_);
    RCLCPP_INFO(this->get_logger(), "use_gl_pose_sampler: %d", useGLPoseSampler_);
    this->get_parameter("fuse_gl_pose_sampler_only_unreliable", fuseGLPoseSamplerOnlyUnreliable_);
    this->get_parameter("gl_sampled_pose_time_th", glSampledPoseTimeTH_);
    this->get_parameter("gmm_positional_variance", gmmPositionalVariance_);
    this->get_parameter("gmm_angular_variance", gmmAngularVariance_);
    this->get_parameter("pred_dist_unif_rate", predDistUnifRate_);

    this->get_parameter("localization_hz", localizationHz_);
    this->get_parameter("transform_tolerance", transformTolerance_);
    this->get_parameter("update_dist_thresh", update_dist_thresh_);
    this->get_parameter("update_yaw_thresh", update_yaw_thresh_);

    pUnknownPrior_ = 1.0 - pKnownPrior_;
    // degree to radian
    initialPoseYaw_ *= M_PI / 180.0;

    // set initial pose
    mclPose_.setPose(initialPoseX_, initialPoseY_, initialPoseYaw_);

    resetParticlesDistribution();
    odomPose_.setPose(0.0, 0.0, 0.0);
    deltaX_ = deltaY_ = deltaDist_ = deltaYaw_ = 0.0;

    // init some parameters
    normConstHit_ = 1.0 / sqrt(2.0 * varHit_ * M_PI);
    denomHit_ = 1.0 / (2.0 * varHit_);

    // 如果启用可靠性估计，初始化相关参数
    if (estimateReliability_) {
        resetReliabilities();
        if (classifierType_ == 0) {
            maeClassifier_.setClassifierDir(maeClassifierDir_);
            maeClassifier_.readClassifierParams();
            maes_.resize(particlesNum_);
        } else {
            RCLCPP_ERROR(this->get_logger(),
                "Incorrect classifier type was selected. The expected type is 0, but %d was selected.",
                classifierType_);
            throw std::runtime_error("Invalid classifier type");
        }
    }

    RCLCPP_INFO(this->get_logger(), "Parameters initialized");
}

void MCL::initPubSub()
{
    // 创建订阅者
    scanSub_ = create_subscription<LaserT>(
        scanName_, 10, std::bind(&MCL::scanCB, this, std::placeholders::_1));
    
    odomSub_ = create_subscription<OdomT>(
        odomName_, 100, std::bind(&MCL::odomCB, this, std::placeholders::_1));
    
    mapSub_ = create_subscription<MapT>(
        mapName_, 1, std::bind(&MCL::mapCB, this, std::placeholders::_1));
    
    initialPoseSub_ = create_subscription<PoseCovStampT>(
        "initialpose", 1, std::bind(&MCL::initialPoseCB, this, std::placeholders::_1));
    
    if (useGLPoseSampler_) {
        glSampledPosesSub_ = create_subscription<PoseArrayT>(
            glSampledPosesName_, 1, std::bind(&MCL::glSampledPosesCB, this, std::placeholders::_1));
    }
    // 创建生命周期发布者
    posePub_ = create_publisher<PoseStampT>(poseName_, 1);
    particlesPub_ = create_publisher<PoseArrayT>(particlesName_, 1);
    
    if (publishUnknownScan_) {
        unknownScanPub_ = create_publisher<LaserT>(unknownScanName_, 1);
    }
    
    if (publishResidualErrors_) {
        residualErrorsPub_ = create_publisher<LaserT>(residualErrorsName_, 1);
    }
    
    if (estimateReliability_) {
        reliabilityPub_ = create_publisher<ReliabilityT>(reliabilityName_, 1);
        reliabilityMarkerPub_ = create_publisher<MarkerT>(reliabilityMarkerName_, 1);
    }
}

void MCL::initTransforms()
{
    auto timer_interface = std::make_shared<tf2_ros::CreateTimerROS>(
        this->get_node_base_interface(),
        this->get_node_timers_interface());

    tf_buffer_ = std::make_unique<tf2_ros::Buffer>(this->get_clock());
    tf_buffer_->setCreateTimerInterface(timer_interface);
    tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
    tfBroadcaster_ = std::make_unique<tf2_ros::TransformBroadcaster>(this);
}

bool MCL::checkTransform()
{
    try {
        tfBaseLink2Laser_ = tf_buffer_->lookupTransform(
            baseLinkFrame_, laserFrame_, tf2::TimePointZero);
    }
    catch (tf2::TransformException &ex) {
        return false;
    }
    tf2::Quaternion quatBaseLink2Laser(
        tfBaseLink2Laser_.transform.rotation.x,
        tfBaseLink2Laser_.transform.rotation.y,
        tfBaseLink2Laser_.transform.rotation.z,
        tfBaseLink2Laser_.transform.rotation.w);

    double baseLink2LaserRoll, baseLink2LaserPitch, baseLink2LaserYaw;
    tf2::Matrix3x3 rotMatBaseLink2Laser(quatBaseLink2Laser);
    rotMatBaseLink2Laser.getRPY(baseLink2LaserRoll, baseLink2LaserPitch, baseLink2LaserYaw);
    baseLink2Laser_.setX(tfBaseLink2Laser_.transform.translation.x);
    baseLink2Laser_.setY(tfBaseLink2Laser_.transform.translation.y);
    baseLink2Laser_.setYaw(baseLink2LaserYaw);

    RCLCPP_INFO(get_logger(), "Transform from %s to %s is ready!",
        baseLinkFrame_.c_str(), laserFrame_.c_str());

    tfBaseLink2LaserValid_ = true;
    return true;
}

void MCL::predictMclPoseByOdomDelta(Pose &pose) {
    double deltaX, deltaY, deltaYaw;
    {
        std::lock_guard<std::mutex> lock(odom_mutex_);
        deltaX = deltaX_;
        deltaY = deltaY_;
        deltaYaw = deltaYaw_;
    }

    double last_x = mclPose_.getX();
    double last_y = mclPose_.getY();
    double last_yaw = mclPose_.getYaw();

    double t = last_yaw + deltaYaw / 2.0;
    double new_x = last_x + deltaX * cos(t) - deltaY * sin(t);
    double new_y = last_y + deltaX * sin(t) + deltaY * cos(t);
    double new_yaw = last_yaw + deltaYaw;

    while (new_yaw < -M_PI) new_yaw += 2.0 * M_PI;
    while (new_yaw > M_PI) new_yaw -= 2.0 * M_PI;

    pose.setPose(new_x, new_y, new_yaw);
}

void MCL::updateCallback()
{
    if(!tfBaseLink2LaserValid_) {
        RCLCPP_INFO_THROTTLE(get_logger(), *(this->get_clock()), 1000, "Waiting for BaseLink2Laser tf...");
        tfBaseLink2LaserValid_ = checkTransform();
        return;
    }

    if(!gotMap_.load()) {
        RCLCPP_INFO_THROTTLE(get_logger(), *(this->get_clock()), 1000, "Waiting for map...");
        return;
    }

    if(!gotScan_.load()) {
        RCLCPP_INFO_THROTTLE(get_logger(), *(this->get_clock()), 1000, "Waiting for scan...");
        return;
    }

    if(!need_update_.load()) {
        Pose currentPose;
        predictMclPoseByOdomDelta(currentPose);
        mclPoseStamp_ = scan_.header.stamp;
        publishROSMessages(currentPose, mclPoseStamp_);
        broadcastTF(currentPose);
        return;
    }

    if(!paramsInitialized_) {
        pRand_ = 1.0 / (scan_.range_max / mapResolution_);
        measurementModelRandom_ = zRand_ * pRand_;
        measurementModelInvalidScan_ = zMax_ + zRand_ * pRand_;
        paramsInitialized_ = true;
    }

    if(isScanMightInvalid(scan_)) {
        RCLCPP_INFO(get_logger(), "Scan is invalid.");
        return;
    }

    updateParticlesByMotionModel();
    setCanUpdateScan(false);
    calculateLikelihoodsByMeasurementModel();
    calculateLikelihoodsByDecisionModel();
    calculateGLSampledPosesLikelihood();
    calculateAMCLRandomParticlesRate();
    calculateEffectiveSampleSize();
    estimatePose();
    resampleParticles();
    // plotScan();
    // plotWorld(50->0);
    publishROSMessages(mclPose_, mclPoseStamp_);
    broadcastTF(mclPose_);
    // plotLikelihoodMap();
    setCanUpdateScan(true);
    //printResult();
    need_update_.store(false);
}

void MCL::updateParticlesByMotionModel() {
    double deltaX = 0.0;
    double deltaY = 0.0;
    double deltaDist = 0.0;
    double deltaYaw = 0.0;
    {
        std::lock_guard<std::mutex> lock(odom_mutex_);
        deltaX = deltaX_;
        deltaY = deltaY_;
        deltaDist = deltaDist_;
        deltaYaw = deltaYaw_;
        deltaX_ = deltaY_ = deltaDist_ = deltaYaw_ = 0.0;
        deltaXSum_ += fabs(deltaX);
        deltaYSum_ += fabs(deltaY);
        deltaDistSum_ += fabs(deltaDist);
        deltaYawSum_ += fabs(deltaYaw);
    }

    RCLCPP_INFO(get_logger(), "updateParticlesByMotionModel");

    if (!useOmniDirectionalModel_) {
        // differential drive model
        double yaw = mclPose_.getYaw();
        double t = yaw + deltaYaw / 2.0;
        double x = mclPose_.getX() + deltaDist * cos(t);
        double y = mclPose_.getY() + deltaDist * sin(t);
        yaw += deltaYaw;
        mclPose_.setPose(x, y, yaw);
        double dist2 = deltaDist * deltaDist;
        double yaw2 = deltaYaw * deltaYaw;
        double distRandVal = dist2 * odomNoiseDDM_[0] + yaw2 * odomNoiseDDM_[1];
        double yawRandVal = dist2 * odomNoiseDDM_[2] + yaw2 * odomNoiseDDM_[3];
        for (int i = 0; i < particlesNum_; ++i) {
            double ddist = deltaDist + nrand(distRandVal);
            double dyaw = deltaYaw + nrand(yawRandVal);
            double yaw = particles_[i].getYaw();
            double t = yaw + dyaw / 2.0;
            double x = particles_[i].getX() + ddist * cos(t);
            double y = particles_[i].getY() + ddist * sin(t);
            yaw += dyaw;
            particles_[i].setPose(x, y, yaw);
            if (estimateReliability_) {
                double decayRate = 1.0 - (relTransDDM_[0] * ddist * ddist +
                                            relTransDDM_[1] * dyaw * dyaw);
                if (decayRate <= 0.0)
                    decayRate = 10.0e-6;
                reliabilities_[i] *= decayRate;
            }
        }
    } else {
        // omni directional model
        double yaw = mclPose_.getYaw();
        double t = yaw + deltaYaw / 2.0;
        double x = mclPose_.getX() + deltaX * cos(t) + deltaY * cos(t + M_PI / 2.0f);
        double y = mclPose_.getY() + deltaX * sin(t) + deltaY * sin(t + M_PI / 2.0f);
        yaw += deltaYaw;
        mclPose_.setPose(x, y, yaw);
        double x2 = deltaX * deltaX;
        double y2 = deltaY * deltaY;
        double yaw2 = deltaYaw * deltaYaw;
        double xRandVal = x2 * odomNoiseODM_[0] + y2 * odomNoiseODM_[1] +
                            yaw2 * odomNoiseODM_[2];
        double yRandVal = x2 * odomNoiseODM_[3] + y2 * odomNoiseODM_[4] +
                            yaw2 * odomNoiseODM_[5];
        double yawRandVal = x2 * odomNoiseODM_[6] + y2 * odomNoiseODM_[7] +
                            yaw2 * odomNoiseODM_[8];
        for (int i = 0; i < particlesNum_; ++i) {
            double dx = deltaX + nrand(xRandVal);
            double dy = deltaY + nrand(yRandVal);
            double dyaw = deltaYaw + nrand(yawRandVal);
            double yaw = particles_[i].getYaw();
            double t = yaw + dyaw / 2.0;
            double x = particles_[i].getX() + dx * cos(t) + dy * cos(t + M_PI / 2.0f);
            double y = particles_[i].getY() + dx * sin(t) + dy * sin(t + M_PI / 2.0f);
            yaw += dyaw;
            particles_[i].setPose(x, y, yaw);
            if (estimateReliability_) {
                double decayRate = 1.0 - (relTransODM_[0] * dx * dx + relTransODM_[1] * dy * dy +
                        relTransODM_[2] * dyaw * dyaw);
                if (decayRate <= 0.0)
                    decayRate = 10.0e-6;
                reliabilities_[i] *= decayRate;
            }
        }
    }
}

void MCL::calculateLikelihoodsByMeasurementModel(void) {
    if (rejectUnknownScan_ && (measurementModelType_ == 0 || measurementModelType_ == 1))
        rejectUnknownScan();

    mclPoseStamp_ = scan_.header.stamp;
    double xo = baseLink2Laser_.getX();
    double yo = baseLink2Laser_.getY();
    double yawo = baseLink2Laser_.getYaw();
    std::vector<Pose> sensorPoses(particlesNum_);
    for (int i = 0; i < particlesNum_; ++i) {
        double yaw = particles_[i].getYaw();
        double sensorX = xo * cos(yaw) - yo * sin(yaw) + particles_[i].getX();
        double sensorY = xo * sin(yaw) + yo * cos(yaw) + particles_[i].getY();
        double sensorYaw = yawo + yaw;
        Pose sensorPose(sensorX, sensorY, sensorYaw);
        sensorPoses[i] = sensorPose;
        particles_[i].setW(0.0);
    }

    likelihoodShiftedSteps_.clear();
    for (int i = 0; i < (int)scan_.ranges.size(); i += scanStep_) {
        double range = scan_.ranges[i];
        double rangeAngle = (double)i * scan_.angle_increment + scan_.angle_min;
        double max = 0;
        for (int j = 0; j < particlesNum_; ++j) {
            double p;
            if (measurementModelType_ == 0)
                p = calculateLikelihoodFieldModel(sensorPoses[j], range, rangeAngle);
            else if (measurementModelType_ == 1)
                p = calculateBeamModel(sensorPoses[j], range, rangeAngle);
            else
                p = calculateClassConditionalMeasurementModel(sensorPoses[j], range,
                                                            rangeAngle);
            double w = particles_[j].getW();
            w += log(p);
            particles_[j].setW(w);
            if (j == 0) {
                max = w;
            } else {
                if (max < w)
                    max = w;
            }
        }

        // Too small values cannot be calculated.
        // The log sum values are shifted if the maximum value is less than
        // threshold.
        if (max < -300.0) {
            for (int j = 0; j < particlesNum_; ++j) {
                double w = particles_[j].getW() + 300.0;
                particles_[j].setW(w);
            }
            likelihoodShiftedSteps_.push_back(true);
        } else {
            likelihoodShiftedSteps_.push_back(false);
        }
    }

    double sum = 0.0;
    double max = 0;
    int maxIdx = 0;
    for (int i = 0; i < particlesNum_; ++i) {
        // The log sum is converted to the probability.
        double w = exp(particles_[i].getW());
        particles_[i].setW(w);
        sum += w;
        if (i == 0) {
            max = w;
            maxIdx = i;
        } else if (max < w) {
            max = w;
            maxIdx = i;
        }
    }
    totalLikelihood_ = sum;
    averageLikelihood_ = sum / (double)particlesNum_;
    maxLikelihood_ = max;
    maxLikelihoodParticleIdx_ = maxIdx;
}

void MCL::calculateLikelihoodsByDecisionModel(void) {
    if (!estimateReliability_)
        return;

    if (measurementModelType_ == 2 && classifierType_ == 0) {
        Pose mlPose = particles_[maxLikelihoodParticleIdx_].getPose();
        estimateUnknownScanWithClassConditionalMeasurementModel(mlPose);
    }

    double sum = 0.0;
    double max = 0;
    int maxIdx = 0;
    for (int i = 0; i < particlesNum_; ++i) {
        Pose particlePose = particles_[i].getPose();
        double measurementLikelihood = particles_[i].getW();
        std::vector<double> residualErrors =
            getResidualErrors<double>(particlePose);
        double decisionLikelihood = 0;
        if (classifierType_ == 0) {
            double mae = maeClassifier_.getMAE(residualErrors);
            decisionLikelihood =
                maeClassifier_.calculateDecisionModel(mae, &reliabilities_[i]);
            maes_[i] = mae;
        }
        double w = measurementLikelihood * decisionLikelihood;
        particles_[i].setW(w);
        sum += w;
        if (i == 0) {
            max = w;
            maxIdx = 0;
        } else if (max < w) {
            max = w;
            maxIdx = i;
        }
    }
    totalLikelihood_ = sum;
    averageLikelihood_ = sum / (double)particlesNum_;
    maxLikelihood_ = max;
    maxLikelihoodParticleIdx_ = maxIdx;
    reliability_ = reliabilities_[maxIdx];
}

void MCL::calculateGLSampledPosesLikelihood(void) {
    if (!useGLPoseSampler_)
        return;
    if (estimateReliability_ && fuseGLPoseSamplerOnlyUnreliable_) {
        if (reliability_ >= 0.9)
            return;
    }

    glParticlesNum_ = (int)glSampledPoses_.poses.size();
    double dt = fabs(tf2_ros::timeToSec(mclPoseStamp_) -
                    tf2_ros::timeToSec(glSampledPosesStamp_));
    if (dt > glSampledPoseTimeTH_ || glParticlesNum_ == 0 || !isGLSampledPosesUpdated_)
        return;

    canUpdateGLSampledPoses_ = isGLSampledPosesUpdated_ = false;
    double xo = baseLink2Laser_.getX();
    double yo = baseLink2Laser_.getY();
    double yawo = baseLink2Laser_.getYaw();
    std::vector<Pose> sensorPoses(glParticlesNum_);
    glParticles_.resize(glParticlesNum_);
    if (estimateReliability_) {
        glSampledPosesReliabilities_.resize(glParticlesNum_);
    if (classifierType_ == 0)
        glSampledPosesMAEs_.resize(glParticlesNum_);
    }

    for (int i = 0; i < glParticlesNum_; ++i) {
        tf2::Quaternion q(glSampledPoses_.poses[i].orientation.x,
                            glSampledPoses_.poses[i].orientation.y,
                            glSampledPoses_.poses[i].orientation.z,
                            glSampledPoses_.poses[i].orientation.w);
        double roll, pitch, yaw;
        tf2::Matrix3x3 m(q);
        m.getRPY(roll, pitch, yaw);
        glParticles_[i].setPose(glSampledPoses_.poses[i].position.x,
                                glSampledPoses_.poses[i].position.y, yaw);

        double sensorX = xo * cos(yaw) - yo * sin(yaw) + glParticles_[i].getX();
        double sensorY = xo * sin(yaw) + yo * cos(yaw) + glParticles_[i].getY();
        double sensorYaw = yawo + yaw;
        Pose sensorPose(sensorX, sensorY, sensorYaw);
        sensorPoses[i] = sensorPose;
        glParticles_[i].setW(0.0);
    }
    canUpdateGLSampledPoses_ = true;

    int idx = 0;
    for (int i = 0; i < (int)scan_.ranges.size(); i += scanStep_) {
        double range = scan_.ranges[i];
        double rangeAngle = (double)i * scan_.angle_increment + scan_.angle_min;
        double max;
        for (int j = 0; j < glParticlesNum_; ++j) {
            double p;
            if (measurementModelType_ == 0)
                p = calculateLikelihoodFieldModel(sensorPoses[j], range, rangeAngle);
            else if (measurementModelType_ == 1)
                p = calculateBeamModel(sensorPoses[j], range, rangeAngle);
            else
                p = calculateClassConditionalMeasurementModel(sensorPoses[j], range,
                                                            rangeAngle);
            double w = glParticles_[j].getW();
            w += log(p);
            glParticles_[j].setW(w);
        }

        if (likelihoodShiftedSteps_[idx]) {
            for (int j = 0; j < glParticlesNum_; ++j) {
                double w = glParticles_[j].getW() + 300.0;
                glParticles_[j].setW(w);
            }
        }
        idx++;
    }

    double normConst =
        1.0 / sqrt(2.0 * M_PI *
                (gmmPositionalVariance_ + gmmPositionalVariance_ +
                    gmmAngularVariance_));
    double angleResolution = 1.0 * M_PI / 180.0;
    double sum = totalLikelihood_;
    double max = maxLikelihood_;
    double gmmRate = 1.0 - predDistUnifRate_;
    int maxIdx = -1;
    for (int i = 0; i < glParticlesNum_; ++i) {
    // measurement likelihood
        double w = exp(glParticles_[i].getW());

        // decision likelihood
        if (estimateReliability_) {
            std::vector<double> residualErrors =
                getResidualErrors<double>(glParticles_[i].getPose());
            double decisionLikelihood = 0;
            glSampledPosesReliabilities_[i] = 0.5;
            if (classifierType_ == 0) {
                double mae = maeClassifier_.getMAE(residualErrors);
                decisionLikelihood = maeClassifier_.calculateDecisionModel(
                    mae, &glSampledPosesReliabilities_[i]);
                glSampledPosesMAEs_[i] = mae;
            }
            w *= decisionLikelihood;
        }

        // predictive distribution likelihood
        double gmmVal = 0.0;
        for (int j = 0; j < particlesNum_; ++j) {
            double dx = glParticles_[i].getX() - particles_[j].getX();
            double dy = glParticles_[i].getY() - particles_[j].getY();
            double dyaw = glParticles_[i].getYaw() - particles_[j].getYaw();
            while (dyaw < -M_PI)
            dyaw += 2.0 * M_PI;
            while (dyaw > M_PI)
            dyaw -= 2.0 * M_PI;
            gmmVal += normConst * exp(-((dx * dx) / (2.0 * gmmPositionalVariance_) +
                                (dy * dy) / (2.0 * gmmPositionalVariance_) +
                                (dyaw * dyaw) / (2.0 * gmmAngularVariance_)));
        }
        double pGMM = (double)glParticlesNum_ * gmmVal * mapResolution_ *
                        mapResolution_ * angleResolution / (double)particlesNum_;
        double predLikelihood = gmmRate * pGMM + predDistUnifRate_ * 10e-9;
        w *= predLikelihood;
        if (w > 1.0)
            w = 1.0;

        glParticles_[i].setW(w);
        sum += w;
        if (max < w) {
            max = w;
            maxIdx = i;
        }
    }

    if (std::isnan(sum)) {
        for (int i = 0; i < glParticlesNum_; ++i)
            glParticles_[i].setW(0.0);
        canUseGLSampledPoses_ = false;
        return;
    }

    totalLikelihood_ = sum;
    averageLikelihood_ = sum / (double)(particlesNum_ + glParticlesNum_);
    if (maxIdx >= 0) {
    maxLikelihood_ = max;
    maxLikelihoodParticleIdx_ = particlesNum_ + maxIdx;
    if (estimateReliability_)
        reliability_ = glSampledPosesReliabilities_[maxIdx];
    }
    canUseGLSampledPoses_ = true;
}

void MCL::calculateAMCLRandomParticlesRate(void) {
    if (!useAugmentedMCL_)
        return;
    omegaSlow_ += alphaSlow_ * (averageLikelihood_ - omegaSlow_);
    omegaFast_ += alphaFast_ * (averageLikelihood_ - omegaFast_);
    amclRandomParticlesRate_ = 1.0 - omegaFast_ / omegaSlow_;
    if (amclRandomParticlesRate_ < 0.0)
        amclRandomParticlesRate_ = 0.0;
}

void MCL::calculateEffectiveSampleSize(void) {
    double sum = 0.0;
    if (!useGLPoseSampler_ || !canUseGLSampledPoses_) {
        double wo = 1.0 / (double)particlesNum_;
        for (int i = 0; i < particlesNum_; ++i) {
            double w = particles_[i].getW() / totalLikelihood_;
            particles_[i].setW(w);
            sum += w * w;
        }
    } else {
        double wo = 1.0 / (double)(particlesNum_ + glParticlesNum_);
        for (int i = 0; i < particlesNum_; ++i) {
            double w = particles_[i].getW() / totalLikelihood_;
            particles_[i].setW(w);
            sum += w * w;
        }
        for (int i = 0; i < glParticlesNum_; ++i) {
            double w = glParticles_[i].getW() / totalLikelihood_;
            glParticles_[i].setW(w);
            sum += w * w;
        }
    }
    effectiveSampleSize_ = 1.0 / sum;
}

void MCL::estimatePose(void) {
    double tmpYaw = mclPose_.getYaw();
    double x = 0.0, y = 0.0, yaw = 0.0;
    double sum = 0.0;
    for (int i = 0; i < particlesNum_; ++i) {
        double w = particles_[i].getW();
        x += particles_[i].getX() * w;
        y += particles_[i].getY() * w;
        double dyaw = tmpYaw - particles_[i].getYaw();
        while (dyaw < -M_PI)
            dyaw += 2.0 * M_PI;
        while (dyaw > M_PI)
            dyaw -= 2.0 * M_PI;
        yaw += dyaw * w;
        sum += w;
    }

    if (useGLPoseSampler_ && canUseGLSampledPoses_) {
        double x2 = x, y2 = y, yaw2 = yaw;
        for (int i = 0; i < glParticlesNum_; ++i) {
            double w = glParticles_[i].getW();
            x += glParticles_[i].getX() * w;
            y += glParticles_[i].getY() * w;
            double dyaw = tmpYaw - glParticles_[i].getYaw();
            while (dyaw < -M_PI)
                dyaw += 2.0 * M_PI;
            while (dyaw > M_PI)
                dyaw -= 2.0 * M_PI;
            yaw += dyaw * w;
            sum += w;
        }
    if (sum > 1.0)
        x = x2, y = y2, yaw = yaw2;
    }

    yaw = tmpYaw - yaw;
    mclPose_.setPose(x, y, yaw);
}

void MCL::resampleParticles(void) {
    double threshold = (double)particlesNum_ * resampleThresholdESS_;
    if (useGLPoseSampler_ && canUseGLSampledPoses_)
        threshold = (double)(particlesNum_ + glParticlesNum_) * resampleThresholdESS_;
    if (effectiveSampleSize_ > threshold)
        return;

    if (deltaXSum_ < resampleThresholds_[0] &&
        deltaYSum_ < resampleThresholds_[1] &&
        deltaDistSum_ < resampleThresholds_[2] &&
        deltaYawSum_ < resampleThresholds_[3] &&
        deltaTimeSum_ < resampleThresholds_[4]) 
    {
        return;
    }

    deltaXSum_ = 0;
    deltaYSum_ = 0;
    deltaDistSum_ = 0;
    deltaYSum_ = 0;
    deltaTimeSum_ = 0.0;
    std::vector<double> wBuffer;
    if (useGLPoseSampler_ && canUseGLSampledPoses_) {
        wBuffer.resize(particlesNum_ + glParticlesNum_);
        wBuffer[0] = particles_[0].getW();
        for (int i = 1; i < particlesNum_; ++i)
            wBuffer[i] = particles_[i].getW() + wBuffer[i - 1];
        for (int i = 0; i < glParticlesNum_; ++i)
            wBuffer[particlesNum_ + i] = glParticles_[i].getW() + wBuffer[particlesNum_ + i - 1];
    } else {
        wBuffer.resize(particlesNum_);
        wBuffer[0] = particles_[0].getW();
        for (int i = 1; i < particlesNum_; ++i)
            wBuffer[i] = particles_[i].getW() + wBuffer[i - 1];
    }

    std::vector<Particle> tmpParticles = particles_;
    std::vector<double> tmpReliabilities;
    if (estimateReliability_)
        tmpReliabilities = reliabilities_;
    double wo = 1.0 / (double)particlesNum_;

    if (!addRandomParticlesInResampling_ && !useAugmentedMCL_) {
        // normal resampling
        for (int i = 0; i < particlesNum_; ++i) {
            double darts = (double)rand() / ((double)RAND_MAX + 1.0);
            bool isResampled = false;
            for (int j = 0; j < particlesNum_; ++j) {
                if (darts < wBuffer[j]) {
                    particles_[i].setPose(tmpParticles[j].getPose());
                    if (estimateReliability_)
                    reliabilities_[i] = tmpReliabilities[j];
                    particles_[i].setW(wo);
                    isResampled = true;
                    break;
                }
            }
            if (!isResampled && useGLPoseSampler_ && canUseGLSampledPoses_) {
                for (int j = 0; j < glParticlesNum_; ++j) {
                    if (darts < wBuffer[particlesNum_ + j]) {
                        particles_[i].setPose(glParticles_[j].getPose());
                        if (estimateReliability_)
                            reliabilities_[i] = glSampledPosesReliabilities_[j];
                        particles_[i].setW(wo);
                        break;
                    }
                }
            }
        }
    } else {
        // resampling and add random particles
        double randomParticlesRate = randomParticlesRate_;
        if (useAugmentedMCL_ && amclRandomParticlesRate_ > 0.0) {
            omegaSlow_ = omegaFast_ = 0.0;
            randomParticlesRate = amclRandomParticlesRate_;
        } else if (!addRandomParticlesInResampling_) {
            randomParticlesRate = 0.0;
        }
        int resampledParticlesNum = (int)((1.0 - randomParticlesRate) * (double)particlesNum_);
        int randomParticlesNum = particlesNum_ - resampledParticlesNum;
        for (int i = 0; i < resampledParticlesNum; ++i) {
            double darts = (double)rand() / ((double)RAND_MAX + 1.0);
            bool isResampled = false;
            for (int j = 0; j < particlesNum_; ++j) {
                if (darts < wBuffer[j]) {
                    particles_[i].setPose(tmpParticles[j].getPose());
                    if (estimateReliability_)
                        reliabilities_[i] = tmpReliabilities[j];
                    particles_[i].setW(wo);
                    isResampled = true;
                    break;
                }
            }
            if (!isResampled && useGLPoseSampler_ && canUseGLSampledPoses_) {
                for (int j = 0; j < glParticlesNum_; ++j) {
                    if (darts < wBuffer[particlesNum_ + j]) {
                        particles_[i].setPose(glParticles_[j].getPose());
                        if (estimateReliability_)
                            reliabilities_[i] = glSampledPosesReliabilities_[j];
                        particles_[i].setW(wo);
                        break;
                    }
                }
            }
        }

        double xo = mclPose_.getX();
        double yo = mclPose_.getY();
        double yawo = mclPose_.getYaw();
        for (int i = resampledParticlesNum; i < resampledParticlesNum + randomParticlesNum; ++i) {
            double x = xo + nrand(randomParticlesNoise_[0]);
            double y = yo + nrand(randomParticlesNoise_[1]);
            double yaw = yawo + nrand(randomParticlesNoise_[2]);
            particles_[i].setPose(x, y, yaw);
            particles_[i].setW(wo);
            if (estimateReliability_)
                reliabilities_[i] = reliability_;
        }
    }
    canUseGLSampledPoses_ = false;
}

template <typename T> 
std::vector<T> MCL::getResidualErrors(Pose pose) {
    double yaw = pose.getYaw();
    double sensorX = baseLink2Laser_.getX() * cos(yaw) -
                    baseLink2Laser_.getY() * sin(yaw) + pose.getX();
    double sensorY = baseLink2Laser_.getX() * sin(yaw) +
                    baseLink2Laser_.getY() * cos(yaw) + pose.getY();
    double sensorYaw = baseLink2Laser_.getYaw() + yaw;
    int size = (int)scan_.ranges.size();
    std::vector<T> residualErrors(size);
    for (int i = 0; i < size; ++i) {
        double r = scan_.ranges[i];
        if (r <= scan_.range_min || scan_.range_max <= r) {
            residualErrors[i] = -1.0;
            continue;
        }
        double t =
            (double)i * scan_.angle_increment + scan_.angle_min + sensorYaw;
        double x = r * cos(t) + sensorX;
        double y = r * sin(t) + sensorY;
        int u, v;
        xy2uv(x, y, &u, &v);
        if (onMap(u, v)) {
            T dist = (T)distMap_.at<float>(v, u);
            residualErrors[i] = dist;
        } else {
            residualErrors[i] = -1.0;
        }
    }
    return residualErrors;
}

void MCL::plotScan(void) {
    static bool isFirst = true;
    FILE *fp;
    if (isFirst) {
        fp = fopen("/tmp/ascan.txt", "w");
        fclose(fp);
        isFirst = false;
    }
    fp = fopen("/tmp/ascan.txt", "a");
    double yaw = mclPose_.getYaw();
    double sensorX = baseLink2Laser_.getX() * cos(yaw) -
                    baseLink2Laser_.getY() * sin(yaw) + mclPose_.getX();
    double sensorY = baseLink2Laser_.getX() * sin(yaw) +
                    baseLink2Laser_.getY() * cos(yaw) + mclPose_.getY();
    double sensorYaw = baseLink2Laser_.getYaw() + yaw;
    int size = (int)scan_.ranges.size();
    for (int i = 0; i < size; ++i) {
        double r = scan_.ranges[i];
        if (r <= scan_.range_min || scan_.range_max <= r)
            continue;
        double t =
            (double)i * scan_.angle_increment + scan_.angle_min + sensorYaw;
        double x = r * cos(t) + sensorX;
        double y = r * sin(t) + sensorY;
        fprintf(fp, "%f %f\n", x, y);
    }
    fclose(fp);
}

void MCL::printResult() {
    RCLCPP_INFO(get_logger(), 
        "MCL: x = %.3f [m], y = %.3f [m], yaw = %.3f [deg]",
        mclPose_.getX(), mclPose_.getY(), mclPose_.getYaw() * rad2deg_);
        
    {
        std::lock_guard<std::mutex> lock(odom_mutex_);
        RCLCPP_INFO(get_logger(),
            "Odom: x = %.3f [m], y = %.3f [m], yaw = %.3f [deg]",
            odomPose_.getX(), odomPose_.getY(), odomPose_.getYaw() * rad2deg_);
    }
        
    RCLCPP_INFO(get_logger(), "total likelihood = %.3f", totalLikelihood_);
    RCLCPP_INFO(get_logger(), "average likelihood = %.3f", averageLikelihood_);
    RCLCPP_INFO(get_logger(), "max likelihood = %.3f", maxLikelihood_);
    RCLCPP_INFO(get_logger(), "effective sample size = %.3f", effectiveSampleSize_);

    if (useAugmentedMCL_) { 
        RCLCPP_INFO(get_logger(), "amcl random particles rate = %.3f", 
            amclRandomParticlesRate_);
    }

    if (estimateReliability_ && classifierType_ == 0) {
        RCLCPP_INFO(get_logger(), 
            "reliability = %.3f (mae = %.3f [m], th = %.3f [m])",
            reliability_,
            maeClassifier_.getMAE(getResidualErrors<double>(
                particles_[maxLikelihoodParticleIdx_].getPose())),
            maeClassifier_.getFailureThreshold());
    }
}

void MCL::publishROSMessages(Pose &pose, const rclcpp::Time &stamp) {
    // pose
    PoseStampT poseMsg;
    poseMsg.header.frame_id = mapFrame_;
    poseMsg.header.stamp = stamp;
    poseMsg.pose.position.x = pose.getX();
    poseMsg.pose.position.y = pose.getY();
    poseMsg.pose.orientation = createQuaternionMsgFromYaw(pose.getYaw());
    posePub_->publish(poseMsg);

    // particles
    PoseArrayT particlesPoses;
    particlesPoses.header.frame_id = mapFrame_;
    particlesPoses.header.stamp = stamp;
    particlesPoses.poses.resize(particlesNum_);
    for (int i = 0; i < particlesNum_; ++i) {
        PoseT pose;
        pose.position.x = particles_[i].getX();
        pose.position.y = particles_[i].getY();
        pose.orientation = createQuaternionMsgFromYaw(particles_[i].getYaw());
        particlesPoses.poses[i] = pose;
    }
    particlesPub_->publish(particlesPoses);

    // unknown scan
    if (publishUnknownScan_ && (rejectUnknownScan_ || measurementModelType_ == 2)) {
        if (!estimateReliability_ && classifierType_ != 0 && measurementModelType_ == 2) {
            Pose mlPose = particles_[maxLikelihoodParticleIdx_].getPose();
            estimateUnknownScanWithClassConditionalMeasurementModel(mlPose);
        }
        unknownScanPub_->publish(unknownScan_);
    }

    // residual errors
    if (publishResidualErrors_) {
        LaserT residualErrors = scan_;
        residualErrors.intensities = getResidualErrors<float>(pose);
        residualErrorsPub_->publish(residualErrors);
    }

    // reliability
    if (estimateReliability_) {
        ReliabilityT reliability;
        reliability.header.stamp = stamp;
        reliability.vector.x = reliability_;
        if (classifierType_ == 0) {
            double mae;
            if (maxLikelihoodParticleIdx_ < particlesNum_)
                mae = maes_[maxLikelihoodParticleIdx_];
            else
                mae = glSampledPosesMAEs_[maxLikelihoodParticleIdx_ - particlesNum_];
            reliability.vector.y = mae;
            reliability.vector.z = maeClassifier_.getFailureThreshold();
        }
        reliabilityPub_->publish(reliability);

        MarkerT marker;
        marker.header.frame_id = mapFrame_;
        marker.header.stamp = stamp;
        marker.ns = "reliability_marker_namespace";
        marker.id = 0;
        marker.type = MarkerT::TEXT_VIEW_FACING;
        marker.action = MarkerT::ADD;
        marker.pose.position.x = pose.getX();
        marker.pose.position.y = pose.getY() - 3.0;
        marker.pose.position.z = 0.0;
        marker.scale.x = 0.0;
        marker.scale.y = 0.0;
        marker.scale.z = 2.0;
        marker.text = "Reliability: " + std::to_string((int)(reliability_ * 100.0)) + " %";
        marker.color.a = 1.0;
        marker.color.r = 1.0;
        marker.color.g = 1.0;
        marker.color.b = 1.0;
        if (reliability_ < 0.9)
            marker.color.r = marker.color.g = 0.0;
        reliabilityMarkerPub_->publish(marker);
    }
}

void MCL::broadcastTF(Pose &pose) {
    if (!broadcastTF_)
        return;

    PoseT poseOnMap;
    poseOnMap.position.x = pose.getX();
    poseOnMap.position.y = pose.getY();
    poseOnMap.position.z = 0.0;
    poseOnMap.orientation = createQuaternionMsgFromYaw(pose.getYaw());
    tf2::Transform map2baseTrans;
    tf2::convert(poseOnMap, map2baseTrans);

    if (useOdomTF_) {
        PoseT poseOnOdom;
        {
            std::lock_guard<std::mutex> lock(odom_mutex_);
            poseOnOdom.position.x = odomPose_.getX();
            poseOnOdom.position.y = odomPose_.getY();
            poseOnOdom.position.z = 0.0;
            poseOnOdom.orientation = createQuaternionMsgFromYaw(odomPose_.getYaw());
        }
        tf2::Transform odom2baseTrans;
        tf2::convert(poseOnOdom, odom2baseTrans);

        tf2::Transform map2odomTrans = map2baseTrans * odom2baseTrans.inverse();
        // add transform_tolerance: send a transform that is good up until a
        // tolerance time so that odom can be used rclcpp::Time
        
        //rclcpp::Time transformExpiration =
          //  (rclcpp::Clock().now() + rclcpp::Duration(transformTolerance_, 0));
        rclcpp::Time transformExpiration = mclPoseStamp_ + rclcpp::Duration(transformTolerance_, 0);
        geometry_msgs::msg::TransformStamped map2odomStampedTrans;
        map2odomStampedTrans.header.stamp = transformExpiration;
        map2odomStampedTrans.header.frame_id = mapFrame_;
        map2odomStampedTrans.child_frame_id = odomFrame_;
        tf2::convert(map2odomTrans, map2odomStampedTrans.transform);
        tfBroadcaster_->sendTransform(map2odomStampedTrans);

    } else {
        // rclcpp::Time transformExpiration = (mclPoseStamp_ +
        // rclcpp::Duration(transformTolerance_));
        rclcpp::Time transformExpiration =
            (rclcpp::Clock().now() + rclcpp::Duration(transformTolerance_, 0));
        geometry_msgs::msg::TransformStamped map2baseStampedTrans;
        map2baseStampedTrans.header.stamp = transformExpiration;
        map2baseStampedTrans.header.frame_id = mapFrame_;
        map2baseStampedTrans.child_frame_id = baseLinkFrame_;
        tf2::convert(map2baseTrans, map2baseStampedTrans.transform);
        tfBroadcaster_->sendTransform(map2baseStampedTrans);
    }
}

void MCL::plotLikelihoodMap(void) {
    double yaw = mclPose_.getYaw();
    double sensorX = baseLink2Laser_.getX() * cos(yaw) -
                    baseLink2Laser_.getY() * sin(yaw) + mclPose_.getX();
    double sensorY = baseLink2Laser_.getX() * sin(yaw) +
                    baseLink2Laser_.getY() * cos(yaw) + mclPose_.getY();
    double sensorYaw = baseLink2Laser_.getYaw() + yaw;
    double range = 0.5;

    std::vector<Pose> sensorPoses;
    for (double x = -range - mapResolution_; x <= range + mapResolution_;
        x += mapResolution_) {
        for (double y = -range - mapResolution_; y <= range + mapResolution_;
            y += mapResolution_) {
            Pose sensorPose(sensorX + x, sensorY + y, sensorYaw);
            sensorPoses.push_back(sensorPose);
        }
    }

    std::vector<double> likelihoods((int)sensorPoses.size(), 0.0);
    for (int i = 0; i < (int)scan_.ranges.size(); i += scanStep_) {
        double range = scan_.ranges[i];
        double rangeAngle = (double)i * scan_.angle_increment + scan_.angle_min;
        double max;
        for (int j = 0; j < (int)sensorPoses.size(); ++j) {
            double p;
            if (measurementModelType_ == 0)
                p = calculateLikelihoodFieldModel(sensorPoses[j], range, rangeAngle);
            else if (measurementModelType_ == 1)
                p = calculateBeamModel(sensorPoses[j], range, rangeAngle);
            else
                p = calculateClassConditionalMeasurementModel(sensorPoses[j], range,
                                                            rangeAngle);
            double w = likelihoods[j];
            w += log(p);
            likelihoods[j] = w;
            if (j == 0) {
                max = w;
            } else {
                if (max < w)
                    max = w;
            }
        }
        if (max < -300.0) {
            for (int j = 0; j < (int)sensorPoses.size(); ++j)
                likelihoods[j] += 300.0;
            }
        }

        double sum = 0.0;
        double max;
        int maxIdx;
        for (int i = 0; i < (int)sensorPoses.size(); ++i) {
            double w = exp(likelihoods[i]);
            if (estimateReliability_) {
                double reliability = 0.5;
                if (classifierType_ == 0) {
                    std::vector<double> residualErrors =
                        getResidualErrors<double>(sensorPoses[i]);
                    double mae = maeClassifier_.getMAE(residualErrors);
                    w *= maeClassifier_.calculateDecisionModel(mae, &reliability);
                }
            }
            likelihoods[i] = w;
            sum += w;
        }

        FILE *fp;
        fp = fopen("/tmp/als_ros_likelihood_map.txt", "w");
        int cnt = 0;
        for (double x = -range - mapResolution_; x <= range + mapResolution_;
            x += mapResolution_) {
        for (double y = -range - mapResolution_; y <= range + mapResolution_;
            y += mapResolution_) {
            fprintf(fp, "%lf %lf %lf\n", x, y, likelihoods[cnt] / sum);
            cnt++;
        }
        fprintf(fp, "\n");
    }
    fclose(fp);

    fp = fopen("/tmp/als_ros_scan_points.txt", "w");
    for (int i = 0; i < (int)scan_.ranges.size(); ++i) {
        double r = scan_.ranges[i];
        if (r < scan_.range_min || scan_.range_max < r)
            continue;
        double t =
            (double)i * scan_.angle_increment + scan_.angle_min + sensorYaw;
        double x = r * cos(t) + sensorX;
        double y = r * sin(t) + sensorY;
        fprintf(fp, "%lf %lf\n", x, y);
    }
    fclose(fp);

    if (measurementModelType_ == 2 || rejectUnknownScan_) {
        fp = fopen("/tmp/als_ros_unknown_scan_points.txt", "w");
        for (int i = 0; i < (int)unknownScan_.ranges.size(); ++i) {
            double r = unknownScan_.ranges[i];
            if (r < unknownScan_.range_min || unknownScan_.range_max < r)
                continue;
            double t = (double)i * unknownScan_.angle_increment +
                    unknownScan_.angle_min + sensorYaw;
            double x = r * cos(t) + sensorX;
            double y = r * sin(t) + sensorY;
            fprintf(fp, "%lf %lf\n", x, y);
        }
        fclose(fp);
    }

    static FILE *gp;
    if (gp == NULL) {
        gp = popen("gnuplot -persist", "w");
        fprintf(gp, "set colors classic\n");
        fprintf(gp, "set grid\n");
        fprintf(gp, "set size ratio 1 1\n");
        fprintf(gp, "set xlabel \"%s\"\n", "{/Symbol D}x [m]");
        fprintf(gp, "set ylabel \"%s\"\n", "{/Symbol D}y [m]");
        fprintf(gp, "set tics font \"Arial, 14\"\n");
        fprintf(gp, "set xlabel font \"Arial, 14\"\n");
        fprintf(gp, "set ylabel font \"Arial, 14\"\n");
        fprintf(gp, "set cblabel font \"Arial, 14\"\n");
        fprintf(gp, "set xrange [ %lf : %lf ]\n", -range, range);
        fprintf(gp, "set yrange [ %lf : %lf ]\n", -range, range);
        fprintf(gp, "set pm3d map interpolate 2, 2\n");
        fprintf(gp, "unset key\n");
        fprintf(gp, "unset cbtics\n");
    }
    fprintf(gp, "splot \"/tmp/als_ros_likelihood_map.txt\" with pm3d\n");
    fflush(gp);
}

void MCL::plotWorld(double plotRange) {
    static FILE *gp;
    FILE *fp;
    if (gp == NULL) {
    gp = popen("gnuplot -persist", "w");
    fprintf(gp, "set colors classic\n");
    fprintf(gp, "unset key\n");
    fprintf(gp, "set grid\n");
    fprintf(gp, "set size ratio 1 1\n");
    fprintf(gp, "set xlabel \"%s\"\n", "X [m]");
    fprintf(gp, "set ylabel \"%s\"\n", "Y [m]");
    fprintf(gp, "set tics font \"Arial, 14\"\n");
    fprintf(gp, "set xlabel font \"Arial, 14\"\n");
    fprintf(gp, "set ylabel font \"Arial, 14\"\n");

    fp = fopen("/tmp/als_ros_map_points.txt", "w");
        for (int u = 0; u < mapWidth_; u++) {
            for (int v = 0; v < mapHeight_; v++) {
            if (distMap_.at<float>(v, u) == 0.0f) {
                double x, y;
                uv2xy(u, v, &x, &y);
                fprintf(fp, "%lf %lf\n", x, y);
            }
            }
        }
        fclose(fp);
    }

    fprintf(gp, "set xrange [ %lf : %lf ]\n", mclPose_.getX() - plotRange,
            mclPose_.getX() + plotRange);
    fprintf(gp, "set yrange [ %lf : %lf ]\n", mclPose_.getY() - plotRange,
            mclPose_.getY() + plotRange);

    double axesLength = 2.0;
    double x1 = mclPose_.getX() + axesLength * cos(mclPose_.getYaw());
    double y1 = mclPose_.getY() + axesLength * sin(mclPose_.getYaw());
    double x2 =
        mclPose_.getX() + axesLength * cos(mclPose_.getYaw() + M_PI / 2.0);
    double y2 =
        mclPose_.getY() + axesLength * sin(mclPose_.getYaw() + M_PI / 2.0);
    fp = fopen("/tmp/als_ros_robot_pose1.txt", "w");
    fprintf(fp, "%lf %lf\n", mclPose_.getX(), mclPose_.getY());
    fprintf(fp, "%lf %lf\n", x1, y1);
    fclose(fp);
    fp = fopen("/tmp/als_ros_robot_pose2.txt", "w");
    fprintf(fp, "%lf %lf\n", mclPose_.getX(), mclPose_.getY());
    fprintf(fp, "%lf %lf\n", x2, y2);
    fclose(fp);

    fp = fopen("/tmp/als_ros_scan_points.txt", "w");
    double yaw = mclPose_.getYaw();
    double sensorX = baseLink2Laser_.getX() * cos(yaw) -
                    baseLink2Laser_.getY() * sin(yaw) + mclPose_.getX();
    double sensorY = baseLink2Laser_.getX() * sin(yaw) +
                    baseLink2Laser_.getY() * cos(yaw) + mclPose_.getY();
    double sensorYaw = baseLink2Laser_.getYaw() + yaw;
    for (size_t i = 0; i < scan_.ranges.size(); ++i) {
    double r = scan_.ranges[i];
    if (r < scan_.range_min || scan_.range_max < r)
        continue;
    double angle =
        scan_.angle_min + (double)i * scan_.angle_increment + sensorYaw;
    double x = sensorX + r * cos(angle);
    double y = sensorY + r * sin(angle);
    fprintf(fp, "%lf %lf\n", x, y);
    }
    fclose(fp);

    fprintf(gp, "plot \"%s\" with points pointtype 5 pointsize 0.1 lt -1, \
            \"%s\" pointtype 1 pointsize 0.8 lt 1, \"%s\" w l lt 2 lw 3, \"%s\" u 1:2 w l lt 1 lw 3\n",
            "/tmp/als_ros_map_points.txt", "/tmp/als_ros_scan_points.txt",
            "/tmp/als_ros_robot_pose2.txt", "/tmp/als_ros_robot_pose1.txt");
    fflush(gp);
}

void MCL::scanCB(const LaserT::ConstSharedPtr &msg) {
    if (canUpdateScan_.load()) {
        scan_ = *msg;
    }
    if (!gotScan_.load()) 
        gotScan_.store(true);
}

void MCL::odomCB(const OdomT::ConstSharedPtr &msg) {
    auto currentTime = msg->header.stamp;
    double currTime = tf2_ros::timeToSec(currentTime);
    if (isFirstPose_.load()) {
        lastOdomStamp_ = currentTime;
        isFirstPose_.store(false);
        {
            std::lock_guard<std::mutex> lock(odom_mutex_);
            odomPose_.setPose(0.0, 0.0, 0.0);
            deltaX_ = deltaY_ = deltaDist_ = deltaYaw_ = 0.0;
        }
        
        need_update_.store(true);
        return;
    }
    double deltaTime = currTime - tf2_ros::timeToSec(lastOdomStamp_);
    if (deltaTime == 0.0)
        return;

    odomPoseStamp_ = msg->header.stamp;
    double norm_delta_dist = 0.0;
    double norm_delta_yaw = 0.0;

    {
        std::lock_guard<std::mutex> lock(odom_mutex_);
        deltaX_ += msg->twist.twist.linear.x * deltaTime;
        deltaY_ += msg->twist.twist.linear.y * deltaTime;
        deltaDist_ += msg->twist.twist.linear.x * deltaTime;
        deltaYaw_ += msg->twist.twist.angular.z * deltaTime;
        while (deltaYaw_ < -M_PI)
            deltaYaw_ += 2.0 * M_PI;
        while (deltaYaw_ > M_PI)
            deltaYaw_ -= 2.0 * M_PI;
        deltaTimeSum_ += deltaTime;
        tf2::Quaternion q(
            msg->pose.pose.orientation.x, msg->pose.pose.orientation.y,
            msg->pose.pose.orientation.z, msg->pose.pose.orientation.w);
        double roll, pitch, yaw;
        tf2::Matrix3x3 m(q);
        m.getRPY(roll, pitch, yaw);
        odomPose_.setPose(msg->pose.pose.position.x, msg->pose.pose.position.y, yaw);       
    
        norm_delta_dist = sqrt(deltaX_ * deltaX_ + deltaY_ * deltaY_);
        norm_delta_yaw = fabs(deltaYaw_);
    }
    if (norm_delta_dist >= update_dist_thresh_ || norm_delta_yaw >= update_yaw_thresh_) {
        {
            need_update_.store(true);
            //print norm_delta_dist and norm_delta_yaw
            RCLCPP_INFO(get_logger(), "norm_delta_dist = %lf, norm_delta_yaw = %lf", norm_delta_dist, norm_delta_yaw);
        }
    }
    lastOdomStamp_ = currentTime;
}

void MCL::mapCB(const MapT::ConstSharedPtr &msg) {
    // perform distance transform to build the distance field
    mapWidth_ = msg->info.width;
    mapHeight_ = msg->info.height;
    mapResolution_ = msg->info.resolution;
    cv::Mat binMap(mapHeight_, mapWidth_, CV_8UC1);
    for (int v = 0; v < mapHeight_; v++) {
        for (int u = 0; u < mapWidth_; u++) {
            int node = v * mapWidth_ + u;
            int val = msg->data[node];
            if (val == 100)
                binMap.at<uchar>(v, u) = 0;
            else
                binMap.at<uchar>(v, u) = 1;
        }
    }
    cv::Mat distMap(mapHeight_, mapWidth_, CV_32FC1);
    cv::distanceTransform(binMap, distMap, cv::DIST_L2, 5);
    for (int v = 0; v < mapHeight_; v++) {
        for (int u = 0; u < mapWidth_; u++) {
            float d = distMap.at<float>(v, u) * (float)mapResolution_;
            distMap.at<float>(v, u) = d;
        }
    }
    distMap_ = distMap;
    tf2::Quaternion q(
        msg->info.origin.orientation.x, msg->info.origin.orientation.y,
        msg->info.origin.orientation.z, msg->info.origin.orientation.w);
    double roll, pitch, yaw;
    tf2::Matrix3x3 m(q);
    m.getRPY(roll, pitch, yaw);
    mapOrigin_.setX(msg->info.origin.position.x);
    mapOrigin_.setY(msg->info.origin.position.y);
    mapOrigin_.setYaw(yaw);
    gotMap_.store(true);
    RCLCPP_INFO(get_logger(), "Map is received.");
}

void MCL::initialPoseCB(const PoseCovStampT::ConstSharedPtr &msg) {
    tf2::Quaternion q(
        msg->pose.pose.orientation.x, msg->pose.pose.orientation.y,
        msg->pose.pose.orientation.z, msg->pose.pose.orientation.w);
    double roll, pitch, yaw;
    tf2::Matrix3x3 m(q);
    m.getRPY(roll, pitch, yaw);
    mclPose_.setPose(msg->pose.pose.position.x, msg->pose.pose.position.y, yaw);
    resetParticlesDistribution();
    if (estimateReliability_)
        resetReliabilities();
    isFirstPose_ = true;
    RCLCPP_INFO(get_logger(), "Initial pose is set(%lf, %lf, %lf).", msg->pose.pose.position.x, msg->pose.pose.position.y, yaw);
}

void MCL::glSampledPosesCB(const PoseArrayT::ConstSharedPtr &msg) {
    if (canUpdateGLSampledPoses_) {
        glSampledPosesStamp_ = msg->header.stamp;
        glSampledPoses_ = *msg;
        isGLSampledPosesUpdated_ = true;
    }
}

void MCL::resetParticlesDistribution(void) {
    particles_.resize(particlesNum_);
    double xo = mclPose_.getX();
    double yo = mclPose_.getY();
    double yawo = mclPose_.getYaw();
    double wo = 1.0 / (double)particlesNum_;
    for (int i = 0; i < particlesNum_; ++i) {
        double x = xo + nrand(initialNoiseX_);
        double y = yo + nrand(initialNoiseY_);
        double yaw = yawo + nrand(initialNoiseYaw_);
        particles_[i].setPose(x, y, yaw);
        particles_[i].setW(wo);
    }
}

void MCL::resetReliabilities(void) { 
    reliabilities_.resize(particlesNum_, 0.5); 
}

void MCL::rejectUnknownScan(void) {
    unknownScan_ = scan_;
    double xo = baseLink2Laser_.getX();
    double yo = baseLink2Laser_.getY();
    double yawo = baseLink2Laser_.getYaw();
    double hitThreshold = 0.5 * mapResolution_;
    for (int i = 0; i < (int)unknownScan_.ranges.size(); ++i) {
        if (i % scanStep_ != 0) {
            unknownScan_.ranges[i] = 0.0;
            continue;
        }
        double r = unknownScan_.ranges[i];
        if (r <= unknownScan_.range_min || unknownScan_.range_max <= r) {
            unknownScan_.ranges[i] = 0.0;
            continue;
        }
        double laserYaw =
            (double)i * unknownScan_.angle_increment + unknownScan_.angle_min;
        double pShortSum = 0.0, pBeamSum = 0.0;
        for (int j = 0; j < particlesNum_; ++j) {
            double yaw = particles_[j].getYaw();
            double x = xo * cos(yaw) - yo * sin(yaw) + particles_[j].getX();
            double y = xo * sin(yaw) + yo * cos(yaw) + particles_[j].getY();
            double t = yawo + yaw + laserYaw;
            double dx = mapResolution_ * cos(t);
            double dy = mapResolution_ * sin(t);
            int u, v;
            double expectedRange = -1.0;
            for (double range = 0.0; range <= unknownScan_.range_max; range += mapResolution_) {
                xy2uv(x, y, &u, &v);
                if (onMap(u, v)) {
                    double dist = (double)distMap_.at<float>(v, u);
                    if (dist < hitThreshold) {
                    expectedRange = range;
                    break;
                    }
                } else {
                    break;
                }
                x += dx;
                y += dy;
            }
            if (r <= expectedRange) {
                double error = expectedRange - r;
                double pHit = normConstHit_ * exp(-(error * error) * denomHit_) *
                                mapResolution_;
                double pShort = lambdaShort_ * exp(-lambdaShort_ * r) /
                                (1.0 - exp(-lambdaShort_ * unknownScan_.range_max)) *
                                mapResolution_;
                pShortSum += pShort;
                pBeamSum += zHit_ * pHit + zShort_ * pShort + measurementModelRandom_;
            } else {
                pBeamSum += measurementModelRandom_;
            }
        }
        double pShort = pShortSum;
        double pBeam = pBeamSum;
        double pUnknown = pShortSum / pBeamSum;
        if (pUnknown < unknownScanProbThreshold_) {
            unknownScan_.ranges[i] = 0.0;
        } else {
            // unknown scan is rejected from the scan message used for localization
            scan_.ranges[i] = 0.0;
        }
    }
}

double MCL::calculateLikelihoodFieldModel(Pose &pose, double range,
                                    double rangeAngle) {
    if (range <= scan_.range_min || scan_.range_max <= range)
        return measurementModelInvalidScan_;

    double t = pose.getYaw() + rangeAngle;
    double x = range * cos(t) + pose.getX();
    double y = range * sin(t) + pose.getY();
    int u, v;
    xy2uv(x, y, &u, &v);
    double p;
    if (onMap(u, v)) {
        double dist = (double)distMap_.at<float>(v, u);
        double pHit =
        normConstHit_ * exp(-(dist * dist) * denomHit_) * mapResolution_;
        p = zHit_ * pHit + measurementModelRandom_;
    } else {
        p = measurementModelRandom_;
    }
    if (p > 1.0)
        p = 1.0;
    return p;
}

double MCL::calculateBeamModel(Pose &pose, double range, double rangeAngle) {
    if (range <= scan_.range_min || scan_.range_max <= range)
        return measurementModelInvalidScan_;

    double t = pose.getYaw() + rangeAngle;
    double x = pose.getX();
    double y = pose.getY();
    double dx = mapResolution_ * cos(t);
    double dy = mapResolution_ * sin(t);
    int u, v;
    double expectedRange = -1.0;
    double hitThreshold = 0.5 * mapResolution_;
    for (double r = 0.0; r < scan_.range_max; r += mapResolution_) {
        xy2uv(x, y, &u, &v);
        if (onMap(u, v)) {
            double dist = (double)distMap_.at<float>(v, u);
            if (dist < hitThreshold) {
                expectedRange = r;
                break;
            }
        } else {
            break;
        }
        x += dx;
        y += dy;
    }

    double p;
    if (range <= expectedRange) {
        double error = expectedRange - range;
        double pHit =
            normConstHit_ * exp(-(error * error) * denomHit_) * mapResolution_;
        double pShort = lambdaShort_ * exp(-lambdaShort_ * range) /
                        (1.0 - exp(-lambdaShort_ * scan_.range_max)) *
                        mapResolution_;
        p = zHit_ * pHit + zShort_ * pShort + measurementModelRandom_;
    } else {
        p = measurementModelRandom_;
    }
    if (p > 1.0)
        p = 1.0;
    return p;
}

double MCL::calculateClassConditionalMeasurementModel(Pose &pose, double range,
                                                double rangeAngle) {
    if (range <= scan_.range_min || scan_.range_max <= range)
        return measurementModelInvalidScan_;

    double t = pose.getYaw() + rangeAngle;
    double x = range * cos(t) + pose.getX();
    double y = range * sin(t) + pose.getY();
    double pUnknown = lambdaUnknown_ * exp(-lambdaUnknown_ * range) /
                    (1.0 - exp(-lambdaUnknown_ * scan_.range_max)) *
                    mapResolution_ * pUnknownPrior_;
    int u, v;
    xy2uv(x, y, &u, &v);
    double p = pUnknown;
    if (onMap(u, v)) {
        double dist = (double)distMap_.at<float>(v, u);
        double pHit =
            normConstHit_ * exp(-(dist * dist) * denomHit_) * mapResolution_;
        p += (zHit_ * pHit + measurementModelRandom_) * pKnownPrior_;
    } else {
        p += measurementModelRandom_ * pKnownPrior_;
    }
    if (p > 1.0)
        p = 1.0;
    return p;
}

void MCL::estimateUnknownScanWithClassConditionalMeasurementModel(Pose &pose) {
    unknownScan_ = scan_;
    double yaw = pose.getYaw();
    double sensorX = baseLink2Laser_.getX() * cos(yaw) -
                    baseLink2Laser_.getY() * sin(yaw) + pose.getX();
    double sensorY = baseLink2Laser_.getX() * sin(yaw) +
                    baseLink2Laser_.getY() * cos(yaw) + pose.getY();
    double sensorYaw = baseLink2Laser_.getYaw() + yaw;
    for (int i = 0; i < (int)unknownScan_.ranges.size(); ++i) {
    double r = unknownScan_.ranges[i];
    if (r <= unknownScan_.range_min || unknownScan_.range_max <= r) {
        unknownScan_.ranges[i] = 0.0;
        continue;
    }
    double t = sensorYaw + (double)i * unknownScan_.angle_increment +
                unknownScan_.angle_min;
    double x = r * cos(t) + sensorX;
    double y = r * sin(t) + sensorY;
    int u, v;
    xy2uv(x, y, &u, &v);
    double pKnown;
    double pUnknown = lambdaUnknown_ * exp(-lambdaUnknown_ * r) /
                        (1.0 - exp(-lambdaUnknown_ * unknownScan_.range_max)) *
                        mapResolution_ * pUnknownPrior_;
    if (onMap(u, v)) {
        double dist = (double)distMap_.at<float>(v, u);
        double pHit =
            normConstHit_ * exp(-(dist * dist) * denomHit_) * mapResolution_;
        pKnown = (zHit_ * pHit + measurementModelRandom_) * pKnownPrior_;
    } else {
        pKnown = measurementModelRandom_ * pKnownPrior_;
    }
    double sum = pKnown + pUnknown;
    pUnknown /= sum;
    if (pUnknown < unknownScanProbThreshold_)
        unknownScan_.ranges[i] = 0.0;
    }
}

}

#include <rclcpp_components/register_node_macro.hpp>
// 注册组件
RCLCPP_COMPONENTS_REGISTER_NODE(localization::MCL)
