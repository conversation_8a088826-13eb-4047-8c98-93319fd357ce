#include <localization/gl_pose_sampler.h>

namespace localization {

GLPoseSampler::GLPoseSampler(const rclcpp::NodeOptions & options)
    : nav2_util::LifecycleNode("gl_pose_sampler", "", options)
{
    RCLCPP_INFO(get_logger(), "Creating");
}

GLPoseSampler::~GLPoseSampler()
{
    //
}

nav2_util::CallbackReturn
GLPoseSampler::on_configure(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Configuring");
    // 初始化参数
    initParameters();
    // 初始化发布者
    initPub();
    // 初始化 TF 相关
    initTransforms();
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
GLPoseSampler::on_activate(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Activating");
    
    // 激活所有生命周期发布者
    posesPub_->on_activate();
    localMapPub_->on_activate();
    sdfKeypointsPub_->on_activate();
    localSDFKeypointsPub_->on_activate();
    
    // 创建与 lifecycle manager 的连接
    createBond();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
GLPoseSampler::on_deactivate(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Deactivating");
    
    // 停用所有生命周期发布者
    posesPub_->on_deactivate();
    localMapPub_->on_deactivate();
    sdfKeypointsPub_->on_deactivate();
    localSDFKeypointsPub_->on_deactivate();
    
    // 断开与 lifecycle manager 的连接
    destroyBond();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
GLPoseSampler::on_cleanup(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Cleaning up");
    
    // 清理资源
    posesPub_.reset();
    localMapPub_.reset();
    sdfKeypointsPub_.reset();
    localSDFKeypointsPub_.reset();
    
    mapSub_.reset();
    scanSub_.reset();
    odomSub_.reset();
    
    tf_listener_.reset();
    tf_buffer_.reset();
    transform_timer_.reset();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
GLPoseSampler::on_shutdown(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Shutting down");
    return nav2_util::CallbackReturn::SUCCESS;
}

void GLPoseSampler::initParameters()
{
    this->declare_parameter<std::string>("map_name", "/map");
    this->get_parameter("map_name", mapName_);

    this->declare_parameter<std::string>("scan_name", "/fusion_scan");
    this->get_parameter("scan_name", scanName_);

    this->declare_parameter<std::string>("odom_name", "/odom");
    this->get_parameter("odom_name", odomName_);

    this->declare_parameter<std::string>("poses_name", "/gl_sampled_poses");
    this->get_parameter("poses_name", posesName_);

    this->declare_parameter<std::string>("local_map_name", "/gl_local_map");
    this->get_parameter("local_map_name", localMapName_);

    this->declare_parameter<std::string>("sdf_keypoints_name", "/gl_sdf_keypoints");
    this->get_parameter("sdf_keypoints_name", sdfKeypointsName_);

    this->declare_parameter<std::string>("local_sdf_keypoints_name", "/gl_local_sdf_keypoints");
    this->get_parameter("local_sdf_keypoints_name", localSDFKeypointsName_);

    this->declare_parameter<std::string>("map_frame", "map");
    this->get_parameter("map_frame", mapFrame_);

    this->declare_parameter<std::string>("odom_frame", "odom");
    this->get_parameter("odom_frame", odomFrame_);

    this->declare_parameter<std::string>("base_link_frame", "base_link");
    this->get_parameter("base_link_frame", baseLinkFrame_);

    this->declare_parameter<std::string>("laser_frame", "base_link");
    this->get_parameter("laser_frame", laserFrame_);

    this->declare_parameter<int>("key_scans_num", 20);
    this->get_parameter("key_scans_num", keyScansNum_);

    this->declare_parameter<double>("key_scan_interval_dist", 0.25);
    this->get_parameter("key_scan_interval_dist", keyScanIntervalDist_);

    this->declare_parameter<double>("key_scan_interval_yaw", 5.0);
    this->get_parameter("key_scan_interval_yaw", keyScanIntervalYaw_);

    this->declare_parameter<double>("gradient_square_th", 10e-4);
    this->get_parameter("gradient_square_th", gradientSquareTH_);

    this->declare_parameter<double>("keypoints_min_dist_from_map", 0.2);
    this->get_parameter("keypoints_min_dist_from_map", keypointsMinDistFromMap_);

    this->declare_parameter<double>("sdf_feature_window_size", 1.0);
    this->get_parameter("sdf_feature_window_size", sdfFeatureWindowSize_);

    this->declare_parameter<double>("average_sdf_delta_th", 1.0);
    this->get_parameter("average_sdf_delta_th", averageSDFDeltaTH_);

    this->declare_parameter<bool>("add_random_samples", true);
    this->get_parameter("add_random_samples", addRandomSamples_);

    this->declare_parameter<bool>("add_opposite_samples", true);
    this->get_parameter("add_opposite_samples", addOppositeSamples_);

    this->declare_parameter<int>("random_samples_num", 30);
    this->get_parameter("random_samples_num", randomSamplesNum_);

    this->declare_parameter<double>("positional_random_noise", 0.5);
    this->get_parameter("positional_random_noise", positionalRandomNoise_);

    this->declare_parameter<double>("angular_random_noise", 0.3);
    this->get_parameter("angular_random_noise", angularRandomNoise_);

    this->declare_parameter<double>("matching_rate_th", 0.1);
    this->get_parameter("matching_rate_th", matchingRateTH_);

    this->declare_parameter<bool>("flip_scan", false);
    this->get_parameter("flip_scan", flipScan_);

    gotMap_ = false;
    gotOdom_ = false;
    
    keyScanIntervalYaw_ *= M_PI / 180.0;
    odomPose_.setPose(0.0, 0.0, 0.0);

    RCLCPP_INFO(this->get_logger(), "mapTopicName: %s, scanTopicName: %s, odomTopicName: %s", 
            mapName_.c_str(), scanName_.c_str(), odomName_.c_str());

}

void GLPoseSampler::initPub()
{
    posesPub_ = this->create_publisher<geometry_msgs::msg::PoseArray>(posesName_, 1);
    localMapPub_ = this->create_publisher<nav_msgs::msg::OccupancyGrid>(localMapName_, 1);
    sdfKeypointsPub_ = this->create_publisher<visualization_msgs::msg::Marker>(sdfKeypointsName_, 1);
    localSDFKeypointsPub_ = this->create_publisher<visualization_msgs::msg::Marker>(localSDFKeypointsName_, 1);
}

void GLPoseSampler::initSub()
{
    mapSub_ = this->create_subscription<nav_msgs::msg::OccupancyGrid>(
        mapName_, rclcpp::QoS(rclcpp::KeepLast(10)).transient_local(), std::bind(&GLPoseSampler::mapCB, this, std::placeholders::_1));

    scanSub_ = this->create_subscription<sensor_msgs::msg::LaserScan>(
        scanName_, 1, std::bind(&GLPoseSampler::scanCB, this, std::placeholders::_1));

    odomSub_ = this->create_subscription<nav_msgs::msg::Odometry>(
        odomName_, 1, std::bind(&GLPoseSampler::odomCB, this, std::placeholders::_1));
}

void GLPoseSampler::initTransforms()
{
    auto timer_interface = std::make_shared<tf2_ros::CreateTimerROS>(
        this->get_node_base_interface(),
        this->get_node_timers_interface());

    tf_buffer_ = std::make_unique<tf2_ros::Buffer>(this->get_clock());
    tf_buffer_->setCreateTimerInterface(timer_interface);
    tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);

    // 创建定时器检查 transform
    transform_timer_ = create_wall_timer(
        std::chrono::milliseconds(20),
        std::bind(&GLPoseSampler::checkTransform, this));
}

void GLPoseSampler::checkTransform()
{
    try {
        tfBaseLink2Laser = tf_buffer_->lookupTransform(
            baseLinkFrame_, laserFrame_, tf2::TimePointZero);
        
        tf2::Quaternion quatBaseLink2Laser(
            tfBaseLink2Laser.transform.rotation.x,
            tfBaseLink2Laser.transform.rotation.y,
            tfBaseLink2Laser.transform.rotation.z,
            tfBaseLink2Laser.transform.rotation.w);

        double baseLink2LaserRoll, baseLink2LaserPitch, baseLink2LaserYaw;
        tf2::Matrix3x3 rotMatBaseLink2Laser(quatBaseLink2Laser);
        rotMatBaseLink2Laser.getRPY(baseLink2LaserRoll, baseLink2LaserPitch, baseLink2LaserYaw);

        baseLink2Laser_.setX(tfBaseLink2Laser.transform.translation.x);
        baseLink2Laser_.setY(tfBaseLink2Laser.transform.translation.y);
        baseLink2Laser_.setYaw(baseLink2LaserYaw);

        transform_timer_->cancel();

        initSub();
        RCLCPP_INFO(get_logger(), "Transform from %s to %s is ready!",
            baseLinkFrame_.c_str(), laserFrame_.c_str());
    }
    catch (tf2::TransformException &ex) {
        RCLCPP_DEBUG(get_logger(), "Could not get transform: %s", ex.what());
    }
}

void GLPoseSampler::mapCB(const nav_msgs::msg::OccupancyGrid::SharedPtr msg)
{
    RCLCPP_INFO(this->get_logger(), "gl pose sampler subscribed map.");
    
    setMapInfo(*msg);
    cv::Mat distMap = buildDistanceFieldMap(*msg);
    cv::GaussianBlur(distMap, distMap, cv::Size(5, 5), 5);
    sdfKeypoints_ = detectKeypoints(*msg, distMap);
    sdfOrientationFeatures_ = calculateFeatures(distMap, sdfKeypoints_);
    sdfKeypointsMarker_ = makeSDFKeypointsMarker(sdfKeypoints_, mapFrame_);
    sdfKeypointsPub_->publish(sdfKeypointsMarker_);
    // print out a statement to show that the callback is running
    RCLCPP_INFO(this->get_logger(), "Map callback is running...");

    gotMap_ = true;
}

void GLPoseSampler::scanCB(const sensor_msgs::msg::LaserScan::SharedPtr msg)
{
    // print out a statement to show that the callback is running
    // RCLCPP_INFO(this->get_logger(), "Scan callback is running...");
    static bool isFirst = true;
    static Pose prevOdomPose;

    if (flipScan_)
    {
        // invert ranges to account for flippled lidar
        std::vector<float> ranges = msg->ranges;
        for (int i = 0; i < (int)ranges.size(); ++i)
        {
            ranges[i] = msg->ranges[(int)msg->ranges.size() - 1 - i];
        }
        msg->ranges = ranges;
    }

    int validScanNum = 0;
    for (int i = 0; i < (int)msg->ranges.size(); ++i)
    {
        float r = msg->ranges[i];
        if (msg->range_min <= r && r <= msg->range_max)
            validScanNum++;
    }
    double validScanRate = (double)validScanNum / (double)msg->ranges.size();
    if (validScanRate < 0.1)
    {
        RCLCPP_WARN(this->get_logger(), "gl pose sampler might subscribe invalid scan.");
        return;
    }

    if (isFirst && gotOdom_)
    {
        keyScans_.push_back(*msg);
        keyPoses_.push_back(odomPose_);
        prevOdomPose.setPose(odomPose_);
        isFirst = false;
        return;
    }

    bool isKeyScanUpdated = false;
    double dx = odomPose_.getX() - prevOdomPose.getX();
    double dy = odomPose_.getY() - prevOdomPose.getY();
    double dl = sqrt(dx * dx + dy * dy);
    double dyaw = odomPose_.getYaw() - prevOdomPose.getYaw();
    while (dyaw < -M_PI)
        dyaw += 2.0 * M_PI;
    while (dyaw > M_PI)
        dyaw -= 2.0 * M_PI;
    if (dl > keyScanIntervalDist_ || fabs(dyaw) > keyScanIntervalYaw_)
    { 
        keyScans_.insert(keyScans_.begin(), *msg);
        keyPoses_.insert(keyPoses_.begin(), odomPose_);
        if ((int)keyScans_.size() >= keyScansNum_)
        {
            keyScans_.resize(keyScansNum_);
            keyPoses_.resize(keyScansNum_);
        }
        prevOdomPose.setPose(odomPose_);
        isKeyScanUpdated = true;
    }

    if (isKeyScanUpdated && (int)keyScans_.size() == keyScansNum_)
    {
        nav_msgs::msg::OccupancyGrid localMap = buildLocalMap();
        cv::Mat localDistMap = buildDistanceFieldMap(localMap);
        cv::GaussianBlur(localDistMap, localDistMap, cv::Size(5, 5), 5);
        std::vector<Keypoint> localSDFKeypoints = detectKeypoints(localMap, localDistMap);
        std::vector<SDFOrientationFeature> localSDFOrientationFeatures = calculateFeatures(localDistMap, localSDFKeypoints);
        std::vector<int> correspondingIndices = findCorrespondingFeatures(localSDFKeypoints, localSDFOrientationFeatures);
        geometry_msgs::msg::PoseArray poses = generatePoses(prevOdomPose, localSDFKeypoints, localSDFOrientationFeatures, correspondingIndices);
        visualization_msgs::msg::Marker localSDFKeypointsMarker = makeSDFKeypointsMarker(localSDFKeypoints, odomFrame_);

        poses.header.stamp = localMap.header.stamp = sdfKeypointsMarker_.header.stamp = localSDFKeypointsMarker.header.stamp = msg->header.stamp;
        posesPub_->publish(poses);
        localMapPub_->publish(localMap);
        sdfKeypointsPub_->publish(sdfKeypointsMarker_);
        localSDFKeypointsPub_->publish(localSDFKeypointsMarker);
    }
}

void GLPoseSampler::odomCB(const nav_msgs::msg::Odometry::SharedPtr msg)
{
    
    //RCLCPP_INFO(this->get_logger(), "gl pose sampler subscribed odom.");
    // print a message to show that odom callback is running
    // RCLCPP_INFO(this->get_logger(), "Odom callback is running...");
    tf2::Quaternion q(msg->pose.pose.orientation.x,
                        msg->pose.pose.orientation.y,
                        msg->pose.pose.orientation.z,
                        msg->pose.pose.orientation.w);
    double roll, pitch, yaw;
    tf2::Matrix3x3 m(q);
    m.getRPY(roll, pitch, yaw);
    odomPose_.setPose(msg->pose.pose.position.x, msg->pose.pose.position.y, yaw);
    gotOdom_ = true;
}

void GLPoseSampler::setMapInfo(nav_msgs::msg::OccupancyGrid map)
{
    mapWidth_ = map.info.width;
    mapHeight_ = map.info.height;
    mapResolution_ = map.info.resolution;
    mapOrigin_.setX(map.info.origin.position.x);
    mapOrigin_.setY(map.info.origin.position.y);
    tf2::Quaternion q(map.info.origin.orientation.x,
                      map.info.origin.orientation.y,
                      map.info.origin.orientation.z,
                      map.info.origin.orientation.w);
    double roll, pitch, yaw;
    tf2::Matrix3x3 m(q);
    m.getRPY(roll, pitch, yaw);
    mapOrigin_.setYaw(yaw);
    mapData_ = map.data;
}

cv::Mat GLPoseSampler::buildDistanceFieldMap(nav_msgs::msg::OccupancyGrid map)
{
    cv::Mat binMap(map.info.height, map.info.width, CV_8UC1);
    for (int v = 0; v < (int)map.info.height; v++)
    {
        for (int u = 0; u < (int)map.info.width; u++)
        {
            int n = v * map.info.width + u;
            int val = map.data[n];
            if (val == 100)
                binMap.at<uchar>(v, u) = 0;
            else
                binMap.at<uchar>(v, u) = 1;
        }
    }

    cv::Mat distMap(map.info.height, map.info.width, CV_32FC1);
    cv::distanceTransform(binMap, distMap, cv::DIST_L2, 5);
    for (int v = 0; v < (int)map.info.height; v++)
    {
        for (int u = 0; u < (int)map.info.width; u++)
        {
            float d = distMap.at<float>(v, u) * (float)map.info.resolution;
            distMap.at<float>(v, u) = d;
        }
    }
    return distMap;
}

std::vector<Keypoint> GLPoseSampler::detectKeypoints(nav_msgs::msg::OccupancyGrid map, cv::Mat distMap)
{
    std::vector<Keypoint> keypoints;
    tf2::Quaternion q(map.info.origin.orientation.x,
                        map.info.origin.orientation.y,
                        map.info.origin.orientation.z,
                        map.info.origin.orientation.w);
    double roll, pitch, yaw;
    tf2::Matrix3x3 m(q);
    m.getRPY(roll, pitch, yaw);
    mapOrigin_.setYaw(yaw);
    for (int u = 1; u < (int)map.info.width - 1; ++u)
    {
        for (int v = 1; v < (int)map.info.height - 1; ++v)
        {
            int n = v * map.info.width + u;
            if (map.data[n] != 0 || distMap.at<float>(v, u) < keypointsMinDistFromMap_)
                continue;

            float dx = -distMap.at<float>(v - 1, u - 1) - distMap.at<float>(v, u - 1) - distMap.at<float>(v + 1, u - 1) + distMap.at<float>(v - 1, u + 1) + distMap.at<float>(v, u + 1) + distMap.at<float>(v + 1, u + 1);
            float dy = -distMap.at<float>(v - 1, u - 1) - distMap.at<float>(v - 1, u) - distMap.at<float>(v - 1, u + 1) + distMap.at<float>(v + 1, u - 1) + distMap.at<float>(v + 1, u) + distMap.at<float>(v + 1, u + 1);

            float dxx = distMap.at<float>(v, u - 1) - 2.0 * distMap.at<float>(v, u) + distMap.at<float>(v, u + 1);
            float dyy = distMap.at<float>(v - 1, u) - 2.0 * distMap.at<float>(v, u) + distMap.at<float>(v + 1, u);
            float dxy = distMap.at<float>(v - 1, u - 1) - distMap.at<float>(v - 1, u) - distMap.at<float>(v, u - 1) + 2.0 * distMap.at<float>(v, u) - distMap.at<float>(v, u + 1) - distMap.at<float>(v + 1, u) + distMap.at<float>(v + 1, u + 1);
            float det = dxx * dyy - dxy * dxy;

            if (dx * dx < gradientSquareTH_ && dy * dy < gradientSquareTH_)
            {
                double xx = (double)u * map.info.resolution;
                double yy = (double)v * map.info.resolution;
                double dx = xx * cos(yaw) - yy * sin(yaw);
                double dy = xx * sin(yaw) + yy * cos(yaw);
                double x = dx + map.info.origin.position.x;
                double y = dy + map.info.origin.position.y;
                if (det > 0.0 && dxx < 0.0) // local maxima
                    keypoints.push_back(Keypoint(u, v, x, y, 1));
                else if (det > 0.0 && dxx > 0.0) // local minima
                    keypoints.push_back(Keypoint(u, v, x, y, -1));
                else if (det < 0.0) // saddle
                    keypoints.push_back(Keypoint(u, v, x, y, 0));
            }
        }
    }
    return keypoints;
}

std::vector<SDFOrientationFeature> GLPoseSampler::calculateFeatures(cv::Mat distMap, std::vector<Keypoint> keypoints)
{
    std::vector<SDFOrientationFeature> features((int)keypoints.size());
    for (int i = 0; i < (int)keypoints.size(); ++i)
    {
        int r = (int)(sdfFeatureWindowSize_ / mapResolution_);
        int uo = keypoints[i].getU();
        int vo = keypoints[i].getV();
        float distSum = 0.0f;
        int cellNum = 0;
        std::vector<int> orientHist(36);
        std::vector<double> orientations;

        for (int u = uo - r; u <= uo + r; ++u)
        {
            for (int v = vo - r; v <= vo + r; ++v)
            {
                if (u < 1 || distMap.cols - 1 < u || v < 1 || distMap.rows - 1 < v)
                    continue;

                distSum += distMap.at<float>(v, u);
                cellNum++;

                float dx = -distMap.at<float>(v - 1, u - 1) - distMap.at<float>(v, u - 1) - distMap.at<float>(v + 1, u - 1) + distMap.at<float>(v - 1, u + 1) + distMap.at<float>(v, u + 1) + distMap.at<float>(v + 1, u + 1);
                float dy = -distMap.at<float>(v - 1, u - 1) - distMap.at<float>(v - 1, u) - distMap.at<float>(v - 1, u + 1) + distMap.at<float>(v + 1, u - 1) + distMap.at<float>(v + 1, u) + distMap.at<float>(v + 1, u + 1);
                double t = atan2((double)dy, (double)dx) * 180.0 / M_PI;
                if (t < 0.0)
                    t += 360.0;
                int orientIdx = (int)(t / 10.0);
                if (0 <= orientIdx && orientIdx < 36)
                {
                    orientHist[orientIdx]++;
                    orientations.push_back(t);
                }
            }
        }

        float distAve = distSum / (float)cellNum;

        int maxVal = orientHist[0];
        double domOrient = 0.0;
        for (int j = 1; j < (int)orientHist.size(); ++j)
        {
            if (orientHist[j] > maxVal)
            {
                maxVal = orientHist[j];
                domOrient = (double)j * 10.0;
            }
        }

        std::vector<int> relOrientHist(17);
        for (int j = 0; j < (int)orientations.size(); ++j)
        {
            double dt = domOrient - orientations[j];
            while (dt > 180.0)
                dt -= 360.0;
            while (dt < -180.0)
                dt += 360.0;
            int relOrientIdx = (int)(fabs(dt) / 10.0);
            if (0 <= relOrientIdx && relOrientIdx < 17)
                relOrientHist[relOrientIdx]++;
        }

        SDFOrientationFeature feature(domOrient * M_PI / 180.0, (double)distAve, relOrientHist);
        features[i] = feature;
    }
    return features;
}

visualization_msgs::msg::Marker GLPoseSampler::makeSDFKeypointsMarker(std::vector<Keypoint> keypoints, std::string frame)
{
    visualization_msgs::msg::Marker marker;
    marker.header.frame_id = frame;
    marker.ns = "gl_marker_namespace";
    marker.id = 0;
    marker.type = visualization_msgs::msg::Marker::SPHERE_LIST;
    marker.action = visualization_msgs::msg::Marker::ADD;
    marker.scale.x = 0.2;
    marker.scale.y = 0.2;
    marker.scale.z = 0.2;
    std_msgs::msg::ColorRGBA c;
    c.a = 1.0;
    marker.points.resize((int)keypoints.size());
    marker.colors.resize((int)keypoints.size());
    for (int i = 0; i < (int)keypoints.size(); ++i)
    {
        geometry_msgs::msg::Point p;
        p.x = keypoints[i].getX();
        p.y = keypoints[i].getY();
        p.z = 0.0;
        char type = keypoints[i].getType();
        if (type == 1)
            c.r = 1.0, c.g = 0.0, c.b = 1.0;
        else if (type == -1)
            c.r = 0.0, c.g = 1.0, c.b = 1.0;
        else
            c.r = 1.0, c.g = 1.0, c.b = 0.0;
        marker.points[i] = p;
        marker.colors[i] = c;
    }
    return marker;
}

nav_msgs::msg::OccupancyGrid GLPoseSampler::buildLocalMap(void)
{
    nav_msgs::msg::OccupancyGrid map;
    map.header.frame_id = odomFrame_;

    double rangeMax = keyScans_[0].range_max;
    map.info.width = (int)(rangeMax * 3.0 / mapResolution_);
    map.info.height = (int)(rangeMax * 3.0 / mapResolution_);
    map.info.resolution = mapResolution_;
    map.info.origin.position.x = keyPoses_[0].getX() - rangeMax * 1.5;
    map.info.origin.position.y = keyPoses_[0].getY() - rangeMax * 1.5;
    map.info.origin.orientation.w = 1.0;
    map.data.resize(map.info.width * map.info.height, -1);

    for (int i = 0; i < (int)keyScans_.size(); ++i)
    {
        double yaw = baseLink2Laser_.getYaw();
        double c = cos(yaw);
        double s = sin(yaw);
        double sensorX = baseLink2Laser_.getX() * c - baseLink2Laser_.getY() * s + keyPoses_[i].getX();
        double sensorY = baseLink2Laser_.getX() * s + baseLink2Laser_.getY() * c + keyPoses_[i].getY();
        double sensorYaw = yaw + keyPoses_[i].getYaw();
        sensor_msgs::msg::LaserScan scan = keyScans_[i];
        for (int j = 0; j < (int)scan.ranges.size(); ++j)
        {
            double range = scan.ranges[j];
            if (range < scan.range_min || scan.range_max < range)
                continue;
            if (range < keypointsMinDistFromMap_)
                continue;

            double t = (double)j * scan.angle_increment + scan.angle_min + sensorYaw;
            double x = sensorX;
            double y = sensorY;
            double dx = mapResolution_ * cos(t);
            double dy = mapResolution_ * sin(t);
            for (double r = 0.0; r < range - mapResolution_; r += mapResolution_)
            {
                int u = (int)((x - map.info.origin.position.x) / map.info.resolution);
                int v = (int)((y - map.info.origin.position.y) / map.info.resolution);
                if (0 < u && u < (int)map.info.width && 0 < v && v < (int)map.info.height)
                {
                    int n = v * map.info.width + u;
                    map.data[n] = 0;
                }
                x += dx;
                y += dy;
            }
            x = range * cos(t) + sensorX;
            y = range * sin(t) + sensorY;
            int u = (int)((x - map.info.origin.position.x) / map.info.resolution);
            int v = (int)((y - map.info.origin.position.y) / map.info.resolution);
            if (0 < u && u < (int)map.info.width && 0 < v && v < (int)map.info.height)
            {
                int n = v * map.info.width + u;
                map.data[n] = 100;
            }
        }
    }
    return map;
}

std::vector<int> GLPoseSampler::findCorrespondingFeatures(std::vector<Keypoint> localSDFKeypoints, std::vector<SDFOrientationFeature> localFeatures)
{
    std::vector<int> correspondingIndices((int)localSDFKeypoints.size());
    for (int i = 0; i < (int)localSDFKeypoints.size(); ++i)
    {
        char localKeypointType = localSDFKeypoints[i].getType();
        double localAverageSDF = localFeatures[i].getAverageSDF();
        std::vector<int> localRelOrientHist = localFeatures[i].getRelativeOrientationHist();
        bool isFirst = true, isSecond = true;
        int idx1 = 0, idx2 = 0, min1 = -1, min2 = -1;
        for (int j = 0; j < (int)sdfKeypoints_.size(); ++j)
        {
            if (localKeypointType != sdfKeypoints_[j].getType())
                continue;
            double dAverageSDF = localAverageSDF - sdfOrientationFeatures_[j].getAverageSDF();
            if (fabs(dAverageSDF) > averageSDFDeltaTH_)
                continue;

            int sum = 0;
            for (int k = 0; k < 17; ++k)
                sum += abs(localRelOrientHist[k] - sdfOrientationFeatures_[j].getRelativeOrientationHist(k));

            if (isFirst)
            {
                min1 = sum;
                idx1 = j;
                isFirst = false;
            }
            else if (isSecond)
            {
                if (min1 < sum)
                {
                    min2 = sum;
                    idx2 = j;
                }
                else
                {
                    min2 = min1;
                    idx2 = idx1;
                    min1 = sum;
                    idx1 = j;
                }
                isSecond = false;
            }
            else if (min1 > sum)
            {
                min2 = min1;
                idx2 = idx1;
                min1 = sum;
                idx1 = j;
            }
        }

        if (min1 >= 0 && min2 >= 0 && (float)min1 * 1.5f < (float)min2)
            correspondingIndices[i] = idx1;
        else if (min1 >= 0 && min2 < 0)
            correspondingIndices[i] = idx1;
        else
            correspondingIndices[i] = -1;
    }
    return correspondingIndices;
}

double GLPoseSampler::computeMatchingRate(Pose pose)
{
    double yaw = baseLink2Laser_.getYaw();
    double c = cos(yaw);
    double s = sin(yaw);
    double sensorX = baseLink2Laser_.getX() * c - baseLink2Laser_.getY() * s + pose.getX();
    double sensorY = baseLink2Laser_.getX() * s + baseLink2Laser_.getY() * c + pose.getY();
    double sensorYaw = yaw + pose.getYaw();
    sensor_msgs::msg::LaserScan scan = keyScans_[(int)keyScans_.size() - 1];

    int validScanNum = 0, matchingNum = 0;
    for (int i = 0; i < (int)scan.ranges.size(); ++i)
    {
        double r = scan.ranges[i];

        if (r < scan.range_min || scan.range_max < r)
            continue;
        if (r < keypointsMinDistFromMap_)
            continue;

        validScanNum++;
        double t = (double)i * scan.angle_increment + scan.angle_min + sensorYaw;
        double x = r * cos(t) + sensorX;
        double y = r * sin(t) + sensorY;
        int u, v;
        xy2uv(x, y, &u, &v);
        if (1 <= u && u < mapWidth_ - 1 && 1 <= v && v < mapHeight_ - 1)
        {
            int n0 = v * mapWidth_ + u;
            int n1 = n0 - mapWidth_;
            int n2 = n0 - 1;
            int n3 = n0 + 1;
            int n4 = n0 + mapWidth_;
            if (mapData_[n0] == 100 || mapData_[n1] == 100 || mapData_[n2] == 100 || mapData_[n3] == 100 || mapData_[n4] == 100)
                matchingNum++;
        }
    }
    return (double)matchingNum / (double)validScanNum;
}

geometry_msgs::msg::PoseArray GLPoseSampler::generatePoses(Pose currentOdomPose, std::vector<Keypoint> localSDFKeypoints,
                                            std::vector<SDFOrientationFeature> localSDFOrientationFeatures, std::vector<int> correspondingIndices)
{
    geometry_msgs::msg::PoseArray poses;
    poses.header.frame_id = mapFrame_;
    for (int i = 0; i < (int)correspondingIndices.size(); ++i)
    {
        int idx = correspondingIndices[i];
        if (idx < 0)
            continue;

        double dx = localSDFKeypoints[i].getX() - currentOdomPose.getX();
        double dy = localSDFKeypoints[i].getY() - currentOdomPose.getY();
        double localDomOrient = localSDFOrientationFeatures[i].getDominantOrientation();
        double dOrient = currentOdomPose.getYaw() - localDomOrient;
        Keypoint targetKeypoint = sdfKeypoints_[idx];
        double targetDomOrient = sdfOrientationFeatures_[idx].getDominantOrientation();

        double dDomOrient = localDomOrient - targetDomOrient;
        double c = cos(dDomOrient);
        double s = sin(dDomOrient);
        double sensorX = dx * c - dy * s + targetKeypoint.getX();
        double sensorY = dx * s + dy * c + targetKeypoint.getY();
        double sensorYaw = targetDomOrient + dOrient;

        int u, v;
        xy2uv(sensorX, sensorY, &u, &v);
        if (u < 0 || mapWidth_ <= u || v < 0 || mapHeight_ <= v)
            continue;
        int n = v * mapWidth_ + u;
        if (mapData_[n] != 0)
            continue;

        double byaw = baseLink2Laser_.getYaw();
        double bc = cos(byaw);
        double bs = sin(byaw);
        double baseX = sensorX - baseLink2Laser_.getX() * bc + baseLink2Laser_.getY() * bs;
        double baseY = sensorY - baseLink2Laser_.getX() * bs - baseLink2Laser_.getY() * bc;
        double baseYaw = sensorYaw - byaw;

        if (!addRandomSamples_)
        {
            if (matchingRateTH_ > 0.0)
            {
                if (computeMatchingRate(Pose(baseX, baseY, baseYaw)) < matchingRateTH_)
                    continue;
            }
            geometry_msgs::msg::Pose pose;
            pose.position.x = baseX;
            pose.position.y = baseY;
            tf2::Quaternion q;
            q.setRPY(0, 0, baseYaw);
            pose.orientation = tf2::toMsg(q);
            poses.poses.push_back(pose);
        }
        else
        {
            for (int j = 0; j < randomSamplesNum_; ++j)
            {
                double x = baseX + nrand(positionalRandomNoise_);
                double y = baseY + nrand(positionalRandomNoise_);
                double yaw;
                if (addOppositeSamples_ && j % 2 == 1)
                    yaw = baseYaw + M_PI + nrand(angularRandomNoise_);
                else
                    yaw = baseYaw + nrand(angularRandomNoise_);
                if (matchingRateTH_ > 0.0)
                {
                    if (computeMatchingRate(Pose(x, y, yaw)) < matchingRateTH_)
                        continue;
                }
                geometry_msgs::msg::Pose pose;
                pose.position.x = x;
                pose.position.y = y;
                tf2::Quaternion q;
                q.setRPY(0, 0, yaw);
                pose.orientation = tf2::toMsg(q);
                poses.poses.push_back(pose);
            }
        }
    }
    return poses;
}

} // namespace localization

#include <rclcpp_components/register_node_macro.hpp>
// 注册组件
RCLCPP_COMPONENTS_REGISTER_NODE(localization::GLPoseSampler)