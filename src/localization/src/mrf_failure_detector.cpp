
#include "localization/mrf_failure_detector.h"

namespace localization {


MRFFD::MRFFD(const rclcpp::NodeOptions & options)
    : nav2_util::LifecycleNode("mrffd", "", options)
    , canUpdateResidualErrors_(true)
{
    RCLCPP_INFO(get_logger(), "Creating");
}

MRFFD::~MRFFD()
{
    //
}

nav2_util::CallbackReturn
MRFFD::on_configure(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Configuring");
    // 初始化参数
    initParameters();
    // 初始化发布者和订阅者
    initPubSub();
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MRFFD::on_activate(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Activating");
    // 激活所有发布者
    failureProbPub_->on_activate();
    if (publishClassifiedScans_) {
        alignedScanPub_->on_activate();
        misalignedScanPub_->on_activate();
        unknownScanPub_->on_activate();
    }
    if (publishFailureProbabilityMarker_) {
        failureProbabilityMarkerPub_->on_activate();
    }

    createBond();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MRFFD::on_deactivate(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Deactivating");
    // 停用所有发布者
    failureProbPub_->on_deactivate();
    if (publishClassifiedScans_) {
        alignedScanPub_->on_deactivate();
        misalignedScanPub_->on_deactivate();
        unknownScanPub_->on_deactivate();
    }
    if (publishFailureProbabilityMarker_) {
        failureProbabilityMarkerPub_->on_deactivate();
    }

    destroyBond();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MRFFD::on_cleanup(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Cleaning up");
    // 清理发布者和订阅者
    failureProbPub_.reset();
    alignedScanPub_.reset();
    misalignedScanPub_.reset();
    unknownScanPub_.reset();
    failureProbabilityMarkerPub_.reset();
    residualErrorsSub_.reset();
    
    return nav2_util::CallbackReturn::SUCCESS;
}

nav2_util::CallbackReturn
MRFFD::on_shutdown(const rclcpp_lifecycle::State & /*state*/)
{
    RCLCPP_INFO(get_logger(), "Shutting down");
    return nav2_util::CallbackReturn::SUCCESS;
}

void MRFFD::initParameters()
{
    this->declare_parameter("residual_errors_name", "/residual_errors");
    this->declare_parameter("failure_probability_name", "/failure_probability");
    this->declare_parameter("publish_classified_scans", true);
    this->declare_parameter("aligned_scan_mrf", "/aligned_scan_mrf");
    this->declare_parameter("misaligned_scan_mrf", "/misaligned_scan_mrf");
    this->declare_parameter("unknown_scan_mrf", "/unknown_scan_mrf");
    this->declare_parameter("failure_probability_marker_name", "/failure_probability_marker");
    this->declare_parameter("publish_failure_probability_marker", true);
    this->declare_parameter("marker_frame", "base_link");
    this->declare_parameter("normal_distribution_mean", 0.0);  // NDMean_
    this->declare_parameter("normal_distribution_var", 0.04);  // NDVar_
    this->declare_parameter("exponential_distribution_lambda", 4.0);  // EDLambda_
    this->declare_parameter("max_residual_error", 1.0);  // maxResidualError_
    this->declare_parameter("residual_error_resolution", 0.05);  // residualErrorReso_
    this->declare_parameter("min_valid_residual_errors_num", 10);  // minValidResidualErrorsNum_
    this->declare_parameter("max_residual_errors_num", 200);  // maxResidualErrorsNum_
    this->declare_parameter("max_lpb_computation_num", 1000);  // maxLPBComputationNum_
    this->declare_parameter("sampling_num", 1000);  // samplingNum_
    this->declare_parameter("misalignment_ratio_threshold", 0.1);  // misalignmentRatioThreshold_
    this->declare_parameter("unknown_ratio_threshold", 0.7);  // unknownRatioThreshold_
    this->declare_parameter("transition_probability_matrix", 
        std::vector<double>{0.8, 0.0, 0.2, 0.0, 0.8, 0.2, 0.333333, 0.333333, 0.333333});
    this->declare_parameter("failure_detection_hz", 10.0);  // failureDetectionHz_

    this->get_parameter("residual_errors_name", residualErrorsName_);
    this->get_parameter("failure_probability_name", failureProbName_);
    this->get_parameter("publish_classified_scans", publishClassifiedScans_);
    this->get_parameter("aligned_scan_mrf", alignedScanName_);
    this->get_parameter("misaligned_scan_mrf", misalignedScanName_);
    this->get_parameter("unknown_scan_mrf", unknownScanName_);
    this->get_parameter("failure_probability_marker_name", failureProbabilityMarkerName_);
    this->get_parameter("publish_failure_probability_marker", publishFailureProbabilityMarker_);
    this->get_parameter("marker_frame", markerFrame_);
    this->get_parameter("normal_distribution_mean", NDMean_);
    this->get_parameter("normal_distribution_var", NDVar_);
    this->get_parameter("exponential_distribution_lambda", EDLambda_);
    this->get_parameter("max_residual_error", maxResidualError_);
    this->get_parameter("residual_error_resolution", residualErrorReso_);
    this->get_parameter("min_valid_residual_errors_num", minValidResidualErrorsNum_);
    this->get_parameter("max_residual_errors_num", maxResidualErrorsNum_);
    this->get_parameter("max_lpb_computation_num", maxLPBComputationNum_);
    this->get_parameter("sampling_num", samplingNum_);
    this->get_parameter("misalignment_ratio_threshold", misalignmentRatioThreshold_);
    this->get_parameter("unknown_ratio_threshold", unknownRatioThreshold_);
    this->get_parameter("transition_probability_matrix", transitionProbMat_);
    this->get_parameter("failure_detection_hz", failureDetectionHz_);

    NDNormConst_ = 1.0 / sqrt(2.0 * M_PI * NDVar_);
}

void MRFFD::initPubSub()
{
    // 创建订阅者 - 只在回调中存储数据
    residualErrorsSub_ = this->create_subscription<LaserT>(
        residualErrorsName_, 1,
        std::bind(&MRFFD::residualErrors_callback, this, std::placeholders::_1));

    // 创建发布者
    failureProbPub_ = create_publisher<ProbT>(failureProbName_, 1);

    if (publishClassifiedScans_) {
        alignedScanPub_ = create_publisher<LaserT>(alignedScanName_, 1);
        misalignedScanPub_ = create_publisher<LaserT>(misalignedScanName_, 1);
        unknownScanPub_ = create_publisher<LaserT>(unknownScanName_, 1);
    }

    if (publishFailureProbabilityMarker_) {
        failureProbabilityMarkerPub_ = create_publisher<MarkerT>(
            failureProbabilityMarkerName_, 1);
    }
}

void MRFFD::predictFailureProbability(const LaserT& residualErrors) {
    std::vector<double> validResidualErrors;
    std::vector<int> validScanIndices;
    for (int i = 0; i < (int)residualErrors.intensities.size(); ++i) {
        double e = residualErrors.intensities[i];
        if (0.0 <= e && e <= maxResidualError_) {
            validResidualErrors.push_back(e);
            validScanIndices.push_back(i);
        }
    }
   
    int validResidualErrorsSize = (int)validResidualErrors.size();
    if (validResidualErrorsSize <= minValidResidualErrorsNum_) {
        RCLCPP_ERROR_STREAM(
          this->get_logger(),
          "WARNING: Number of validResidualErrors is less than the "
          "expected threshold number."
              << " The threshold is " << minValidResidualErrorsNum_
              << ", but the number of validResidualErrors "
              << validResidualErrorsSize << ".");
        failureProbability_ = -1.0;
        return;
    } else if (validResidualErrorsSize <= maxResidualErrorsNum_) {
        usedResidualErrors_ = validResidualErrors;
        usedScanIndices_ = validScanIndices;
    } else {
        usedResidualErrors_.resize(maxResidualErrorsNum_);
        usedScanIndices_.resize(maxResidualErrorsNum_);
        for (int i = 0; i < maxResidualErrorsNum_; ++i) {
            int idx = rand() % (int)validResidualErrors.size();
            usedResidualErrors_[i] = validResidualErrors[idx];
            usedScanIndices_[i] = validScanIndices[idx];
            validResidualErrors.erase(validResidualErrors.begin() + idx);
            validScanIndices.erase(validScanIndices.begin() + idx);
        }
    }

    std::vector<std::vector<double>> likelihoodVectors =
        getLikelihoodVectors(usedResidualErrors_);

    std::vector<std::vector<double>> measurementClassProbabilities =
        estimateMeasurementClassProbabilities(likelihoodVectors);
    setAllMeasurementClassProbabilities(usedResidualErrors_,
                                        measurementClassProbabilities);
    failureProbability_ = predictFailureProbabilityBySampling(measurementClassProbabilities);
}

void MRFFD::publishROSMessages(const LaserT& residualErrors) {
    ProbT failureProbability;
    failureProbability.header.stamp = residualErrors.header.stamp;
    failureProbability.vector.x = failureProbability_;
    failureProbPub_->publish(failureProbability);

    if (publishClassifiedScans_) {
        std::vector<int> residualErrorClasses = getResidualErrorClasses();
        LaserT alignedScan, misalignedScan, unknownScan;
        alignedScan.header = misalignedScan.header = unknownScan.header =
            residualErrors.header;
        alignedScan.range_min = misalignedScan.range_min = unknownScan.range_min =
            residualErrors.range_min;
        alignedScan.range_max = misalignedScan.range_max = unknownScan.range_max =
            residualErrors.range_max;
        alignedScan.angle_min = misalignedScan.angle_min = unknownScan.angle_min =
            residualErrors.angle_min;
        alignedScan.angle_max = misalignedScan.angle_max = unknownScan.angle_max =
            residualErrors.angle_max;
        alignedScan.angle_increment = misalignedScan.angle_increment =
            unknownScan.angle_increment = residualErrors.angle_increment;
        alignedScan.time_increment = misalignedScan.time_increment =
            unknownScan.time_increment = residualErrors.time_increment;
        alignedScan.scan_time = misalignedScan.scan_time = unknownScan.scan_time =
            residualErrors.scan_time;
        int size = (int)residualErrors.ranges.size();
        alignedScan.ranges.resize(size);
        misalignedScan.ranges.resize(size);
        unknownScan.ranges.resize(size);
        alignedScan.intensities.resize(size);
        misalignedScan.intensities.resize(size);
        unknownScan.intensities.resize(size);
        for (int i = 0; i < (int)usedResidualErrors_.size(); ++i) {
            int idx = usedScanIndices_[i];
            if (residualErrorClasses[i] == ALIGNED)
                alignedScan.ranges[idx] = residualErrors.ranges[idx];
            else if (residualErrorClasses[i] == MISALIGNED)
                misalignedScan.ranges[idx] = residualErrors.ranges[idx];
            else
                unknownScan.ranges[idx] = residualErrors.ranges[idx];
        }
        alignedScanPub_->publish(alignedScan);
        misalignedScanPub_->publish(misalignedScan);
        unknownScanPub_->publish(unknownScan);
    }

    if (publishFailureProbabilityMarker_) {
        MarkerT marker;
        marker.header.frame_id = markerFrame_;
        marker.header.stamp = residualErrors.header.stamp;
        marker.ns = "fp_marker_namespace";
        marker.id = 0;
        marker.type = MarkerT::TEXT_VIEW_FACING;
        marker.action = MarkerT::ADD;
        marker.pose.position.x = 0.0;
        marker.pose.position.y = -3.0;
        marker.pose.position.z = 0.0;
        marker.scale.x = 0.0;
        marker.scale.y = 0.0;
        marker.scale.z = 2.0;
        marker.text = "Failure Probability: " + std::to_string((int)(failureProbability_ * 100.0)) + " %";
        marker.color.a = 1.0;
        marker.color.r = 1.0;
        marker.color.g = 1.0;
        marker.color.b = 1.0;
        if (failureProbability_ > 0.1)
            marker.color.r = marker.color.g = 0.0;
        failureProbabilityMarkerPub_->publish(marker);
    }
}

void MRFFD::printFailureProbability(void) {
    RCLCPP_INFO_STREAM(this->get_logger(), "Failure probability = " << failureProbability_ * 100.0  << " [%]");
}

void MRFFD::residualErrors_callback(const LaserT::ConstSharedPtr &msg)
{
    residualErrors_ = *msg;
    predictFailureProbability(residualErrors_);
    publishROSMessages(residualErrors_);
    //printFailureProbability();
}

std::vector<std::vector<double>> MRFFD::getLikelihoodVectors(std::vector<double> validResidualErrors)
{
    std::vector<std::vector<double>> likelihoodVectors((int)validResidualErrors.size());
    double pud = calculateUniformDistribution();
    for (int i = 0; i < (int)likelihoodVectors.size(); i++) {
        likelihoodVectors[i].resize(3);
        likelihoodVectors[i][ALIGNED] = calculateNormalDistribution(validResidualErrors[i]);
        likelihoodVectors[i][MISALIGNED] = calculateExponentialDistribution(validResidualErrors[i]);
        likelihoodVectors[i][UNKNOWN] = pud;
        likelihoodVectors[i] = normalizeVector(likelihoodVectors[i]);
    }
    return likelihoodVectors;
}

std::vector<std::vector<double>> MRFFD::estimateMeasurementClassProbabilities(std::vector<std::vector<double>> likelihoodVectors)
{
    
    if (likelihoodVectors.empty()) {
        RCLCPP_ERROR(get_logger(), "Invalid input parameters");
        return likelihoodVectors;
    }
    std::vector<std::vector<double>> measurementClassProbabilities = likelihoodVectors;
    for (int i = 0; i < (int)measurementClassProbabilities.size(); i++) {
        for (int j = 0; j < (int)measurementClassProbabilities.size(); j++) {
            if (i == j)
                continue;
            std::vector<double> message = calculateTransitionMessage(likelihoodVectors[j]);
            measurementClassProbabilities[i] = getHadamardProduct(measurementClassProbabilities[i], message);
            
            measurementClassProbabilities[i] = normalizeVector(measurementClassProbabilities[i]);
        }
        measurementClassProbabilities[i] = normalizeVector(measurementClassProbabilities[i]);
    }
    
    double variation = 0.0;
    int idx1 = rand() % (int)measurementClassProbabilities.size();
    std::vector<double> message(3);
    message = likelihoodVectors[idx1];
    int checkStep = maxLPBComputationNum_ / 20;
    for (int i = 0; i < maxLPBComputationNum_; i++) {
        int idx2 = rand() % (int)measurementClassProbabilities.size();
        int cnt = 0;
        for (;;) {
            if (idx2 != idx1)
                break;
            idx2 = rand() % (int)measurementClassProbabilities.size();
            cnt++;
            if (cnt >= 10)
                break;
        }
        message = calculateTransitionMessage(message);
        message = getHadamardProduct(likelihoodVectors[idx2], message);
        std::vector<double> measurementClassProbabilitiesPrev = measurementClassProbabilities[idx2];
        measurementClassProbabilities[idx2] = getHadamardProduct(measurementClassProbabilities[idx2], message);
        measurementClassProbabilities[idx2] = normalizeVector(measurementClassProbabilities[idx2]);
        double diffNorm = getEuclideanNormOfDiffVectors(measurementClassProbabilities[idx2],
                                            measurementClassProbabilitiesPrev);
        variation += diffNorm;
        if (i >= checkStep && i % checkStep == 0 && variation < 10e-6)
            break;
        else if (i >= checkStep && i % checkStep == 0)
            variation = 0.0;
        message = measurementClassProbabilities[idx2];
        idx1 = idx2;
    }
    
    return measurementClassProbabilities;
}

double MRFFD::predictFailureProbabilityBySampling(std::vector<std::vector<double>> measurementClassProbabilities)
{
    int failureCnt = 0;
    for (int i = 0; i < samplingNum_; i++) {
        int misalignedNum = 0, validMeasurementNum = 0;
        int measurementNum = (int)measurementClassProbabilities.size();
        for (int j = 0; j < measurementNum; j++) {
            double darts = (double)rand() / ((double)RAND_MAX + 1.0);
            double validProb = measurementClassProbabilities[j][ALIGNED] +
                           measurementClassProbabilities[j][MISALIGNED];
            if (darts > validProb)
                continue;
            validMeasurementNum++;
            if (darts > measurementClassProbabilities[j][ALIGNED])
                misalignedNum++;
        }
        double misalignmentRatio = (double)misalignedNum / (double)validMeasurementNum;
        double unknownRatio = (double)(measurementNum - validMeasurementNum) / (double)measurementNum;
        if (misalignmentRatio >= misalignmentRatioThreshold_ ||
            unknownRatio >= unknownRatioThreshold_)
        failureCnt++;
    }
    double p = (double)failureCnt / (double)samplingNum_;
    return p;
}

void MRFFD::setAllMeasurementClassProbabilities(std::vector<double> residualErrors,
        std::vector<std::vector<double>> measurementClassProbabilities)
{
    measurementClassProbabilities_.resize((int)residualErrors.size());
    int idx = 0;
    for (int i = 0; i < (int)measurementClassProbabilities_.size(); i++) {
        measurementClassProbabilities_[i].resize(3);
        if (0.0 <= residualErrors[i] && residualErrors[i] <= maxResidualError_) {
            measurementClassProbabilities_[i] = measurementClassProbabilities[idx];
            idx++;
        } else {
            measurementClassProbabilities_[i][ALIGNED] = 0.00005;
            measurementClassProbabilities_[i][MISALIGNED] = 0.00005;
            measurementClassProbabilities_[i][UNKNOWN] = 0.9999;
        }
    }
}

std::vector<int> MRFFD::getResidualErrorClasses()
{
    int size = (int)measurementClassProbabilities_.size();
    std::vector<int> residualErrorClasses(size);
    for (int i = 0; i < size; i++) {
        double alignedProb = measurementClassProbabilities_[i][ALIGNED];
        double misalignedProb = measurementClassProbabilities_[i][MISALIGNED];
        double unknownProb = measurementClassProbabilities_[i][UNKNOWN];
        if (alignedProb > misalignedProb && alignedProb > unknownProb)
            residualErrorClasses[i] = ALIGNED;
        else if (misalignedProb > alignedProb && misalignedProb > unknownProb)
            residualErrorClasses[i] = MISALIGNED;
        else
            residualErrorClasses[i] = UNKNOWN;
    }
    return residualErrorClasses;
}

} // namespace localization

#include <rclcpp_components/register_node_macro.hpp>
// 注册组件
RCLCPP_COMPONENTS_REGISTER_NODE(localization::MRFFD)

