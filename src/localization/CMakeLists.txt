cmake_minimum_required(VERSION 3.8)
project(localization)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(rclcpp_lifecycle REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(nav2_util REQUIRED)
find_package(nav2_msgs REQUIRED)
find_package(nav2_common REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(visualization_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(OpenCV REQUIRED)
find_package(yaml-cpp REQUIRED)

# 添加组件库
add_library(gl_pose_sampler SHARED
  src/gl_pose_sampler.cpp
)

# 设置组件库的属性和依赖
ament_target_dependencies(gl_pose_sampler
  rclcpp
  rclcpp_lifecycle
  rclcpp_components
  sensor_msgs
  nav_msgs
  geometry_msgs
  visualization_msgs
  tf2_ros
  OpenCV
  tf2_geometry_msgs
  nav2_util
)


# MCL 组件库
add_library(mcl SHARED
  src/mcl.cpp
)

# MCL 组件库的依赖项
ament_target_dependencies(mcl
  rclcpp
  rclcpp_lifecycle
  rclcpp_components
  sensor_msgs
  nav_msgs
  geometry_msgs
  visualization_msgs
  tf2_ros
  OpenCV
  tf2_geometry_msgs
  nav2_util
  yaml-cpp
)

target_link_libraries(mcl
  ${OpenCV_LIBS}
  yaml-cpp
)

# MCL 组件库
add_library(mrffd SHARED
  src/mrf_failure_detector.cpp
)

# MCL 组件库的依赖项
ament_target_dependencies(mrffd
  rclcpp
  rclcpp_lifecycle
  rclcpp_components
  sensor_msgs
  nav_msgs
  geometry_msgs
  visualization_msgs
  tf2_ros
  OpenCV
  tf2_geometry_msgs
  nav2_util
)

target_include_directories(gl_pose_sampler
  PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

target_include_directories(mcl
  PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

target_include_directories(mrffd
  PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# 注册组件
rclcpp_components_register_nodes(gl_pose_sampler "localization::GLPoseSampler")
# 注册 MCL 组件
rclcpp_components_register_nodes(mcl "localization::MCL")
rclcpp_components_register_nodes(mrffd "localization::MRFFD")


# 安装目标
install(TARGETS gl_pose_sampler
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION lib/${PROJECT_NAME}
)

# 安装 MCL 组件
install(TARGETS mcl
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION lib/${PROJECT_NAME}
)

install(TARGETS mrffd
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION lib/${PROJECT_NAME}
)

install(
  DIRECTORY include/
  DESTINATION include
)

install(
  DIRECTORY classifiers
  DESTINATION share/${PROJECT_NAME}
)

ament_package()


