<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>localization</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>">roald</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>tf2</depend>
  <depend>nav_msgs</depend>
  <depend>nav2_util</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_common</depend>
  <depend>sensor_msgs</depend>
  <depend>libopencv-dev</depend>
  <depend>geometry_msgs</depend>
  <depend>visualization_msgs</depend>
  <depend>yaml-cpp</depend>
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
