#pragma once
#include <localization/mae_classifier.h>
#include <localization/particle.h>
#include <localization/pose.h>

#include <geometry_msgs/msg/pose_array.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <opencv2/opencv.hpp>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <string>
#include <tf2/convert.h>
#include <tf2/transform_datatypes.h>
#include <tf2/transform_storage.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2_ros/create_timer_ros.h>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_ros/transform_listener.h>
#include <nav2_util/lifecycle_node.hpp>
#include <rclcpp_lifecycle/lifecycle_node.hpp>
#include <vector>
#include <atomic>
#include <visualization_msgs/msg/marker.hpp>
#include <mutex>

/**initial  unknownScanProbThreshold_ */

namespace localization {

class MCL : public nav2_util::LifecycleNode {
public:
    using PoseArrayT = geometry_msgs::msg::PoseArray;
    using LaserT = sensor_msgs::msg::LaserScan;
    using OdomT = nav_msgs::msg::Odometry;
    using MapT = nav_msgs::msg::OccupancyGrid;
    using PoseCovStampT = geometry_msgs::msg::PoseWithCovarianceStamped;
    using PoseStampT = geometry_msgs::msg::PoseStamped;
    using MarkerT = visualization_msgs::msg::Marker;
    using ReliabilityT = geometry_msgs::msg::Vector3Stamped;
    using PoseT = geometry_msgs::msg::Pose;

public:
    explicit MCL(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    ~MCL();

protected:
    // Lifecycle 状态回调
    nav2_util::CallbackReturn on_configure(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_activate(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_deactivate(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_cleanup(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_shutdown(const rclcpp_lifecycle::State & state) override;

private:
    void initParameters();
    void initPubSub();
    void initTransforms();
    bool checkTransform();
    void updateCallback();

private:
    inline double nrand(double n) {
        return (n * sqrt(-2.0 * log((double)rand() / RAND_MAX)) *
                cos(2.0 * M_PI * rand() / RAND_MAX));
    }

    inline bool onMap(int u, int v) {
        if (0 <= u && u < mapWidth_ && 0 <= v && v < mapHeight_)
            return true;
        else
            return false;
    }

    inline void xy2uv(double x, double y, int *u, int *v) {
        double dx = x - mapOrigin_.getX();
        double dy = y - mapOrigin_.getY();
        double yaw = -mapOrigin_.getYaw();
        double xx = dx * cos(yaw) - dy * sin(yaw);
        double yy = dx * sin(yaw) + dy * cos(yaw);
        *u = (int)(xx / mapResolution_);
        *v = (int)(yy / mapResolution_);
    }

    inline void uv2xy(int u, int v, double *x, double *y) {
        double xx = (double)u * mapResolution_;
        double yy = (double)v * mapResolution_;
        double yaw = -mapOrigin_.getYaw();
        double dx = xx * cos(yaw) + yy * sin(yaw);
        double dy = -xx * sin(yaw) + yy * cos(yaw);
        *x = dx + mapOrigin_.getX();
        *y = dy + mapOrigin_.getY();
    }

    inline void setCanUpdateScan(bool canUpdateScan) {
        canUpdateScan_.store(canUpdateScan);
    }

    inline bool isScanMightInvalid(const LaserT& scan) {
        int invalidScanNum = 0;
        for (int i = 0; i < (int)scan.ranges.size(); ++i) {
            double r = scan.ranges[i];
            if (r < scan.range_min || scan.range_max < r)
                invalidScanNum++;
        }
        double invalidScanRate =
            (double)invalidScanNum / (int)scan.ranges.size();
        return invalidScanRate > 0.95;
    }

    // inline getting functions
    inline double getLocalizationHz(void) { return localizationHz_; }
    inline std::string getMapFrame(void) { return mapFrame_; }
    inline LaserT getScan(void) { return scan_; }
    inline int getParticlesNum(void) { return particlesNum_; }
    inline Pose getParticlePose(int i) { return particles_[i].getPose(); }
    inline double getParticleW(int i) { return particles_[i].getW(); }
    inline Pose getBaseLink2Laser(void) { return baseLink2Laser_; }
    inline int getScanStep(void) { return scanStep_; }
    inline int getMaxLikelihoodParticleIdx(void) {
        return maxLikelihoodParticleIdx_;
    }
    inline double getNormConstHit(void) { return normConstHit_; }
    inline double getDenomHit(void) { return denomHit_; }
    inline double getZHit(void) { return zHit_; }
    inline double getMeasurementModelRandom(void) {
        return measurementModelRandom_;
    }

    // inline setting functions
    inline void setMCLPoseStamp(rclcpp::Time &stamp) { mclPoseStamp_ = stamp; }
    inline void setParticleW(int i, double w) { particles_[i].setW(w); }
    inline void setTotalLikelihood(double totalLikelihood) {
        totalLikelihood_ = totalLikelihood;
    }
    inline void setAverageLikelihood(double averageLikelihood) {
        averageLikelihood_ = averageLikelihood;
    }
    inline void setMaxLikelihood(double maxLikelihood) {
        maxLikelihood_ = maxLikelihood;
    }
    inline void setMaxLikelihoodParticleIdx(int maxLikelihoodParticleIdx) {
        maxLikelihoodParticleIdx_ = maxLikelihoodParticleIdx;
    }
    inline geometry_msgs::msg::Quaternion createQuaternionMsgFromYaw(double yaw) {
        tf2::Quaternion q;
        q.setRPY(0, 0, yaw);
        return tf2::toMsg(q);
    }

    // inline other functions
    void clearLikelihoodShiftedSteps(void) { likelihoodShiftedSteps_.clear(); }
    void addLikelihoodShiftedSteps(bool flag) {
        likelihoodShiftedSteps_.push_back(flag);
    }

    void updateParticlesByMotionModel();
    void calculateLikelihoodsByMeasurementModel(void);
    void calculateLikelihoodsByDecisionModel(void);
    void calculateGLSampledPosesLikelihood(void);
    void calculateAMCLRandomParticlesRate(void);
    void calculateEffectiveSampleSize(void);
    void estimatePose(void);
    void resampleParticles(void);
    template <typename T> 
    std::vector<T> getResidualErrors(Pose pose);
    void plotScan(void);
    void printResult();
    void publishROSMessages(Pose &pose, const rclcpp::Time &stamp);
    void broadcastTF(Pose &pose);
    void plotLikelihoodMap(void);
    void plotWorld(double plotRange);
    
    void scanCB(const LaserT::ConstSharedPtr &msg);
    void odomCB(const OdomT::ConstSharedPtr &msg);
    void mapCB(const MapT::ConstSharedPtr &msg);
    void glSampledPosesCB(const PoseArrayT::ConstSharedPtr &msg);
    void initialPoseCB(const PoseCovStampT::ConstSharedPtr &msg);
    void resetParticlesDistribution(void);
    void resetReliabilities(void);
    void rejectUnknownScan(void);
    double calculateLikelihoodFieldModel(Pose &pose, double range,
                                    double rangeAngle);
    double calculateBeamModel(Pose &pose, double range, double rangeAngle);
    double calculateClassConditionalMeasurementModel(Pose &pose, double range,
                                                double rangeAngle);
    void estimateUnknownScanWithClassConditionalMeasurementModel(Pose &pose);
    void predictMclPoseByOdomDelta(Pose &pose);

private:
    // sub
    std::string scanName_, odomName_, mapName_, glSampledPosesName_;
    rclcpp::Subscription<LaserT>::SharedPtr scanSub_;
    rclcpp::Subscription<OdomT>::SharedPtr odomSub_;
    rclcpp::Subscription<MapT>::SharedPtr mapSub_;
    rclcpp::Subscription<PoseArrayT>::SharedPtr glSampledPosesSub_;
    rclcpp::Subscription<PoseCovStampT>::SharedPtr initialPoseSub_;

    // pub
    std::string poseName_, particlesName_, unknownScanName_, residualErrorsName_,
        reliabilityName_, reliabilityMarkerName_;
    rclcpp_lifecycle::LifecyclePublisher<PoseStampT>::SharedPtr posePub_;
    rclcpp_lifecycle::LifecyclePublisher<PoseArrayT>::SharedPtr particlesPub_;
    rclcpp_lifecycle::LifecyclePublisher<LaserT>::SharedPtr unknownScanPub_;
    rclcpp_lifecycle::LifecyclePublisher<LaserT>::SharedPtr residualErrorsPub_;
    rclcpp_lifecycle::LifecyclePublisher<ReliabilityT>::SharedPtr reliabilityPub_;
    rclcpp_lifecycle::LifecyclePublisher<MarkerT>::SharedPtr reliabilityMarkerPub_;

    // tf
    std::string laserFrame_, baseLinkFrame_, mapFrame_, odomFrame_;
    bool broadcastTF_, useOdomTF_;

    // pose
    double initialPoseX_, initialPoseY_, initialPoseYaw_;
    Pose mclPose_, baseLink2Laser_, odomPose_;
    rclcpp::Time mclPoseStamp_, odomPoseStamp_, glSampledPosesStamp_, lastOdomStamp_;

    // particles
    int particlesNum_;
    std::vector<Particle> particles_;
    double initialNoiseX_, initialNoiseY_, initialNoiseYaw_;
    bool useAugmentedMCL_, addRandomParticlesInResampling_;
    double randomParticlesRate_;
    std::vector<double> randomParticlesNoise_;
    int glParticlesNum_;
    std::vector<Particle> glParticles_;
    PoseArrayT glSampledPoses_;
    bool canUpdateGLSampledPoses_, canUseGLSampledPoses_,
        isGLSampledPosesUpdated_;
    double glSampledPoseTimeTH_, gmmPositionalVariance_,
            gmmAngularVariance_;
    double predDistUnifRate_;

    // map
    cv::Mat distMap_;
    double mapResolution_;
    Pose mapOrigin_;
    int mapWidth_, mapHeight_;
    std::atomic_bool gotMap_;

    std::atomic_bool gotScan_;

    // motion
    double deltaX_, deltaY_, deltaDist_, deltaYaw_;
    double deltaXSum_ = 0., deltaYSum_ = 0., deltaDistSum_ = 0.,
            deltaYawSum_ = 0., deltaTimeSum_ = 0.;
    std::vector<double> resampleThresholds_;
    std::vector<double> odomNoiseDDM_, odomNoiseODM_;
    bool useOmniDirectionalModel_;

    // measurements
    LaserT scan_, unknownScan_;
    std::atomic_bool canUpdateScan_ = true;
    std::vector<bool> likelihoodShiftedSteps_;

    // measurement model
    // 0: likelihood field model, 1: beam model, 2: class conditional measurement
    // model
    int measurementModelType_;
    double zHit_, zShort_, zMax_, zRand_;
    double varHit_, lambdaShort_, lambdaUnknown_;
    double normConstHit_, denomHit_, pRand_;
    double measurementModelRandom_, measurementModelInvalidScan_;
    double pKnownPrior_, pUnknownPrior_, unknownScanProbThreshold_;
    double alphaSlow_, alphaFast_, omegaSlow_, omegaFast_;
    int scanStep_;
    bool rejectUnknownScan_, publishUnknownScan_,
        publishResidualErrors_;
    double resampleThresholdESS_;

    // localization result
    double totalLikelihood_, averageLikelihood_, maxLikelihood_;
    double amclRandomParticlesRate_, effectiveSampleSize_;
    int maxLikelihoodParticleIdx_;

    // other parameters
    std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
    std::unique_ptr<tf2_ros::Buffer> tf_buffer_;
    std::unique_ptr<tf2_ros::TransformBroadcaster> tfBroadcaster_;

    std::atomic_bool isFirstPose_;
    double localizationHz_;
    double transformTolerance_;

    // reliability estimation
    bool estimateReliability_ = true;
    int classifierType_;
    double reliability_;
    std::vector<double> reliabilities_, glSampledPosesReliabilities_;
    std::vector<double> relTransDDM_, relTransODM_;

    // mean absolute error (MAE)-based failure detector
    MAEClassifier maeClassifier_;
    std::string maeClassifierDir_;
    std::vector<double> maes_, glSampledPosesMAEs_;

    // global-localization-based pose sampling
    bool useGLPoseSampler_, fuseGLPoseSamplerOnlyUnreliable_;

    // constant parameters
    const double rad2deg_ = 180.0 / M_PI;
    std::shared_ptr<rclcpp::TimerBase> worker_timer_;
    geometry_msgs::msg::TransformStamped tfBaseLink2Laser_;
    bool tfBaseLink2LaserValid_;
    bool paramsInitialized_;
    std::atomic<bool> need_update_{false};
    double update_dist_thresh_;
    double update_yaw_thresh_;
    std::mutex odom_mutex_;

};
} // namespace als_ros
