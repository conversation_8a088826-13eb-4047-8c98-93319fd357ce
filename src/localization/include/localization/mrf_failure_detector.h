#pragma once
#include <cmath>
#include <functional>
#include <geometry_msgs/msg/vector3_stamped.hpp>
#include <iostream>
#include <rclcpp/rclcpp.hpp>
#include <nav2_util/lifecycle_node.hpp>
#include <rclcpp_lifecycle/lifecycle_node.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <vector>
#include <visualization_msgs/msg/marker.hpp>

namespace localization {

enum Measurement { ALIGNED = 0, MISALIGNED = 1, UNKNOWN = 2 };

class MRFFD : public nav2_util::LifecycleNode {
public:
    using LaserT = sensor_msgs::msg::LaserScan;
    using MarkerT = visualization_msgs::msg::Marker;
    using ProbT = geometry_msgs::msg::Vector3Stamped;

public:
    explicit MRFFD(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    ~MRFFD();

protected:
    nav2_util::CallbackReturn on_configure(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_activate(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_deactivate(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_cleanup(const rclcpp_lifecycle::State & state) override;
    nav2_util::CallbackReturn on_shutdown(const rclcpp_lifecycle::State & state) override;

private:
    void initParameters();
    void initPubSub();

    inline double getFailureProbability(void) { return failureProbability_; }
    inline double getMeasurementClassProbabilities(int errorIndex, int measurementClass) {
        return measurementClassProbabilities_[errorIndex][measurementClass];
    }
    inline std::vector<double> getMeasurementClassProbabilities(int errorIndex) {
        return measurementClassProbabilities_[errorIndex];
    }
    inline double getFailureDetectionHz(void) { return failureDetectionHz_; }

    void predictFailureProbability(const LaserT& residualErrors);
    void publishROSMessages(const LaserT& residualErrors);
    void printFailureProbability();

private:
    inline double calculateNormalDistribution(double e) {
        return (0.95 * (2.0 * NDNormConst_ * exp(-((e - NDMean_) * (e - NDMean_)) / (2.0 * NDVar_))) +
                0.05 * (1.0 / maxResidualError_)) * residualErrorReso_;
    }

    inline double calculateExponentialDistribution(double e) {
        return (0.95 * (1.0 / (1.0 - exp(-EDLambda_ * maxResidualError_))) * EDLambda_ * exp(-EDLambda_ * e) +
                0.05 * (1.0 / maxResidualError_)) * residualErrorReso_;
    }

    inline double calculateUniformDistribution(void) {
        return (1.0 / maxResidualError_) * residualErrorReso_;
    }

    inline double getSumOfVecotr(std::vector<double> vector) {
        double sum = 0.0;
        for (int i = 0; i < (int)vector.size(); i++)
            sum += vector[i];
        return sum;
    }

    inline std::vector<double> getHadamardProduct(std::vector<double> vector1,
                                                    std::vector<double> vector2) {
        for (int i = 0; i < (int)vector1.size(); i++)
            vector1[i] *= vector2[i];
        return vector1;
    }

    inline std::vector<double> normalizeVector(std::vector<double> vector) {
        double sum = getSumOfVecotr(vector);
        for (int i = 0; i < (int)vector.size(); i++)
            vector[i] /= sum;
        return vector;
    }

    inline double getEuclideanNormOfDiffVectors(std::vector<double> vector1,
                                                std::vector<double> vector2) {
        double sum = 0.0;
        for (int i = 0; i < (int)vector1.size(); i++) {
            double diff = vector1[i] - vector2[i];
            sum += diff * diff;
        }
        return sqrt(sum);
    }

    inline std::vector<double> calculateTransitionMessage(std::vector<double> probs) {
        std::vector<double> message(3);
        std::vector<double> tm = transitionProbMat_;
        message[ALIGNED] = tm[ALIGNED] * probs[ALIGNED] + tm[MISALIGNED] * probs[MISALIGNED] + tm[UNKNOWN] * probs[UNKNOWN];
        message[MISALIGNED] = tm[ALIGNED + 3] * probs[ALIGNED] + tm[MISALIGNED + 3] * probs[MISALIGNED] + tm[UNKNOWN + 3] * probs[UNKNOWN];
        message[UNKNOWN] = tm[ALIGNED + 6] * probs[ALIGNED] + tm[MISALIGNED + 6] * probs[MISALIGNED] + tm[UNKNOWN + 6] * probs[UNKNOWN];
        return message;
    }

    void residualErrors_callback(const LaserT::ConstSharedPtr &msg);
    std::vector<std::vector<double>> getLikelihoodVectors(std::vector<double> validResidualErrors);
    std::vector<std::vector<double>> estimateMeasurementClassProbabilities(
        std::vector<std::vector<double>> likelihoodVectors);
    double predictFailureProbabilityBySampling(
        std::vector<std::vector<double>> measurementClassProbabilities);
    void setAllMeasurementClassProbabilities(
        std::vector<double> residualErrors,
        std::vector<std::vector<double>> measurementClassProbabilities);
    std::vector<int> getResidualErrorClasses();

private:
    // Member variables
    std::string residualErrorsName_;
    rclcpp::Subscription<LaserT>::SharedPtr residualErrorsSub_;

    std::string failureProbName_, alignedScanName_, misalignedScanName_,
        unknownScanName_;
    rclcpp_lifecycle::LifecyclePublisher<ProbT>::SharedPtr failureProbPub_;
    rclcpp_lifecycle::LifecyclePublisher<LaserT>::SharedPtr alignedScanPub_;
    rclcpp_lifecycle::LifecyclePublisher<LaserT>::SharedPtr misalignedScanPub_;
    rclcpp_lifecycle::LifecyclePublisher<LaserT>::SharedPtr unknownScanPub_;
    bool publishClassifiedScans_;

    std::string failureProbabilityMarkerName_, markerFrame_;
    bool publishFailureProbabilityMarker_;
    rclcpp_lifecycle::LifecyclePublisher<MarkerT>::SharedPtr failureProbabilityMarkerPub_;

    double maxResidualError_;
    double NDMean_, NDVar_, EDLambda_, NDNormConst_;
    int minValidResidualErrorsNum_, maxResidualErrorsNum_;
    int maxLPBComputationNum_;
    int samplingNum_;
    double residualErrorReso_;
    double misalignmentRatioThreshold_, unknownRatioThreshold_;
    std::vector<double> transitionProbMat_;

    LaserT residualErrors_;
    std::vector<double> usedResidualErrors_;
    std::vector<int> usedScanIndices_;
    bool canUpdateResidualErrors_;
    double failureDetectionHz_;

    std::vector<std::vector<double>> measurementClassProbabilities_;
    double failureProbability_;
    rclcpp::Time failureProbabilityStamp_;
};

} // namespace localization
