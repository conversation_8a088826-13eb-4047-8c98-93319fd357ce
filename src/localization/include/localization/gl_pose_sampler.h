/****************************************************************************
 * als_ros: An Advanced Localization System for ROS use with 2D LiDAR
 * Copyright (C) 2022 Naoki Akai
 *
 * Licensed under the Apache License, Version 2.0 (the “License”);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an “AS IS” BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * To implement this program, a following paper was referred.
 * https://arxiv.org/pdf/1908.01863.pdf
 *
 * <AUTHOR>
 * modified by: Roald Ong
 ****************************************************************************/

#pragma once

#include <opencv2/opencv.hpp>

#include <nav_msgs/msg/occupancy_grid.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <geometry_msgs/msg/pose_array.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <visualization_msgs/msg/marker.hpp>

#include "tf2_ros/transform_listener.h"
#include "tf2_ros/buffer.h"
#include <tf2_ros/create_timer_ros.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp/qos.hpp>
#include <nav2_util/lifecycle_node.hpp>
#include <rclcpp_lifecycle/lifecycle_node.hpp>

#include "localization/pose.h"
#include <atomic>

using namespace std::chrono_literals;

namespace localization
{
    /**
     * @brief Represents a keypoint with its coordinates and type.
     *
     * The Keypoint class stores the coordinates (u, v) and the corresponding
     * world coordinates (x, y) of a keypoint. It also stores the type of the
     * keypoint, which can be -2 (invalid), -1 (local minima), 0 (saddle), or
     * 1 (local maxima).
     */
    class Keypoint
    {
    private:
        int u_, v_;
        double x_, y_;
        char type_;
        // type values -2, -1, 0, or 1.
        // -2: invalid, -1: local minima, 0: saddle, 1: local maxima

    public:
        Keypoint(void) : u_(0), v_(0), x_(0.0), y_(0.0), type_(-2) {}

        Keypoint(int u, int v) : u_(u), v_(v), x_(0.0), y_(0.0), type_(-2) {}

        Keypoint(double x, double y) : u_(0), v_(0), x_(x), y_(y), type_(-2) {}

        Keypoint(int u, int v, double x, double y) : u_(u), v_(v), x_(x), y_(y), type_(-2) {}

        Keypoint(int u, int v, double x, double y, char type) : u_(u), v_(v), x_(x), y_(y), type_(type) {}

        inline int getU(void) { return u_; }
        inline int getV(void) { return v_; }
        inline double getX(void) { return x_; }
        inline double getY(void) { return y_; }
        inline char getType(void) { return type_; }

        inline void setU(int u) { u_ = u; }
        inline void setV(int v) { v_ = v; }
        inline void setX(double x) { x_ = x; }
        inline void setY(double y) { y_ = y; }
        inline void setType(char type) { type_ = type; }
    }; // class Keypoint

    /**
     * @brief Represents a feature that describes the orientation of a surface using signed distance fields (SDF).
     */
    class SDFOrientationFeature
    {
    private:
        double dominantOrientation_;
        double averageSDF_;
        std::vector<int> relativeOrientationHist_;

    public:
        /**
         * @brief Default constructor for SDFOrientationFeature.
         */
        SDFOrientationFeature(void) {}

        /**
         * @brief Constructor for SDFOrientationFeature.
         * @param dominantOrientation The dominant orientation of the surface.
         * @param averageSDF The average signed distance field value of the surface.
         * @param relativeOrientationHist The histogram of relative orientations of the surface.
         */
        SDFOrientationFeature(double dominantOrientation, double averageSDF, std::vector<int> relativeOrientationHist) : dominantOrientation_(dominantOrientation),
                                                                                                                         averageSDF_(averageSDF),
                                                                                                                         relativeOrientationHist_(relativeOrientationHist) {}

        /**
         * @brief Get the dominant orientation of the surface.
         * @return The dominant orientation.
         */
        inline double getDominantOrientation(void) { return dominantOrientation_; }

        /**
         * @brief Get the average signed distance field value of the surface.
         * @return The average signed distance field value.
         */
        inline double getAverageSDF(void) { return averageSDF_; }

        /**
         * @brief Get the histogram of relative orientations of the surface.
         * @return The histogram of relative orientations.
         */
        std::vector<int> getRelativeOrientationHist(void) { return relativeOrientationHist_; }

        /**
         * @brief Get the value at the specified index in the histogram of relative orientations.
         * @param idx The index of the value to retrieve.
         * @return The value at the specified index.
         */
        int getRelativeOrientationHist(int idx) { return relativeOrientationHist_[idx]; }
    }; // class SDFOrientationFeature

    class GLPoseSampler : public nav2_util::LifecycleNode
    {
    public:
        explicit GLPoseSampler(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
        ~GLPoseSampler();
        
    protected:
        /*
        * @brief Lifecycle configure
        */
        nav2_util::CallbackReturn on_configure(const rclcpp_lifecycle::State & state) override;
        /*
        * @brief Lifecycle activate
        */
        nav2_util::CallbackReturn on_activate(const rclcpp_lifecycle::State & state) override;
        /*
        * @brief Lifecycle deactivate
        */
        nav2_util::CallbackReturn on_deactivate(const rclcpp_lifecycle::State & state) override;
        /*
        * @brief Lifecycle cleanup
        */
        nav2_util::CallbackReturn on_cleanup(const rclcpp_lifecycle::State & state) override;
        /*
        * @brief Lifecycle shutdown
        */
        nav2_util::CallbackReturn on_shutdown(const rclcpp_lifecycle::State & state) override;


    private:
        void initParameters();
        void initPub();
        void initSub();
        void initTransforms();
        void checkTransform();
        inline double nrand(double n)
        {
            return (n * sqrt(-2.0 * log((double)rand() / RAND_MAX)) * cos(2.0 * M_PI * rand() / RAND_MAX));
        }

        inline void xy2uv(double x, double y, int *u, int *v)
        {
            double dx = x - mapOrigin_.getX();
            double dy = y - mapOrigin_.getY();
            double yaw = -mapOrigin_.getYaw();
            double xx = dx * cos(yaw) - dy * sin(yaw);
            double yy = dx * sin(yaw) + dy * cos(yaw);
            *u = (int)(xx / mapResolution_);
            *v = (int)(yy / mapResolution_);
        }

        inline void uv2xy(int u, int v, double *x, double *y)
        {
            double xx = (double)u * mapResolution_;
            double yy = (double)v * mapResolution_;
            double yaw = mapOrigin_.getYaw();
            double dx = xx * cos(yaw) - yy * sin(yaw);
            double dy = xx * sin(yaw) + yy * cos(yaw);
            *x = dx + mapOrigin_.getX();
            *y = dy + mapOrigin_.getY();
        }

        void mapCB(const nav_msgs::msg::OccupancyGrid::SharedPtr msg);
        void scanCB(const sensor_msgs::msg::LaserScan::SharedPtr msg);
        void odomCB(const nav_msgs::msg::Odometry::SharedPtr msg);

        void setMapInfo(nav_msgs::msg::OccupancyGrid map);

        cv::Mat buildDistanceFieldMap(nav_msgs::msg::OccupancyGrid map);

        std::vector<Keypoint> detectKeypoints(nav_msgs::msg::OccupancyGrid map, cv::Mat distMap);

        std::vector<SDFOrientationFeature> calculateFeatures(cv::Mat distMap, std::vector<Keypoint> keypoints);

        visualization_msgs::msg::Marker makeSDFKeypointsMarker(std::vector<Keypoint> keypoints, std::string frame);

        nav_msgs::msg::OccupancyGrid buildLocalMap(void);

        std::vector<int> findCorrespondingFeatures(std::vector<Keypoint> localSDFKeypoints, std::vector<SDFOrientationFeature> localFeatures);

        double computeMatchingRate(Pose pose);

        geometry_msgs::msg::PoseArray generatePoses(Pose currentOdomPose, std::vector<Keypoint> localSDFKeypoints,
                                                    std::vector<SDFOrientationFeature> localSDFOrientationFeatures, std::vector<int> correspondingIndices);

    private:
        std::string mapName_, scanName_, odomName_, posesName_, localMapName_, sdfKeypointsName_, localSDFKeypointsName_;
        std::string mapFrame_, odomFrame_, baseLinkFrame_, laserFrame_;

        rclcpp::Subscription<nav_msgs::msg::OccupancyGrid>::SharedPtr mapSub_;
        rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr scanSub_;
        rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odomSub_;

        rclcpp_lifecycle::LifecyclePublisher<geometry_msgs::msg::PoseArray>::SharedPtr posesPub_;
        rclcpp_lifecycle::LifecyclePublisher<nav_msgs::msg::OccupancyGrid>::SharedPtr localMapPub_;
        rclcpp_lifecycle::LifecyclePublisher<visualization_msgs::msg::Marker>::SharedPtr sdfKeypointsPub_;
        rclcpp_lifecycle::LifecyclePublisher<visualization_msgs::msg::Marker>::SharedPtr localSDFKeypointsPub_;

        std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
        std::unique_ptr<tf2_ros::Buffer> tf_buffer_;

        Pose baseLink2Laser_;

        int mapWidth_, mapHeight_;
        double mapResolution_;
        Pose mapOrigin_;
        std::vector<signed char> mapData_;
        bool gotMap_;
        bool flipScan_;

        sensor_msgs::msg::LaserScan scan_;
        double keyScanIntervalDist_, keyScanIntervalYaw_;
        std::vector<sensor_msgs::msg::LaserScan> keyScans_;
        int keyScansNum_;
        Pose odomPose_;
        std::vector<Pose> keyPoses_;
        bool gotOdom_;
        std::vector<Keypoint> sdfKeypoints_;
        std::vector<SDFOrientationFeature> sdfOrientationFeatures_;
        visualization_msgs::msg::Marker sdfKeypointsMarker_;

        double gradientSquareTH_;
        double keypointsMinDistFromMap_;
        double sdfFeatureWindowSize_;
        double averageSDFDeltaTH_;
        bool addRandomSamples_, addOppositeSamples_;
        int randomSamplesNum_;
        double positionalRandomNoise_, angularRandomNoise_, matchingRateTH_;

        geometry_msgs::msg::TransformStamped tfBaseLink2Laser;

        std::mutex mutex_;
        std::condition_variable cv_;

        rclcpp::TimerBase::SharedPtr transform_timer_;

    };

} // namespace localization