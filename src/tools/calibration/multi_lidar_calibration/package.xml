<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>multi_lidar_calibration</name>
  <version>0.1.0</version>
  <description>Multi LiDAR calibration package for ROS2</description>
  <maintainer email="<EMAIL>">hanlin</maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_eigen</depend>
  <depend>pcl_conversions</depend>
  <depend>angles</depend>
  <depend>libpcl-all-dev</depend>
  <depend>eigen</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
