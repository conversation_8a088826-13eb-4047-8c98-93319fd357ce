#pragma once

#include <Eigen/Dense>
#include <map>
#include <memory>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <string>
#include <tf2_eigen/tf2_eigen.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <vector>

#include "calibration.hpp"

struct LidarConfig
{
    int id;
    std::string topic;
    Eigen::Matrix4d transform;
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud;
    bool data_received;
};

class MultiLidarCalibrationNode : public rclcpp::Node
{
public:
    explicit MultiLidarCalibrationNode(const rclcpp::NodeOptions &options = rclcpp::NodeOptions());
    ~MultiLidarCalibrationNode();

private:
    void scanCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg, int lidar_id);

    void performCalibration();

    void saveCalibrationResult(const std::map<int32_t, Eigen::Matrix4d> &transforms);
    
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserScanToPointCloud(const sensor_msgs::msg::LaserScan::SharedPtr scan);

    void loadParameters();
    
private:
    bool enable_ground_filter_;
    int master_id_;
    std::string result_path_;

    std::map<int, LidarConfig> lidars_;

    std::unique_ptr<Calibrator> calibrator_;

    std::map<int, rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr> scan_subs_;

    rclcpp::TimerBase::SharedPtr calibration_timer_;

};
