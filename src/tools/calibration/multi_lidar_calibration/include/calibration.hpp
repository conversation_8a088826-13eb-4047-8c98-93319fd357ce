#pragma once

#include <Eigen/Dense>
#include <array>
#include <map>
#include <memory>
#include <pcl/io/pcd_io.h>
#include <string>
#include <vector>

#include <rclcpp/rclcpp.hpp>

#include "registration_icp.hpp"

struct PlaneParam
{
    PlaneParam() : normal(1, 1, 1), intercept(0) {}
    PlaneParam(const Eigen::Vector3d &n, double i) : normal(n), intercept(i) {}
    
    Eigen::Vector3d normal;
    double intercept;
};

class Calibrator
{
public:
    Calibrator() = default;

    Calibrator(const rclcpp::Logger& logger);

    void LoadCalibrationData(
        const int master_id, const bool enable_ground_filter,
        const std::map<int32_t, pcl::PointCloud<pcl::PointXYZI>> &lidar_points,
        const std::map<int32_t, Eigen::Matrix4d> &extrinsics);

    Eigen::Matrix3d GetRotation(double roll, double pitch, double yaw);

    Eigen::Matrix4d GetMatrix(const Eigen::Vector3d &translation, const Eigen::Matrix3d &rotation);

    bool Calibrate();

    bool GroundPlaneExtraction(const pcl::PointCloud<pcl::PointXYZI>::Ptr &in_cloud,
                               pcl::PointCloud<pcl::PointXYZI>::Ptr g_cloud,
                               pcl::PointCloud<pcl::PointXYZI>::Ptr ng_cloud,
                               PlaneParam &plane);

    std::map<int32_t, Eigen::Matrix4d> GetFinalTransformation();

private:
    int master_id_;
    bool enable_ground_filter_;
    std::map<int32_t, pcl::PointCloud<pcl::PointXYZI>> pcs_;
    std::map<int32_t, Eigen::Matrix4d> init_extrinsics_;
    std::map<int32_t, Eigen::Matrix4d> refined_extrinsics_;
    std::unique_ptr<ICPRegistrator> registrator_;

    rclcpp::Logger logger_;
};
