#include "multi_lidar_calibration_node.hpp"

#include <angles/angles.h>
#include <fstream>
#include <pcl/common/transforms.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/io/pcd_io.h>
#include <pcl/segmentation/sac_segmentation.h>

MultiLidarCalibrationNode::MultiLidarCalibrationNode(
    const rclcpp::NodeOptions &options)
    : Node("multi_lidar_calibration_node", options)
{
    RCLCPP_INFO(this->get_logger(), "Starting Multi-LiDAR Calibration Node");

    loadParameters();

    calibrator_.reset(new Calibrator(this->get_logger()));

    for (auto &lidar_pair : lidars_)
    {
        int id = lidar_pair.first;
        auto &lidar = lidar_pair.second;

        lidar.cloud.reset(new pcl::PointCloud<pcl::PointXYZI>());
        lidar.data_received = false;

        scan_subs_[id] = this->create_subscription<sensor_msgs::msg::LaserScan>(
            lidar.topic, 10,
            [this, id](const sensor_msgs::msg::LaserScan::SharedPtr msg) {
            this->scanCallback(msg, id);
            });

        RCLCPP_INFO(this->get_logger(), "Subscribed to LiDAR %d on topic: %s", id, lidar.topic.c_str());
    }

    calibration_timer_ = this->create_wall_timer(
        std::chrono::seconds(2),
        std::bind(&MultiLidarCalibrationNode::performCalibration, this));
}

MultiLidarCalibrationNode::~MultiLidarCalibrationNode() {}

void MultiLidarCalibrationNode::loadParameters()
{
    this->declare_parameter("enable_ground_filter", true);
    this->declare_parameter("master_id", 0);
    this->declare_parameter("result_path", "calibration_result.txt");

    enable_ground_filter_ = this->get_parameter("enable_ground_filter").as_bool();
    master_id_ = this->get_parameter("master_id").as_int();
    result_path_ = this->get_parameter("result_path").as_string();

    RCLCPP_INFO(this->get_logger(), "Ground filter: %s", enable_ground_filter_ ? "enabled" : "disabled");
    RCLCPP_INFO(this->get_logger(), "Master LiDAR ID: %d", master_id_);

    this->declare_parameter("lidar_count", 2);
    int lidar_count = this->get_parameter("lidar_count").as_int();

    for (int i = 0; i < lidar_count; i++)
    {
        std::string prefix = "lidar" + std::to_string(i) + ".";

        this->declare_parameter(prefix + "id", i);
        this->declare_parameter(prefix + "topic", "/scan" + std::to_string(i));
        this->declare_parameter(prefix + "transform", std::vector<double>(16, 0.0));

        LidarConfig config;
        config.id = this->get_parameter(prefix + "id").as_int();
        config.topic = this->get_parameter(prefix + "topic").as_string();

        std::vector<double> transform_values = this->get_parameter(prefix + "transform").as_double_array();

        config.transform = Eigen::Matrix4d::Identity();
        for (int row = 0; row < 4; row++)
        {
            for (int col = 0; col < 4; col++)
            {
                config.transform(row, col) = transform_values[row * 4 + col];
            }
        }

        lidars_[config.id] = config;

        RCLCPP_INFO(this->get_logger(), "Loaded LiDAR %d config: topic=%s", config.id, config.topic.c_str());
    }
}

pcl::PointCloud<pcl::PointXYZI>::Ptr MultiLidarCalibrationNode::laserScanToPointCloud(const sensor_msgs::msg::LaserScan::SharedPtr scan)
{
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZI>());

    for (size_t i = 0; i < scan->ranges.size(); i++)
    {
        float range = scan->ranges[i];

        if (std::isnan(range) || range < scan->range_min || range > scan->range_max)
        {
            continue;
        }

        float angle = scan->angle_min + i * scan->angle_increment;

        pcl::PointXYZI point;
        point.x = range * cos(angle);
        point.y = range * sin(angle);
        point.z = 0.0;

        if (i < scan->intensities.size())
        {
            point.intensity = scan->intensities[i];
        }
        else
        {
            point.intensity = 0.0;
        }

        cloud->push_back(point);
    }

    return cloud;
}

void MultiLidarCalibrationNode::scanCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg, int lidar_id)
{
    if (lidars_.find(lidar_id) == lidars_.end())
    {
        RCLCPP_ERROR(this->get_logger(), "Received scan for unknown LiDAR ID: %d", lidar_id);
        return;
    }

    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud = laserScanToPointCloud(msg);

    *lidars_[lidar_id].cloud = *cloud;
    lidars_[lidar_id].data_received = true;

    RCLCPP_INFO(this->get_logger(), "Received scan from LiDAR %d with %zu points", lidar_id, cloud->size());
}

void MultiLidarCalibrationNode::performCalibration()
{
    for (const auto &lidar_pair : lidars_)
    {
        if (!lidar_pair.second.data_received)
        {
            RCLCPP_INFO(this->get_logger(), "Waiting for data from LiDAR %d...", lidar_pair.first);
            return;
        }
    }

    calibration_timer_->cancel();

    RCLCPP_INFO(this->get_logger(), "Starting calibration process...");

    std::map<int32_t, pcl::PointCloud<pcl::PointXYZI>> lidar_points;
    std::map<int32_t, Eigen::Matrix4d> extrinsics;

    for (const auto &lidar_pair : lidars_)
    {
        int id = lidar_pair.first;
        const auto &lidar = lidar_pair.second;
        lidar_points[id] = *lidar.cloud;
        extrinsics[id] = lidar.transform;
    }

    calibrator_->LoadCalibrationData(master_id_, enable_ground_filter_, lidar_points, extrinsics);


    bool isSuccess = calibrator_->Calibrate();

    if(!isSuccess)
    {
        RCLCPP_ERROR(this->get_logger(), "Calibration failed, try again!");

        for (auto &lidar_pair : lidars_)
        {
            lidar_pair.second.data_received = false;
        }

        calibration_timer_->reset();
    
        return;
    }

    std::map<int32_t, Eigen::Matrix4d> transforms = calibrator_->GetFinalTransformation();

    saveCalibrationResult(transforms);

    RCLCPP_INFO(this->get_logger(), "Calibration completed successfully!");
    
    rclcpp::shutdown();
    exit(0);
}

void MultiLidarCalibrationNode::saveCalibrationResult(const std::map<int32_t, Eigen::Matrix4d> &transforms)
{
    std::ofstream file(result_path_);
    if (!file.is_open())
    {
        RCLCPP_ERROR(this->get_logger(),
                    "Failed to open file for saving calibration result: %s",
                    result_path_.c_str());
        return;
    }

    for (const auto &transform_pair : transforms)
    {
        int id = transform_pair.first;
        const Eigen::Matrix4d &transform = transform_pair.second;

        RCLCPP_INFO(this->get_logger(), "Transform matrix for LiDAR %d:", id);
        RCLCPP_INFO(this->get_logger(), "%.6f %.6f %.6f %.6f", 
                    transform(0,0), transform(0,1), transform(0,2), transform(0,3));
        RCLCPP_INFO(this->get_logger(), "%.6f %.6f %.6f %.6f", 
                    transform(1,0), transform(1,1), transform(1,2), transform(1,3));
        RCLCPP_INFO(this->get_logger(), "%.6f %.6f %.6f %.6f", 
                    transform(2,0), transform(2,1), transform(2,2), transform(2,3));
        RCLCPP_INFO(this->get_logger(), "%.6f %.6f %.6f %.6f", 
                    transform(3,0), transform(3,1), transform(3,2), transform(3,3));

        file << "device_id: " << id << std::endl;
        file << "transform_matrix:" << std::endl;
        file << std::fixed << std::setprecision(6);
        for (int i = 0; i < 4; i++)
        {
            file << transform(i,0) << " " << transform(i,1) << " " 
                 << transform(i,2) << " " << transform(i,3) << std::endl;
        }
        file << std::endl;
    }

    file.close();
    RCLCPP_INFO(this->get_logger(), "Calibration result saved to: %s", result_path_.c_str());
}

int main(int argc, char *argv[])
{
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<MultiLidarCalibrationNode>());
    rclcpp::shutdown();
    return 0;
}