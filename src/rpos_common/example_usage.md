# 如何在其他包中使用 rpos_common 和 jsoncpp (来自 rslamware/3rdparty)

## 1. 在 package.xml 中添加依赖

```xml
<depend>rpos_common</depend>
```

## 2. 在 CMakeLists.txt 中使用

```cmake
find_package(rpos_common REQUIRED)

# 创建你的目标
add_executable(my_node src/my_node.cpp)

# 链接 rpos_common（会自动包含 jsoncpp）
target_link_libraries(my_node rpos_common::rpos_common)

# 或者如果需要直接使用 jsoncpp
target_link_libraries(my_node 
  rpos_common::rpos_common
  rpos_common::jsoncpp_static
)

# ament 依赖
ament_target_dependencies(my_node rpos_common)
```

## 3. 在代码中使用

```cpp
#include <rpos_common/some_header.h>
#include <json/json.h>  // jsoncpp 头文件

int main() {
    // 使用 rpos_common 功能
    // ...
    
    // 使用 jsoncpp 功能
    Json::Value root;
    root["message"] = "Hello World";
    
    return 0;
}
```

## 4. 依赖关系说明

- `rpos_common` 提供静态库，包含所有功能
- `jsoncpp_static` 从 rslamware/3rdparty/jsoncpp 构建的静态库依赖
- jsoncpp 头文件来自 `${CMAKE_SOURCE_DIR}/3rdparty/jsoncpp/include`
- 其他包可以选择只使用 rpos_common，或者同时使用 jsoncpp
- 所有依赖都是静态链接，无运行时依赖
- 确保 rslamware/3rdparty/jsoncpp 目录存在且包含完整的 jsoncpp 源码
