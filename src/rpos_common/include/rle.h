#pragma once

#ifdef _WIN32
#   define RPOS_DEPS_RLE_MODULE_EXPORT __declspec(dllexport)
#   define RPOS_DEPS_RLE_MODULE_IMPORT __declspec(dllimport)
#else
#   define RPOS_DEPS_RLE_MODULE_EXPORT
#   define RPOS_DEPS_RLE_MODULE_IMPORT
#endif

#ifdef RPOS_DEPS_RLE_DLL
#   ifdef RPOS_DEPS_RLE_EXPORT
#       define RPOS_DEPS_RLE_API RPOS_DEPS_RLE_MODULE_EXPORT
#   else
#       define RPOS_DEPS_RLE_API RPOS_DEPS_RLE_MODULE_IMPORT
#   endif
#else
#   define RPOS_DEPS_RLE_API
#endif

#include <string>
#include <stdint.h>

RPOS_DEPS_RLE_API
int RLEEncode( unsigned char *infile, uint32_t inlen,
               unsigned char *outfile, uint32_t *outlen,
               unsigned char &sentinel1, unsigned char &sentinel2,
               uint32_t &i, uint32_t &j);

RPOS_DEPS_RLE_API
int RLEDecode( unsigned char *infile, uint32_t inlen,
               unsigned char *outfile, uint32_t *outlen);
