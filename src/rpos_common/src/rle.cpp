/*---rle.cpp---------------------------------------------------------
*Decode & Encode a file for RLE.
*
* Entry:    RLEEncode(IN unsigned char *infile,// input file buffer
*                     IN uint32_t inlen, // input length
*                     IN unsigned char *outfile, // output file buffer
*                 IN&OUT uint32_t *outlen, // output length
*                     IN unsigned char sentinel1, // default sentinel
*                     IN unsigned char sentinel2 // changed sentinel
*                      );
*
*            RLEEncode() returns:
*                -1: output buffer too small
*                 0: success
*                    
* Entry:    RLEDecode(IN unsigned char *infile, // input file buffer
*                      IN uint32_t inlen,  // input length
*                      IN unsigned char *outfile, // output file length
*                  IN&OUT uint32_t *outlen // output length
*                      );
*
*            RLEDecode() returns:
*                0: success
*                1: outlen too small, and return outlen was needed;
*---------------------------------------------------------------*/

#include "rle.h"
#ifdef DEBUG_SELF
#include <iostream>
#include <fstream>
#endif
#include <string.h>

#if __BIG_ENDIAN__
static inline uint32_t sltc_rle_reverse_byte_order(uint32_t uVal)
{
    uint32_t uRet;
    unsigned char* pDest = (unsigned char*)&uRet;
    const unsigned char* pcSrc = (const unsigned char*)&uVal;
    pDest[0] = pcSrc[3];
    pDest[1] = pcSrc[2];
    pDest[2] = pcSrc[1];
    pDest[3] = pcSrc[0];
    return uRet;
}
static inline uint32_t sltc_rle_cpu_to_le(uint32_t uVal)
{
    return sltc_rle_reverse_byte_order(uVal);
}
static inline uint32_t sltc_rle_le_to_cpu(uint32_t uVal)
{
    return sltc_rle_reverse_byte_order(uVal);
}
#else // __BIG_ENDIAN__
static inline uint32_t sltc_rle_cpu_to_le(uint32_t uVal)
{
    return uVal;
}
static inline uint32_t sltc_rle_le_to_cpu(uint32_t uVal)
{
    return uVal;
}
#endif // __BIG_ENDIAN__

int RLEEncode( unsigned char *infile, uint32_t inlen,
               unsigned char *outfile, uint32_t *outlen,
               unsigned char &sentinel1, unsigned char &sentinel2,
               uint32_t &i, uint32_t &j)
{
    unsigned char sentinel,
                  prev_char;
    unsigned short count = 1;

    sentinel = sentinel1;

    if(i==0 && j==0)
    {
        outfile[ j++ ] = 'R';
        outfile[ j++ ] = 'L';
        outfile[ j++ ] = 'E';
        outfile[ j++ ] = sentinel1;
        outfile[ j++ ] = sentinel2;

        const uint32_t leInLen = sltc_rle_cpu_to_le(inlen);
        ::memcpy(&outfile[ j ], &leInLen, sizeof(uint32_t));
        j += sizeof( uint32_t );
    }
 
    while ( i < inlen )
    {
        //Avoid overflow
        if ( j >= *outlen - 6 )
        {
            if (sentinel == sentinel2)
            {
                sentinel2 = sentinel1;
                sentinel1 = sentinel;
            }
            return -1;
        }
        count = 1;
        prev_char = infile[ i++ ];
        if ( prev_char == sentinel )
        {
            outfile[ j++ ] = sentinel;
            outfile[ j++ ] = 0x00;
            if ( sentinel == sentinel1 ) {
                outfile[ j++ ] = sentinel2;
                sentinel = sentinel2;
            } else {
                outfile[ j++ ] = sentinel1;
                sentinel = sentinel1;
            }
        }

        while (i<inlen && prev_char == infile[ i ])
        {
            i++;
            count++;
            if ( i >= inlen )
            {
                break;
            }
            if ( count == 255 )
            {
                break;
            }
        }
        switch ( count )
        {
            case 1:
                outfile[ j++ ] = prev_char;
                break;
            case 2:
                outfile[ j++ ] = prev_char;
                outfile[ j++ ] = prev_char;
                break;
            default:
                outfile[ j++ ] = sentinel;
                outfile[ j++ ] = static_cast<unsigned char>(count);
                outfile[ j++ ] = prev_char;
                break;
        }
    } 

#ifdef DEBUG_SELF
    std::fstream fout;
    fout.open("/tmp/encode.txt", std::ios::out);
    for(int i=0; i<j; i++)
    {
        fout << int(outfile[i]) << std::endl;
    }
    fout.close();
    fout.open("/tmp/raw.txt", std::ios::out);
    for(int i=0; i<inlen; i++)
    {
        fout << int(infile[i]) << std::endl;
    }
    fout.close();
#endif
    *outlen = j;
    return 0;
}
 
int RLEDecode( unsigned char *infile, uint32_t inlen,
               unsigned char *outfile, uint32_t *outlen )
{
    uint32_t i,
                  j,
                  len;
    unsigned char default_sentinel,
                  change_sentinel,
                  prev_char;
    unsigned short count;

    i = 0;
    j = 0;
    if ( infile[ i++ ] == 'R' &&\
         infile[i++] == 'L' && \
         infile[i++] == 'E') {
        default_sentinel = infile[ i++ ];
        change_sentinel = infile[ i++ ];
        uint32_t leLen;
        ::memcpy(&leLen, &infile[ i ], sizeof(uint32_t));
        len = sltc_rle_le_to_cpu(leLen);
        i += sizeof ( uint32_t );
        if ( *outlen < len ) {
            *outlen = len;
            return 1;
        } else {
            *outlen = len;
        }
    } else {
        return 2;
    }

    while ( i < inlen )
    {
        prev_char = infile[ i++ ];
        count = 1;
        if ( prev_char == default_sentinel )
        {
            if ( infile[ i ] == 0x00 ) {
                if ( change_sentinel == infile[ ++i ] ) {
                    change_sentinel = default_sentinel;
                    default_sentinel = infile[ i++ ];
                    continue;
                } else {
                    // error
                }
            } else {
                count = infile[ i++ ];
                prev_char = infile[ i++ ];
            }
        }

        do
        {
            if(j>=len)
            {
                break;
            }
            outfile[ j++ ] = prev_char;
        } while ( --count );
    }
    return 0;
}
