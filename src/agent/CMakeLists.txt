cmake_minimum_required(VERSION 3.8)
project(agent)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_python REQUIRED)
find_package(rclpy REQUIRED)

# Install Python modules using ament_python_install_package
ament_python_install_package(${PROJECT_NAME})

# Install Python executables (scripts)
# Method 1: Install Python files as executable scripts
install(PROGRAMS
  agent/agent_server_node.py
  DESTINATION lib/${PROJECT_NAME}
)

# Method 2: Alternative - Install via setup.py entry points (commented out)
# The entry points are defined in setup.py and will be automatically installed
# when using: pip install -e .

# Install launch files
install(DIRECTORY
  launch/
  DESTINATION share/${PROJECT_NAME}/launch/
  FILES_MATCHING PATTERN "*.py"
)

# Install additional resources if needed
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/resource)
  install(DIRECTORY
    resource/
    DESTINATION share/${PROJECT_NAME}/resource/
  )
endif()

# Install configuration files if any  
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/config)
  install(DIRECTORY
    config/
    DESTINATION share/${PROJECT_NAME}/config/
    FILES_MATCHING PATTERN "*.yaml" PATTERN "*.yml" PATTERN "*.json"
  )
endif()

# Install scripts
if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/scripts)
    install(DIRECTORY
        scripts/
        DESTINATION lib/${PROJECT_NAME}/
    )
endif()

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # Skip some linters for Python code
  set(ament_cmake_cpplint_FOUND TRUE)
  set(ament_cmake_cppcheck_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
  
  # Add Python tests if test directory exists
  if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test)
    find_package(ament_cmake_pytest REQUIRED)
    ament_add_pytest_test(${PROJECT_NAME}_pytest test/
      APPEND_ENV PYTHONPATH=${CMAKE_CURRENT_BINARY_DIR}
      TIMEOUT 60
      WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    )
  endif()
endif()

# Important: Call ament_package() at the end
ament_package() 