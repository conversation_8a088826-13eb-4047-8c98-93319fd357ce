from setuptools import setup, find_packages
import os
from glob import glob

package_name = 'agent'

setup(
    name=package_name,
    version='0.0.1',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        # Launch files are handled by CMakeLists.txt
        # ('share/' + package_name + '/launch', glob('launch/*.py')),
    ],
    install_requires=[
        'setuptools',
        'rclpy',
        'fastapi',
        'uvicorn[standard]',
        'pydantic',
    ],
    zip_safe=True,
    maintainer='Slamtec',
    maintainer_email='<EMAIL>',
    description='Agent ROS2 node with FastAPI REST server',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            # This creates an executable script in the virtual environment
            'agent_server_node = agent.agent_server_node:main',
            'agent_server_node.py = agent.agent_server_node:main',
        ],
    },
) 