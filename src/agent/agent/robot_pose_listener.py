import math

from rclpy.node import Node
from rclpy.time import Time
from tf2_ros import <PERSON><PERSON>er, TransformListener

from .response_models import Pose


class RobotPoseListener:

    def __init__(self, node: Node):
        self._pose = Pose(x=0.0, y=0.0, z=0.0, roll=0.0, pitch=0.0, yaw=0.0)

        self._map_frame = "map"
        self._base_frame = "base_link"

        self._tf_buffer = Buffer()
        self._tf_listener = TransformListener(self._tf_buffer, node)

        self._update_timer = node.create_timer(1.0 / 60, self._update_pose)

    def _update_pose(self):
        try:
            transform = self._tf_buffer.lookup_transform(self._map_frame, self._base_frame, Time())

            x = transform.transform.translation.x
            y = transform.transform.translation.y
            z = transform.transform.translation.z

            orientation = transform.transform.rotation
            yaw = math.atan2(2.0 * (orientation.w * orientation.z + orientation.x * orientation.y),
                         1.0 - 2.0 * (orientation.y * orientation.y + orientation.z * orientation.z))

            self._pose = Pose(x=x, y=y, z=z, roll=0.0, pitch=0.0, yaw=yaw)
        except Exception as e:
            pass

    @property
    def pose(self):
        return self._pose