"""
System manager
"""
import math

from typing import Dict
from rclpy.node import Node
from interfaces.msg import RobotHealthInfo
from sensor_msgs.msg import LaserScan

from .base_manager import BaseManager
from .response_models import Capability, CapabilitiesResponse, RobotInfo, PowerStatus, HealthStatus, BaseError, LaserPoint, LaserScanResponse
from .robot_pose_listener import RobotPoseListener


class SystemManager(BaseManager):
    """System manager for handling capabilities and system operations"""
    
    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        """Initialize system manager"""
        super().__init__(node)

        self._pose_listener = pose_listener

        self._capabilities: Dict[str, Capability] = {}
        self._initialize_default_capabilities()

        self._robot_info: RobotInfo = RobotInfo(
            manufacturerId=-1,
            manufacturerName="Slamtec",
            modelId=-1,
            modelName="Test",
            deviceID="-1",
            hardwareVersion="-1",
            softwareVersion="-1",
        )

        self._health_status = None

    def _initialize_default_capabilities(self):
        """Initialize default capabilities"""
        # Add some default system capabilities
        default_capabilities = [
            Capability(
                name="navigation",
                enabled=True,
                version="1.0.0"
            ),
            Capability(
                name="mapping",
                enabled=True,
                version="1.0.0"
            )
        ]
        
        for capability in default_capabilities:
            self._capabilities[capability.name] = capability

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                RobotHealthInfo,
                "robot/health",
                self._health_callback,
                10
            )
        )
        self._subscriptions.append(
            self._node.create_subscription(
                LaserScan,
                "scan",
                self._laser_callback,
                10
            )
        )

    def _health_callback(self, msg):
        has_warning = msg.has_warning
        has_error = msg.has_error
        has_fatal = msg.has_fatal
        errors = []

        for err in msg.errors:
            errors.append(BaseError(id=0, component=err.component, errorCode=err.error_code, level=err.level, message=err.message))

        self._health_status = HealthStatus(hasWarning=has_warning, hasError=has_error, hasFatal=has_fatal, baseError=errors)

    def _laser_callback(self, msg):
        _pose = self._pose_listener.pose
        _points = []

        for index, rng in enumerate(msg.ranges):
            if math.isnan(rng) or math.isinf(rng) or rng > msg.range_max or rng < msg.range_min:
                continue

            angle = msg.angle_min + (index * msg.angle_increment)

            _points.append(LaserPoint(distance=rng, angle=angle, valid=True))

        self._laser_scan = LaserScanResponse(pose=_pose, laser_points=_points)

    @property
    def all_capabilities(self) -> CapabilitiesResponse:
        """Get all system capabilities"""
        capabilities_list = list(self._capabilities.values())
        
        return capabilities_list

    @property
    def robot_info(self) -> RobotInfo:
        return self._robot_info

    @property
    def power_status(self) -> PowerStatus:
        return PowerStatus(
            batteryPercentage=0,
            dockingStatus="on_dock",
            isCharging=True,
            isDCConnected=False,
            powerStage="running",
            sleepMode="awake"
        )

    @property
    def health_status(self) -> HealthStatus:
        if self._health_status is None:
            return HealthStatus(hasWarning=False, hasError=False, hasFatal=False, baseError=[])
        return self._health_status

    @property
    def laser_scan(self) -> LaserScanResponse | None:
        return self._laser_scan
