from abc import ABC, abstractmethod
from rclpy.node import Node
from rclpy.subscription import Subscription
from typing import List

class BaseManager(ABC):

    def __init__(self, node: Node):
        self._node = node
        self._subscriptions: List[Subscription] = []
        self._subscribe_to_topics()

    def __del__(self):
        self.cleanup()

    @abstractmethod
    def _subscribe_to_topics(self):
        pass

    async def _call_shell_script(self, script_file_name: str, script_args: str|None=None):
        import asyncio, os
        from ament_index_python.packages import get_package_prefix

        try:
            pkg_prefix = get_package_prefix("agent")
            script_path = os.path.join(pkg_prefix, "lib", "agent", script_file_name)

            args = [script_args] if script_args is not None else []

            process = await asyncio.create_subprocess_exec(
                "bash", script_path, *args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            asyncio.create_task(process.communicate())
        except Exception as e:
            print(f"Error calling shell script: {str(e)}")

    def cleanup(self):
        for subscription in self._subscriptions:
            try:
                self._node.destroy_subscription(subscription)
            except Exception as e:
                self._node.get_logger().error(f"Error destroying subscription: {str(e)}")
        self._subscriptions.clear()
