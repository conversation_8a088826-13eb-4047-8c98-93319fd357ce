#!/usr/bin/env python3
"""
Main entry point for ROS2 Agent node and FastAPI server
"""
import rclpy
from rclpy.node import Node
import uvicorn
import threading

# Handle both relative and absolute imports
try:
    from .api_server import APIServer
except ImportError:
    from agent.api_server import APIServer


class AgentServerNode(Node):
    """ROS2 Agent node with integrated FastAPI server"""
    
    def __init__(self):
        """Initialize the node"""
        super().__init__('agent_server_node')

        # Create API server
        self.api_server = APIServer(self)
        
        # Create API server thread
        self.api_thread = threading.Thread(
            target=self._run_api_server,
            daemon=True
        )
        
        # Start API server
        self.api_thread.start()
        
        self.get_logger().info('Agent Server Node started')
    
    def _run_api_server(self):
        """Run API server in separate thread"""
        uvicorn.run(
            self.api_server.get_app(),
            host="0.0.0.0",
            port=1448,
            log_level="info"
        )


def main(args=None):
    """Main function"""
    try:
        # Initialize ROS2
        rclpy.init(args=args)
        
        # Create node
        node = AgentServerNode()
        
        # Run node
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error occurred: {str(e)}")
    finally:
        # Clean up resources
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main() 