from abc import ABC
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Union

from .response_models import SimplePose
from .utils import ActionDefinition

class GeneralEnableRequest(BaseModel):
    enable: bool = Field(..., description="Enable or disable localization")


class MoveOptions(BaseModel):
    mode: Optional[int] = Field(default=None, description="Move mode")
    flags: Optional[List[str]] = Field(default=None, description="Move flags")
    yaw: Optional[float] = Field(default=None, description="Yaw of target position")
    acceptable_precision: Optional[float] = Field(default=None, description="Acceptable precision")
    fail_retry_count: Optional[int] = Field(default=None, description="Retry count if action fails")
    speed_ratio: Optional[float] = Field(default=None, description="Speed ratio")


class MoveTarget(BaseModel):
    x: float
    y: float
    z: Optional[float] = Field(default=None)


class BaseActionOptions(ABC, BaseModel):
    pass


class MoveToActionOptions(BaseActionOptions):
    target: MoveTarget = Field(description="Target point")
    move_options: Optional[MoveOptions] = Field(default=None, description="Move options")


class MoveByActionOptions(BaseActionOptions):
    direction: Optional[int] = Field(default=None, description="Direction. Forward: 0. Backward: 1. Turn left: 2. Turn right: 3")
    theta: Optional[float] = Field(default=None, description="Theta")
    duration: Optional[int] = Field(default=500, description="Action duration")


class GeneralRotateActionOptions(BaseActionOptions):
    angle: float = Field(description="Rotate by angle")


ActionOptionsUnion = Union[
    MoveByActionOptions,
    MoveToActionOptions,
    GeneralRotateActionOptions,
]


class ActionRequest(BaseModel):
    action_name: str = Field(description="Action name")
    options: dict = Field(description="Action options")

    @property
    def action_type(self) -> ActionDefinition|None:
        return ActionDefinition.parse_action_name(self.action_name)

    def get_parsed_options(self) -> ActionOptionsUnion|None:
        match self.action_type:
            case ActionDefinition.MOVE_TO:
                return MoveToActionOptions.model_validate(self.options)
            case ActionDefinition.MOVE_BY:
                return MoveByActionOptions.model_validate(self.options)
            case ActionDefinition.ROTATE:
                return GeneralRotateActionOptions.model_validate(self.options)
            case ActionDefinition.ROTATE_TO:
                return GeneralRotateActionOptions.model_validate(self.options)
            case _:
                return None


class POIRequest(BaseModel):
    id: str = Field(description="POI id")
    pose: Optional[SimplePose] = Field(description="Pose")
    metadata: Dict[str, str] = Field(description="Metadata")
