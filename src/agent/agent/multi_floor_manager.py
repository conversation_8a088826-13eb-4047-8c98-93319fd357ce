from rclpy.node import Node

from .base_manager import BaseManager
from stcm_manager.srv import SaveMap

class MultiFloorManager(BaseManager):

    def __init__(self, node: Node):
        super().__init__(node)

    def _subscribe_to_topics(self):
        pass

    async def save_map_to_disk(self) -> bool:
        client = self._node.create_client(SaveMap, "save_map")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        response = await client.call_async(SaveMap.Request())

        return response is not None and response.success