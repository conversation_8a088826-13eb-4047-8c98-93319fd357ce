import math
import struct

from nav_msgs.msg import Odometry, OccupancyGrid
from nav_msgs.srv import GetMap
from rclpy.node import Node
from stcm_manager.srv import ClearMap, GetKnownArea, GetStcmFile, UploadStcmFile
from std_msgs.msg import Int32
from threading import Lock
from typing import Optional

from .base_manager import BaseManager
from .response_models import Pose, RectangleArea
from .robot_pose_listener import RobotPoseListener
from .utils import quaternion_to_euler, SLAMMode


class SLAMManager(BaseManager):
    """Slam manager for handling SLAM related operations"""

    def __init__(self, node: Node, pose_listener: RobotPoseListener):
        super().__init__(node)

        self._laser_scan = None
        self._odometry = None
        self._localization_quality = 0
        self._slam_mode = None
        self._known_area = None

        self._pose_listener = pose_listener

        self._map_data_fetcher = MapDataFetcher(node)

        self._detect_slam_mode()

    def _subscribe_to_topics(self):
        self._subscriptions.append(
            self._node.create_subscription(
                Odometry,
                "odom",
                self._odom_callback,
                10
            )
        )
        self._subscriptions.append(
            self._node.create_subscription(
                Int32,
                "localization_quality",
                self._localization_quality_callback,
                10
            )
        )

    def _odom_callback(self, msg):
        x = msg.pose.pose.position.x
        y = msg.pose.pose.position.y
        z = msg.pose.pose.position.z

        qx, qy, qz, qw = msg.pose.pose.orientation.x, msg.pose.pose.orientation.y, msg.pose.pose.orientation.z, msg.pose.pose.orientation.w
        roll, pitch, yaw = quaternion_to_euler(qx, qy, qz, qw)

        self._odometry = Pose(x=x, y=y, z=z, roll=roll, pitch=pitch, yaw=yaw)



    def _localization_quality_callback(self, msg):
        self._localization_quality = int(msg.data)

    def _detect_slam_mode(self):
        try:
            node_names = self._node.get_node_names()
            if "nav2_container" in node_names:
                self._slam_mode = SLAMMode.LOCALIZATION
            else:
                self._slam_mode = SLAMMode.MAPPING
        except:
            pass

    @property
    def pose(self) -> Pose:
        return self._pose_listener.pose

    @pose.setter
    def pose(self, pose: Pose):
        from tf2_ros import TransformBroadcaster, TransformStamped

        broadcaster = TransformBroadcaster(self._node)

        stamped = TransformStamped()

        stamped.header.stamp = self._node.get_clock().now().to_msg()
        stamped.header.frame_id = "map"
        stamped.child_frame_id = "base_link"

        stamped.transform.translation.x = pose.x
        stamped.transform.translation.y = pose.y
        stamped.transform.translation.z = 0.0

        half_yaw = pose.yaw / 2.0
        stamped.transform.rotation.x = 0.0
        stamped.transform.rotation.y = 0.0
        stamped.transform.rotation.z = math.sin(half_yaw)
        stamped.transform.rotation.w = math.cos(half_yaw)

        broadcaster.sendTransform(stamped)

    @property
    def odometry(self) -> Pose | None:
        return self._odometry

    @property
    def localization_quality(self) -> int:
        return self._localization_quality

    @property
    def slam_mode(self) -> SLAMMode | None:
        self._detect_slam_mode()
        return self._slam_mode

    async def set_slam_mode(self, mode: SLAMMode):
        if mode == self._slam_mode:
            return
        await self._call_shell_script("switch_mapping_localization.sh", mode.value)

    def get_map(self, x_min: float, y_min: float, x_max: float, y_max: float) -> bytes:
        if not self._map_data_fetcher.has_map:
            return b''

        ros2_map_data = self._map_data_fetcher.map_data

        total_width = ros2_map_data.info.width
        total_height = ros2_map_data.info.height
        map_origin_x = ros2_map_data.info.origin.position.x
        map_origin_y = ros2_map_data.info.origin.position.y
        resolution = ros2_map_data.info.resolution
        raw_data = ros2_map_data.data

        start_x = math.floor((max(map_origin_x, x_min) - map_origin_x) / resolution)
        start_y = math.floor((max(map_origin_y, y_min) - map_origin_y) / resolution)
        requested_width = math.ceil((x_max - x_min) / resolution)
        requested_height = math.ceil((y_max - y_min) / resolution)
        width = min(total_width - start_x, requested_width - start_x)
        height = min(total_height - start_y, requested_height - start_y)

        data: bytes = b''

        row = start_y
        while row < start_y + height:
            data += raw_data[row * total_width + start_x:row * total_width + start_x + width]
            row += 1

        header_map_start_x = struct.pack('<f', max(x_min, map_origin_x))
        header_map_start_y = struct.pack('<f', max(y_min, map_origin_y))
        header_map_width = struct.pack('<I', width)
        header_map_height = struct.pack('<I', height)
        header_map_resolution = struct.pack('<f', resolution)
        header_placeholder = struct.pack('<f', 0.0) + struct.pack("<f", 0.0) + struct.pack("<f", 0.0)
        header_data_count = struct.pack('<I', len(data))

        return (header_map_start_x +
                header_map_start_y +
                header_map_width +
                header_map_height +
                header_map_resolution +
                header_placeholder +
                header_data_count +
                data)


    async def clear_map(self):
        client = self._node.create_client(ClearMap, "clear_map")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        await client.call_async(ClearMap.Request())

        await self._call_shell_script("clear_map.sh")

    async def get_known_area(self) -> RectangleArea | None:
        client = self._node.create_client(GetKnownArea, "get_known_area")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        response = await client.call_async(GetKnownArea.Request())

        if response is not None:
            data = response.known_area
            self._known_area = RectangleArea(x=data.x_min, y=data.y_min, width=data.width, height=data.height)
        else:
            self._node.get_logger().error("known area future is not ready.")

        return self._known_area

    async def get_stcm_file(self) -> bytes|None:
        client = self._node.create_client(GetStcmFile, "get_stcm_file")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        response = await client.call_async(GetStcmFile.Request())

        if response is not None and response.success:
            return bytes(response.file_data)
        else:
            return None

    async def upload_stcm_file(self, data: bytes) -> bool:
        client = self._node.create_client(UploadStcmFile, "upload_stcm_file")

        while not client.wait_for_service(timeout_sec=1.0):
            pass

        response = await client.call_async(UploadStcmFile.Request(file_data=data))

        return response is not None and response.success


class MapDataFetcher:

    def __init__(self, node):
        self._node = node
        self._map_data: Optional[OccupancyGrid] = None
        self._lock = Lock()

        self._subscription = node.create_subscription(
            OccupancyGrid,
            "map",
            self._map_callback,
            10
        )

        self._initialize_map_data()

    def _initialize_map_data(self):
        def future_callback(fut):
            try:
                response = fut.result()
                if response is not None:
                    self._set_map_data(response.map)
            except Exception as e:
                self._node.get_logger().error(f"Error initialize map: {str(e)}")

        # check if service "map_server/map" exists
        services = self._node.get_service_names_and_types()
        service_exists = any(name == "map_server/map" for name, _ in services)
        if not service_exists:
            self._node.get_logger().info("map_server/map service not found.")
            return

        client = self._node.create_client(GetMap, "map_server/map")

        while not client.wait_for_service(timeout_sec=0.1):
            print("waiting for service")
            pass

        future = client.call_async(GetMap.Request())
        future.add_done_callback(future_callback)

    def _map_callback(self, msg):
        try:
            self._set_map_data(msg)
        except Exception as e:
            self._node.get_logger().error(f"Error get map: {str(e)}")

    def _set_map_data(self, occupancy_grid: OccupancyGrid):
        if occupancy_grid is not None and occupancy_grid.data is not None:
            import numpy as np
            data = np.array(occupancy_grid.data, dtype=np.int8)
            converted_data = np.zeros_like(data, dtype=np.int8)

            free_mask = (data == 0)
            converted_data[free_mask] = 127

            other = (data != 0)
            converted_data[other] = 255 - data[other]

            occupancy_grid.data = converted_data.tolist()

            with self._lock:
                self._map_data = occupancy_grid

    @property
    def map_data(self):
        with self._lock:
            return self._map_data

    @property
    def has_map(self) -> bool:
        return self._map_data is not None

    def __del__(self):
        self._node.destroy_subscription(self._subscription)
