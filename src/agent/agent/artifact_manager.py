from rclpy.node import Node
from typing import List

from .base_manager import <PERSON>Manager
from .request_models import POIRequest
from .response_models import PointOfInterest, VirtualLine, VirtualArea
from .utils import ArtifactLineUsage, ArtifactAreaUsage


class ArtifactManager(BaseManager):

    def __init__(self, node: Node):
        super().__init__(node)

        self._pois = None

    def _subscribe_to_topics(self):
        pass

    @property
    def pois(self) -> List[PointOfInterest]|None:
        return self._pois

    def add_poi(self, data: POIRequest):
        # TODO:
        pass

    def clear_pois(self):
        # TODO:
        pass

    def get_poi_by_id(self, poi_id: str) -> PointOfInterest|None:
        return None

    def modify_poi_by_id(self, poi_id: str, poi: PointOfInterest) -> bool:
        return False

    def delete_poi_by_id(self, poi_id: str) -> bool:
        return False

    def get_virtual_lines(self, usage: ArtifactLineUsage) -> List[VirtualLine]:
        return list()

    def add_virtual_lines(self, usage: ArtifactLineUsage, lines: List[VirtualLine]):
        return False

    def modify_virtual_lines(self, usage: ArtifactLineUsage, lines: List[VirtualLine]):
        return False

    def clear_virtual_lines(self, usage: ArtifactLineUsage):
        return False

    def delete_a_virtual_line(self, usage: ArtifactLineUsage, line_id: int):
        return False

    def get_virtual_areas(self, usage: ArtifactAreaUsage) -> List[VirtualArea]:
        return list()

    def add_new_virtual_area(self, usage: ArtifactAreaUsage, data: VirtualArea):
        return False

    def clear_virtual_areas(self, usage: ArtifactAreaUsage):
        return False

    def modify_virtual_arae(self, usage: ArtifactAreaUsage, area_id: int, data: VirtualArea):
        return False

    def delete_virtual_area(self, usage: ArtifactAreaUsage, area_id: int):
        return False