#include "health_provider/health_provider.h"
#include "topics.h"

namespace rslamware {
namespace health {

HealthProvider::HealthProvider(
    rclcpp::Node::SharedPtr node,
    const std::string& component_name)
    : node_(node)
    , component_name_(component_name)
{
    if (!node_) {
        throw std::invalid_argument("Node pointer cannot be null");
    }

    // Create publisher with reliable and persistent QoS for health information
    rclcpp::QoS qos(10);
    qos.reliable();
    qos.transient_local(); // Persist health messages for late-joining subscribers

    health_publisher_ = node_->create_publisher<interfaces::msg::ComponentHealthInfo>(
        rslamware::TOPIC_COMPONENT_HEALTH, qos);

    RCLCPP_INFO(node_->get_logger(), 
               "HealthProvider initialized for component '%s'", component_name_.c_str());
}

HealthProvider::~HealthProvider()
{
    if (node_) {
        RCLCPP_DEBUG(node_->get_logger(), 
                    "HealthProvider for component '%s' destroyed", 
                    component_name_.c_str());
    }
}

void HealthProvider::reportHealth(int32_t error_code, const std::string& message)
{

    // Extract error level and component ID from error code
    int32_t level = extractErrorLevel(error_code);
    int32_t component_id = extractComponentId(error_code);

    // Create error info
    interfaces::msg::BaseErrorInfo error_info;
    error_info.error_code = error_code;
    error_info.message = message;
    error_info.level = level;
    error_info.component = component_id;
    error_info.timestamp = node_->now().nanoseconds();

    // Store in map using error_code as key
    {
        std::lock_guard<std::mutex> lock(health_mutex_);
        if(errors_.find(error_code) != errors_.end()){
            return;
        }
        errors_[error_code] = error_info;
        publishHealthLocked();
    }
    // Log based on error level
    switch (level) {
        case 1:
            RCLCPP_WARN(node_->get_logger(), "[%s] Warning %d: %s",
                       component_name_.c_str(), error_code, message.c_str());
            break;
        case 2:
            RCLCPP_ERROR(node_->get_logger(), "[%s] Error %d: %s",
                        component_name_.c_str(), error_code, message.c_str());
            break;
        case 4:
            RCLCPP_FATAL(node_->get_logger(), "[%s] Fatal %d: %s",
                        component_name_.c_str(), error_code, message.c_str());
            break;
        default:
            break;
    }
}

void HealthProvider::removeHealth(int32_t error_code)
{
    std::lock_guard<std::mutex> lock(health_mutex_);

    auto it = errors_.find(error_code);
    if (it != errors_.end()) {
        RCLCPP_DEBUG(node_->get_logger(),
                    "[%s] Removed health status: %s",
                    component_name_.c_str(), it->second.message.c_str());
        errors_.erase(it);

        // Auto-publish updated health status
        publishHealthLocked();
    }
}

void HealthProvider::clearHealth()
{
    std::lock_guard<std::mutex> lock(health_mutex_);

    errors_.clear();

    RCLCPP_INFO(node_->get_logger(),
               "[%s] Health status cleared", component_name_.c_str());

    // Auto-publish updated health status
    publishHealthLocked();
}

void HealthProvider::publishHealth()
{
    std::lock_guard<std::mutex> lock(health_mutex_);

    publishHealthLocked();
}

void HealthProvider::publishHealthLocked()
{  
    interfaces::msg::ComponentHealthInfo health_msg;
    health_msg.source = component_name_;

    // Convert map to vector
    health_msg.errors.reserve(errors_.size());
    for (const auto& pair : errors_) {
        health_msg.errors.push_back(pair.second);
    }

    health_publisher_->publish(health_msg);
}

int32_t HealthProvider::extractErrorLevel(int32_t error_code) const
{
    // Extract error level from error code
    // Error code format: LLCCCCCC (LL = level, CCCCCC = component/error specific)
    return (error_code >> 24) & 0x0F;
}

int32_t HealthProvider::extractComponentId(int32_t error_code) const
{
    // Extract component ID from error code
    // Error code format: LLCCCCCC (LL = level, CCCCCC = component/error specific)
    return (error_code >> 16) & 0x00FF;
}

} // namespace health
} // namespace rslamware
