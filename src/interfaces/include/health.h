#pragma once
#include <string>
#include <vector>

namespace rslamware{

enum class BaseErrorLevel : int32_t
{
    Healthy = 0,
    Warn = 1,
    Error = 2,
    Fatal = 4,
    Unknown = 255
};

#define SLAMWARE_HEALTH_ERROR_NONE          (0u << 24)
#define SLAMWARE_HEALTH_ERROR_WARN          (1u << 24)
#define SLAMWARE_HEALTH_ERROR_ERROR         (2u << 24)
#define SLAMWARE_HEALTH_ERROR_FATAL         (4u << 24)

#define BASE_COMPONENT_SYSTEM	(1u << 16)
#define BASE_COMPONENT_POWER	(2u << 16)
#define BASE_COMPONENT_MOTION	(3u << 16)
#define BASE_COMPONENT_SENSOR	(4u << 16)
#define BASE_COMPONENT_SOFTWARE (5u << 16)

//system errors
#define SYSTEM_ERROR_EMERGENCY_STOP      (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SYSTEM | 0x0100u)
#define SYSTEM_ERROR_BRAKE_RELEASED      (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SYSTEM | 0x0200u) 
#define SYSTEM_WARN_TEMPERATURE_HIGH	 (SLAMWARE_HEALTH_ERROR_WARN  | BASE_COMPONENT_SYSTEM | 0x0300u)
#define SYSTEM_ERROR_TEMPERATURE_HIGH 	 (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SYSTEM | 0x0300u)
#define SYSTEM_FATAL_TEMPERATURE_HIGH	 (SLAMWARE_HEALTH_ERROR_FATAL | BASE_COMPONENT_SYSTEM | 0x0300u)
#define SYSTEM_WARN_BASE_RECONNECTED	 (SLAMWARE_HEALTH_ERROR_WARN  | BASE_COMPONENT_SYSTEM | 0x0400u)
#define SYSTEM_FATAL_BASE_DISCONNECTED   (SLAMWARE_HEALTH_ERROR_FATAL | BASE_COMPONENT_SYSTEM | 0x0400u)
#define SYSTEM_WARN_DISK_USAGE_HIGH      (SLAMWARE_HEALTH_ERROR_WARN  | BASE_COMPONENT_SYSTEM | 0x0500u)
#define SYSTEM_WARN_CPU_TEMPERATURE_HIGH (SLAMWARE_HEALTH_ERROR_WARN  | BASE_COMPONENT_SYSTEM | 0x0600u)

//power errors

//motion errors

//sensor errors
#define SENSOR_ERROR_LIDAR_DISCONNECTED    (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SENSOR | 0x0100u)
#define SENSOR_ERROR_DEPTHCAM_START_FAILED (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SENSOR | 0x0200u)
#define SENSOR_ERROR_DEPTHCAM_DISCONNECTED (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SENSOR | 0x0300u)

//software errors
#define SOFTWARE_ERROR_RSLAMWARE_REBOOTED        (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SOFTWARE | 0x0100u)
#define SOFTWARE_ERROR_RELOC_FAILED              (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SOFTWARE | 0x0200u)
#define SOFTWARE_ERROR_LOW_LOCALIZAITON_QUALITY  (SLAMWARE_HEALTH_ERROR_ERROR | BASE_COMPONENT_SOFTWARE | 0x0300u)


}