#pragma once

#include <rclcpp/rclcpp.hpp>
#include <memory>
#include <string>
#include <vector>
#include <mutex>
#include <map>
#include "interfaces/msg/component_health_info.hpp"
#include "interfaces/msg/base_error_info.hpp"

namespace rslamware {
namespace health {

/**
 * @brief Health Provider class for publishing component health information
 * 
 * This class provides a simple interface for components to publish their health status.
 * It manages a ComponentHealthInfo publisher and provides methods to report errors,
 * warnings, and clear health status.
 */
class HealthProvider
{
public:
    /**
     * @brief Constructor
     * @param node ROS2 node pointer
     * @param component_name Name of the component using this health provider
     * @param topic_name Topic name for health publishing (default: "robot/component_health")
     * @param queue_size Publisher queue size (default: 10)
     */
    explicit HealthProvider(
        rclcpp::Node::SharedPtr node,
        const std::string& component_name
    );

    /**
     * @brief Destructor
     */
    ~HealthProvider();

    /**
     * @brief Report an error
     * @param error_code Error code
     * @param message Error message
     */
    void reportHealth(int32_t error_code, const std::string& message);
 

    void removeHealth(int32_t error_code);

    /**
     * @brief Clear all errors and warnings
     */
    void clearHealth();

    /**
     * @brief Publish current health status
     */
    void publishHealth();

private:
    void publishHealthLocked();
    
    int32_t extractErrorLevel(int32_t error_code) const;

    int32_t extractComponentId(int32_t error_code) const;

    // ROS2 components
    rclcpp::Node::SharedPtr node_;
    rclcpp::Publisher<interfaces::msg::ComponentHealthInfo>::SharedPtr health_publisher_;

    // Configuration
    std::string component_name_;  

    // Health data (thread-safe)
    mutable std::mutex health_mutex_;
    std::map<uint32_t, interfaces::msg::BaseErrorInfo> errors_;
};

} // namespace health
} // namespace rslamware
