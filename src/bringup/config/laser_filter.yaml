laser_filter_front:
  ros__parameters:
    filter1:
      name: angle
      type: laser_filters/LaserScanAngularBoundsFilterInPlace
      params:
        lower_angle: -0.99
        upper_angle: 0.99
        replace_with_nan: True
    filter2:
      name: median_spatial
      type: laser_filters/LaserScanMedianSpatialFilter
      params:
        window_size: 15
    filter3:
      name: shadows
      type: laser_filters/ScanShadowsFilter
      params:
        min_angle: 10.
        max_angle: 170.
        neighbors: 0
        window: 1
laser_filter_back:
  ros__parameters:
    filter1:
      name: angle
      type: laser_filters/LaserScanAngularBoundsFilterInPlace
      params:
        lower_angle: -1.05
        upper_angle: 1.05
        replace_with_nan: True
    filter2:
      name: median_spatial
      type: laser_filters/LaserScanMedianSpatialFilter
      params:
        window_size: 15
    filter3:
      name: shadows
      type: laser_filters/ScanShadowsFilter
      params:
        min_angle: 10.
        max_angle: 170.
        neighbors: 0
        window: 1
