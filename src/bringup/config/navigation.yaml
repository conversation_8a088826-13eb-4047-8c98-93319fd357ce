amcl:
  ros__parameters:
    use_sim_time: False
    alpha1: 0.2
    alpha2: 0.2
    alpha3: 0.8
    alpha4: 0.2
    alpha5: 0.1
    base_frame_id: "base_link"
    beam_skip_distance: 0.5
    beam_skip_error_threshold: 0.9
    beam_skip_threshold: 0.3
    do_beamskip: false
    global_frame_id: "map"
    lambda_short: 0.1
    laser_likelihood_max_dist: 2.0
    laser_max_range: 100.0
    laser_min_range: -1.0
    laser_model_type: "likelihood_field"
    max_beams: 60
    max_particles: 2000
    min_particles: 500
    odom_frame_id: "odom"
    pf_err: 0.05
    pf_z: 0.99
    recovery_alpha_fast: 0.0
    recovery_alpha_slow: 0.0
    resample_interval: 2
    robot_model_type: "nav2_amcl::DifferentialMotionModel"
    save_pose_rate: 0.5
    sigma_hit: 0.2
    tf_broadcast: true
    transform_tolerance: 1.0
    update_min_a: 0.2
    update_min_d: 0.2
    z_hit: 0.5
    z_max: 0.05
    z_rand: 0.5
    z_short: 0.05
    scan_topic: fusion_scan

mcl:
  ros__parameters:
    scan_name: fusion_scan
    laser_frame: base_link
    reject_unknown_scan: true
    use_gl_pose_sampler: false
    estimate_reliability: false
    use_augmented_mcl: true
    particle_num: 1000
    scan_step: 1
    z_hit: 0.95
    z_short: 0.1
    z_max: 0.01
    z_rand: 0.01
    var_hit: 0.05
    localization_hz: 20.0
    odom_noise_ddm: [0.2, 0.1, 0.1, 0.3]
    resample_thresholds: [0.1, 0.1, 0.1, 0.017, -99999.0]

gl_pose_sampler:
  ros__parameters:
    scan_name: fusion_scan
    laser_frame: base_link
    key_scans_num: 3
    gradient_square_th: 0.01 
    average_sdf_delta_th: 0.01
    random_samples_num: 20
    keypoints_min_dist_from_map: 0.5

bt_navigator:
  ros__parameters:
    use_sim_time: False
    global_frame: map
    robot_base_frame: base_link
    odom_topic: /odom
    bt_loop_duration: 10
    transform_tolerance: 0.3
    default_server_timeout: 20
    wait_for_service_timeout: 1000
    # 'default_nav_through_poses_bt_xml' and 'default_nav_to_pose_bt_xml' are use defaults:
    # nav2_bt_navigator/navigate_to_pose_w_replanning_and_recovery.xml
    # nav2_bt_navigator/navigate_through_poses_w_replanning_and_recovery.xml
    # They can be set here or via a RewrittenYaml remap from a parent launch file to Nav2.
    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_compute_path_through_poses_action_bt_node
      - nav2_smooth_path_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_assisted_teleop_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_drive_on_heading_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_truncate_path_local_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_path_expiring_timer_condition
      - nav2_distance_traveled_condition_bt_node
      - nav2_single_trigger_bt_node
      - nav2_goal_updated_controller_bt_node
      - nav2_is_battery_low_condition_bt_node
      - nav2_navigate_through_poses_action_bt_node
      - nav2_navigate_to_pose_action_bt_node
      - nav2_remove_passed_goals_action_bt_node
      - nav2_planner_selector_bt_node
      - nav2_controller_selector_bt_node
      - nav2_goal_checker_selector_bt_node
      - nav2_controller_cancel_bt_node
      - nav2_path_longer_on_approach_bt_node
      - nav2_wait_cancel_bt_node
      - nav2_spin_cancel_bt_node
      - nav2_back_up_cancel_bt_node
      - nav2_assisted_teleop_cancel_bt_node
      - nav2_drive_on_heading_cancel_bt_node
      - nav2_is_battery_charging_condition_bt_node
      - nav2_path_monitor_bt_node
      - nav2_path_evaluator_bt_node

bt_navigator_navigate_through_poses_rclcpp_node:
  ros__parameters:
    use_sim_time: False

bt_navigator_navigate_to_pose_rclcpp_node:
  ros__parameters:
    use_sim_time: False

controller_server:
  ros__parameters:
    use_sim_time: False
    controller_frequency: 30.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.0
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    transform_tolerance: 0.3
    use_realtime_priority: True
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["general_goal_checker"]
    controller_plugins: ["FollowPath"]

    # Progress checker parameters
    progress_checker:
      plugin: "nav2_controller::PoseProgressChecker"
      required_movement_radius: 0.05
      movement_time_allowance: 6.0

    general_goal_checker:
      stateful: True
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.25
      yaw_goal_tolerance: 0.25
    # DWB parameters
    FollowPath:  
      plugin: "nav2_rotation_shim_controller::RotationShimController"
      primary_controller: "dwb_core::DWBLocalPlanner"
      angular_dist_threshold: 1.0
      forward_sampling_distance: 0.5
      angular_disengage_threshold: 0.5
      rotate_to_heading_angular_vel: 0.8
      max_angular_accel: 3.2
      simulate_ahead_time: 1.0
      rotate_to_goal_heading: true

      # plugin: "teb_local_planner::TebLocalPlannerROS"
      # odom_topic: "/odom"
      # # Trajectory
      # teb_autosize: 1.0
      # dt_ref: 0.5
      # dt_hysteresis: 0.03
      # min_samples: 5
      # max_samples: 500
      # global_plan_overwrite_orientation: true
      # allow_init_with_backwards_motion: false
      # max_global_plan_lookahead_dist: 3.0
      # global_plan_viapoint_sep: -1.0
      # via_points_ordered: false
      # global_plan_prune_distance: 1.0
      # feasibility_check_no_poses: 5
      # feasibility_check_lookahead_distance: 1.0
      # exact_arc_length: false
      # publish_feedback: true
      # min_resolution_collision_check_angular: 0.017
      # control_look_ahead_poses: 3
      # # Robot
      # max_vel_x: 1.0
      # max_vel_x_backwards: 0.0
      # max_vel_theta: 1.0
      # acc_lim_x: 0.3
      # acc_lim_theta: 1.0
      # is_footprint_dynamic: false
      # footprint_model.type: "polygon"
      # footprint_model.vertices: "[[-0.4, -0.24], [-0.4, 0.24], [0.4, 0.24], [0.4, -0.24]]"
      # min_turning_radius: 0.0
      # wheelbase: 0.0
      # cmd_angle_instead_rotvel: false
      # # Goal tolerance
      # free_goal_vel: false
      # # Obstacles
      # min_obstacle_dist: 0.2
      # inflation_dist: 0.3
      # include_costmap_obstacles: true
      # costmap_obstacles_behind_robot_dist: 1.0
      # obstacle_poses_affected: 30
      # legacy_obstacle_association: false
      # obstacle_association_force_inclusion_factor: 1.5
      # obstacle_association_cutoff_factor: 5.0
      # # Optimization
      # no_inner_iterations: 8
      # no_outer_iterations: 5
      # optimization_activate: true
      # optimization_verbose: true
      # penalty_epsilon: 0.1
      # weight_max_vel_x: 2.0
      # weight_max_vel_theta: 2.0
      # weight_acc_lim_x: 1.0
      # weight_acc_lim_theta: 1.0
      # weight_kinematics_nh: 1000.0
      # weight_kinematics_forward_drive: 100.0
      # weight_optimaltime: 1.0
      # weight_shortest_path: 0.0
      # weight_obstacle: 30.0
      # weight_inflation: 0.1
      # weight_velocity_obstacle_ratio: 0.5
      # weight_adapt_factor: 2.0
      # # Recovery
      # shrink_horizon_backup: true
      # shrink_horizon_min_duration: 10.0
      # oscillation_recovery: true
      # # Homotopy Class Planning (Disabled)
      # enable_homotopy_class_planning: false
      # enable_multithreading: true
      # simple_exploration: false
      # max_number_classes: 4
      # selection_cost_hysteresis: 1.0
      # selection_prefer_initial_plan: 0.9
      # roadmap_graph_no_samples: 15
      # roadmap_graph_area_width: 5.0
      # obstacle_keypoint_offset: 0.1
      # h_signature_prescaler: 0.5
      # h_signature_threshold: 0.1
      
      
      # plugin: "dwb_core::DWBLocalPlanner"
      debug_trajectory_details: True
      min_vel_x: 0.0
      min_vel_y: 0.0
      max_vel_x: 1.0
      max_vel_y: 0.0
      max_vel_theta: 1.0
      min_speed_xy: 0.0
      max_speed_xy: 1.0
      min_speed_theta: 0.0
      acc_lim_x: 0.3
      acc_lim_y: 0.0
      acc_lim_theta: 3.0
      decel_lim_x: -1.0
      decel_lim_y: 0.0
      decel_lim_theta: -3.0
      vx_samples: 20
      vy_samples: 0
      vtheta_samples: 20
      sim_time: 1.2
      linear_granularity: 0.05
      angular_granularity: 0.025
      transform_tolerance: 0.3
      xy_goal_tolerance: 0.10
      trans_stopped_velocity: 0.01
      rot_stopped_velocity: 0.01
      use_differential_drive: true
      short_circuit_trajectory_evaluation: True
      stateful: True
      speed_reduce_enabled: True
      speed_reduce_min_threshold: 0.8
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "ObstacleFootprint", "GoalAlign", "PathAlign", "PathDist", "GoalDist", "PreferForward"]
      BaseObstacle.scale: 0.0
      ObstacleFootprint.scale: 0.5
      PathAlign.scale: 32.0
      PathAlign.forward_point_distance: 0.1
      GoalAlign.scale: 24.0
      GoalAlign.forward_point_distance: 0.1
      PathDist.scale: 40.0
      GoalDist.scale: 24.0
      RotateToGoal.scale: 32.0
      RotateToGoal.slowing_factor: 5.0
      RotateToGoal.lookahead_time: -1.0
      PreferForward.scale: 0.1
      Oscillation.scale: 1.0

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 40.0
      publish_frequency: 40.0
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: False
      rolling_window: true
      transform_tolerance: 0.3
      width: 4
      height: 4
      resolution: 0.02
      footprint: "[[-0.39, -0.245], [-0.39, 0.245], [0.39, 0.245], [0.39, -0.245]]"
      plugins: ["obstacle_layer", "voxel_layer", "keepout_filter", "denoise_layer", "inflation_layer"]
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        inflation_radius: 0.4
        cost_scaling_factor: 3.0
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        laser_points_filter_enabled: True
        observation_sources: scan depth_points
        scan:
          topic: /fusion_scan
          max_obstacle_height: 1.2
          clearing: True
          marking: True
          data_type: "LaserScan"
          observation_persistence: 0.5
          raytrace_max_range: 2.0
          obstacle_max_range: 2.0
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: False
          marking: True
          data_type: "PointCloud2"
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 1.0
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: False
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 1.3
        mark_threshold: 4
        observation_sources: depth_points
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: True
          marking: True
          data_type: "PointCloud2"
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 0.2
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      denoise_layer:
        plugin: "nav2_costmap_2d::DenoiseLayer"
        enabled: True
        minimal_group_size: 3
      always_send_full_costmap: True
      keepout_filter:
        plugin: "nav2_costmap_2d::KeepoutFilter"
        enabled: True
        filter_info_topic: "/costmap_filter_info"
        transform_tolerance: 0.3

global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 8.0
      publish_frequency: 8.0
      global_frame: map
      robot_base_frame: base_link
      use_sim_time: False
      static_map: False
      map_topic: /map
      transform_tolerance: 0.3
      footprint: "[[-0.39, -0.24], [-0.39, 0.24], [0.39, 0.24], [0.39, -0.24]]"
      resolution: 0.05
      track_unknown_space: true
      plugins: ["static_layer", "voxel_layer","obstacle_layer", "keepout_filter", "denoise_layer", "inflation_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        laser_points_filter_enabled: True
        observation_sources: scan depth_points
        scan:
          topic: /fusion_scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          observation_persistence: 0.5
          raytrace_max_range: 3.0
          obstacle_max_range: 3.0
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: False
          marking: True
          data_type: "PointCloud2"
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 1.0
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: False
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 1.3
        mark_threshold: 4
        observation_sources: depth_points
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: True
          marking: True
          data_type: "PointCloud2"
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 0.2
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.6
      denoise_layer:
        plugin: "nav2_costmap_2d::DenoiseLayer"
        enabled: True
        minimal_group_size: 3
      always_send_full_costmap: True
      keepout_filter:
        plugin: "nav2_costmap_2d::KeepoutFilter"
        enabled: True
        filter_info_topic: "/costmap_filter_info"
        transform_tolerance: 0.3

map_server:
  ros__parameters:
    use_sim_time: False
    yaml_filename: "map.yaml"

map_saver:
  ros__parameters:
    use_sim_time: False
    save_map_timeout: 5.0
    free_thresh_default: 0.25
    occupied_thresh_default: 0.65
    map_subscribe_transient_local: True

planner_server:
  ros__parameters:
    expected_planner_frequency: 5.0
    use_sim_time: False
    planner_plugins: ["GridBased"]
    GridBased:
      # plugin: "nav2_navfn_planner/NavfnPlanner"
      plugin: "nav2_smac_planner/SmacPlanner2D"
      tolerance: 0.2
      # 地图处理
      downsample_costmap: False               # 是否下采样地图（加速）
      downsampling_factor: 2                  # 下采样因子，>1 会更快但不够精细

      # 搜索细节
      allow_unknown: false                     # 是否允许穿越未知区域
      max_iterations: 100000                  # 最大搜索迭代步数
      max_on_approach_iterations: 1000        # 接近目标点时最大迭代步数

      # 目标处理
      goal_checker_name: "simple_goal_checker"  # 或 "advanced_goal_checker"
      angle_tolerance: 0.1
      xy_goal_tolerance: 0.25

      # 启发式策略（最重要的控制项）
      heuristic_type: "euclidean"             # 可选：euclidean, manhattan, diagonal
      heuristic_downweight: 1.0               # 启发式缩放因子（<1 更保守）

      # 运动模型/代价评估
      cost_travel_multiplier: 3.0             # 障碍代价值（乘以 costmap 的 cost）

      # 路径输出
      smooth_path: true        
      interpolation_resolution: 0.1           # 插值分辨率（用于平滑路径）
    # planner_plugins: ["VoronoiPlanner"]
    # VoronoiPlanner:
    #   plugin: "voronoi_planner/VoronoiPlanner"

smoother_server:
  ros__parameters:
    use_sim_time: False
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True
      cost_threshold: 100.0
      interpolation_resolution: 0.1

behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "assisted_teleop", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      #plugin: "nav2_behaviors/BackUp"
      plugin: "pb_nav2_behaviors/BackUpFreeSpace"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    assisted_teleop:
      plugin: "nav2_behaviors/AssistedTeleop"
    global_frame: odom
    robot_base_frame: base_link
    transform_tolerance: 0.3
    use_sim_time: False
    simulate_ahead_time: 2.0
    max_rotational_vel: 1.0
    min_rotational_vel: 0.4
    rotational_acc_lim: 3.2
    max_radius: 0.55
    service_name: "local_costmap/get_costmap"
    free_threshold: 5
    visualize: True

robot_state_publisher:
  ros__parameters:
    use_sim_time: False

waypoint_follower:
  ros__parameters:
    use_sim_time: False
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

velocity_smoother:
  ros__parameters:
    use_sim_time: False
    smoothing_frequency: 30.0
    scale_velocities: True
    feedback: "OPEN_LOOP"
    max_velocity: [1.0, 0.0, 1.0]
    min_velocity: [-0.2, 0.0, -1.0]
    max_accel: [0.5, 0.0, 5.0]
    max_decel: [-1.0, 0.0, -5.0]
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 0.5

