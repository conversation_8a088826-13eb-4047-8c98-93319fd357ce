<!--
  This Behavior Tree replans the global path only when the goal is updated.
-->

<root main_tree_to_execute="MainTree">
  <BehaviorTree ID="MainTree">
    <PipelineSequence name="NavigateWithReplanning">
      <GoalUpdatedController>
        <ComputePathToPose goal="{goal}" path="{path}" planner_id="GridBased"/>
      </GoalUpdatedController>
      <FollowPath path="{path}"  controller_id="FollowPath"/>
    </PipelineSequence>
  </BehaviorTree>
</root>
