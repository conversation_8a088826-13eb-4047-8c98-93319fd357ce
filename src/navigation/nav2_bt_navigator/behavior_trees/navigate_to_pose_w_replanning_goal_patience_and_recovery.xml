
<!--
  This BT has all the functionalities of navigate_to_pose_w_replanning_and_recovery.xml,
  with an additional feature to cancel the control closer to the goal proximity and 
  make the robot wait for a specific time, to see if the obstacle clears out before 
  navigating along a significantly longer path to reach the goal location.
-->
<root main_tree_to_execute="MainTree">
  <BehaviorTree ID="MainTree">
    <RecoveryNode number_of_retries="20" name="NavigateRecovery">
      <PipelineSequence name="NavigateWithReplanning">
        <RateController hz="2.0">
          <Sequence name="ComputeAndEvaluate">
            <RetryUntilSuccessful num_attempts="5" name="ComputePathToPose">
              <ComputePathToPose goal="{goal}" path="{new_path}" planner_id="GridBased"/>
            </RetryUntilSuccessful>
            <PathEvaluator new_path="{new_path}" current_path="{path}" selected_path="{path}" length_factor="1.2"/>
          </Sequence>
        </RateController>
        <RateController hz="1.0">
          <PathMonitorNode path="{path}" cost_threshold="100.0" recover_cost_threshold="10.0"/>
        </RateController>
        <ReactiveSequence name="MonitorAndFollowPath">
          <PathLongerOnApproach path="{path}" prox_len="3.0" length_factor="2.0">
            <RetryUntilSuccessful num_attempts="1">
              <SequenceStar name="CancelingControlAndWait">
                <CancelControl name="ControlCancel"/>
                <Wait wait_duration="1"/>
              </SequenceStar>
            </RetryUntilSuccessful>
          </PathLongerOnApproach>
          <RecoveryNode number_of_retries="1" name="FollowPath">
            <FollowPath path="{path}" controller_id="FollowPath"/>
            <Sequence name="FollowPathRecoveryActions">
              <BackUp backup_dist="0.2" backup_speed="0.2"/>
            </Sequence>
          </RecoveryNode>
        </ReactiveSequence>
      </PipelineSequence>
      <ReactiveFallback name="RecoveryFallback">
        <GoalUpdated/>
        <RoundRobin name="RecoveryActions">
          <Sequence name="ClearingActions">
            <ClearEntireCostmap name="ClearGlobalCostmap-Subtree" service_name="global_costmap/clear_entirely_global_costmap"/>
          </Sequence>
          <BackUp backup_dist="0.5" backup_speed="0.2"/>
        </RoundRobin>
      </ReactiveFallback>
    </RecoveryNode>
  </BehaviorTree>
</root>
