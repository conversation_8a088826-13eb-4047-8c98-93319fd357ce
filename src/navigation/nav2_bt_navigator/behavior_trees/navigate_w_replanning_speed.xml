<!--
  This Behavior Tree replans the global path periodically proprortional to speed.
-->

<root main_tree_to_execute="MainTree">
  <BehaviorTree ID="MainTree">
    <PipelineSequence name="NavigateWithReplanning">
      <SpeedController min_rate="0.1" max_rate="1.0" min_speed="0.0" max_speed="0.26">
        <ComputePathToPose goal="{goal}" path="{path}" planner_id="GridBased"/>
      </SpeedController>
      <FollowPath path="{path}"  controller_id="FollowPath"/>
    </PipelineSequence>
  </BehaviorTree>
</root>
