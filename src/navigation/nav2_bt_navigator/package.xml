<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_bt_navigator</name>
  <version>1.1.18</version>
  <description>TODO</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <depend>tf2_ros</depend>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>
  <build_depend>rclcpp</build_depend>
  <build_depend>rclcpp_action</build_depend>
  <build_depend>rclcpp_lifecycle</build_depend>
  <build_depend>nav2_behavior_tree</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>nav2_msgs</build_depend>
  <build_depend>behaviortree_cpp_v3</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>std_srvs</build_depend>
  <build_depend>nav2_util</build_depend>
  <build_depend>nav2_core</build_depend>

  <exec_depend>behaviortree_cpp_v3</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>rclcpp_action</exec_depend>
  <exec_depend>rclcpp_lifecycle</exec_depend>
  <exec_depend>nav2_behavior_tree</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>nav2_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>nav2_util</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav2_core</exec_depend>

  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
