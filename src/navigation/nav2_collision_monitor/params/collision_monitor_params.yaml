collision_monitor:
  ros__parameters:
    use_sim_time: True
    base_frame_id: "base_footprint"
    odom_frame_id: "odom"
    cmd_vel_in_topic: "cmd_vel_raw"
    cmd_vel_out_topic: "cmd_vel"
    transform_tolerance: 0.5
    source_timeout: 5.0
    base_shift_correction: True
    stop_pub_timeout: 2.0
    # Polygons represent zone around the robot for "stop" and "slowdown" action types,
    # and robot footprint for "approach" action type.
    # Footprint could be "polygon" type with dynamically set footprint from footprint_topic
    # or "circle" type with static footprint set by radius. "footprint_topic" parameter
    # to be ignored in circular case.
    polygons: ["PolygonStop"]
    PolygonStop:
      type: "polygon"
      points: [0.3, 0.3, 0.3, -0.3, 0.0, -0.3, 0.0, 0.3]
      action_type: "stop"
      max_points: 3
      visualize: True
      polygon_pub_topic: "polygon_stop"
      enabled: True
    PolygonSlow:
      type: "polygon"
      points: [0.4, 0.4, 0.4, -0.4, -0.4, -0.4, -0.4, 0.4]
      action_type: "slowdown"
      max_points: 3
      slowdown_ratio: 0.3
      visualize: True
      polygon_pub_topic: "polygon_slowdown"
      enabled: True
    FootprintApproach:
      type: "polygon"
      action_type: "approach"
      footprint_topic: "/local_costmap/published_footprint"
      time_before_collision: 2.0
      simulation_time_step: 0.1
      max_points: 5
      visualize: False
      enabled: True
    observation_sources: ["scan"]
    scan:
      type: "scan"
      topic: "/scan"
      enabled: True
    pointcloud:
      type: "pointcloud"
      topic: "/intel_realsense_r200_depth/points"
      min_height: 0.1
      max_height: 0.5
      enabled: True
