global_frame: map
robot_base_frame: base_link
update_frequency: 5.0
publish_frequency: 0.0
static_map: true
rolling_window: false

#START VOXEL STUFF
map_type: voxel
origin_z: 0.0
z_resolution: 0.2
z_voxels: 10
unknown_threshold: 10
mark_threshold: 0
#END VOXEL STUFF

transform_tolerance: 0.3
obstacle_max_range: 2.5
obstacle_min_range: 0.0
max_obstacle_height: 2.0
raytrace_max_range: 3.0
raytrace_min_range: 0.0
footprint: [[-0.325, -0.325], [-0.325, 0.325], [0.325, 0.325], [0.46, 0.0], [0.325, -0.325]]
#robot_radius: 0.46
footprint_padding: 0.01
inflation_radius: 0.55
cost_scaling_factor: 10.0
lethal_cost_threshold: 100
observation_sources: base_scan
base_scan: {data_type: LaserScan, expected_update_rate: 0.4,
  observation_persistence: 0.0, marking: true, clearing: true, max_obstacle_height: 0.4, min_obstacle_height: 0.08}
