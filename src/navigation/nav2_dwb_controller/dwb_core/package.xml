<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>dwb_core</name>
  <version>1.1.18</version>
  <description>TODO</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD-3-Clause</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>

  <build_depend>rclcpp</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_2d_msgs</build_depend>
  <build_depend>dwb_msgs</build_depend>
  <build_depend>nav2_costmap_2d</build_depend>
  <build_depend>pluginlib</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>nav_2d_utils</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>nav2_util</build_depend>
  <build_depend>nav2_core</build_depend>

  <exec_depend>rclcpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>dwb_msgs</exec_depend>
  <exec_depend>nav2_costmap_2d</exec_depend>
  <exec_depend>nav_2d_utils</exec_depend>
  <exec_depend>pluginlib</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>nav2_util</exec_depend>
  <exec_depend>nav2_core</exec_depend>

  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <nav2_core plugin="${prefix}/local_planner_plugin.xml" />
  </export>
</package>
