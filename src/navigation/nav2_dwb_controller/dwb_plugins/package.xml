<?xml version="1.0"?>
<package format="2">
  <name>dwb_plugins</name>
  <version>1.1.18</version>
  <description>
      Standard implementations of the GoalChecker
      and TrajectoryGenerators for dwb_core
  </description>
  <maintainer email="<EMAIL>">David V. Lu!!</maintainer>
  <license>BSD-3-Clause</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>

  <depend>angles</depend>
  <depend>dwb_core</depend>
  <depend>nav_2d_msgs</depend>
  <depend>nav_2d_utils</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <depend>nav2_util</depend>

  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
