<class_libraries>
  <library path="simple_goal_checker">
    <class type="dwb_plugins::SimpleGoalChecker" base_class_type="nav2_core::GoalChecker">
      <description></description>
    </class>
  </library>
  <library path="standard_traj_generator">
    <class type="dwb_plugins::StandardTrajectoryGenerator" base_class_type="dwb_core::TrajectoryGenerator">
      <description></description>
    </class>
    <class type="dwb_plugins::LimitedAccelGenerator" base_class_type="dwb_core::TrajectoryGenerator">
      <description></description>
    </class>
  </library>
  <library path="stopped_goal_checker">
    <class type="dwb_plugins::StoppedGoal<PERSON>he<PERSON>" base_class_type="nav2_core::GoalChecker">
      <description></description>
    </class>
  </library>
</class_libraries>
