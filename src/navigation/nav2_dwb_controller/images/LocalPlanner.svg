<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="341px" preserveAspectRatio="none" style="width:925px;height:341px;" version="1.1" viewBox="0 0 925 341" width="925px" zoomAndPan="magnify">
    <defs>
        <filter height="300%" id="f1b29cy80af1bi" width="300%" x="-1" y="-1">
            <feGaussianBlur result="blurOut" stdDeviation="2.0" />
            <feColorMatrix in="blurOut" result="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 .4 0" />
            <feOffset dx="4.0" dy="4.0" in="blurOut2" result="blurOut3" />
            <feBlend in="SourceGraphic" in2="blurOut3" mode="normal" />
        </filter>
    </defs>
    <g><text fill="#000000" font-family="sans-serif" font-size="18" lengthAdjust="spacingAndGlyphs" textLength="118" x="412.5" y="16.708">Local Planner</text>
        <!--cluster nav_core-->
        <polygon fill="#FFFFFF" filter="url(#f1b29cy80af1bi)" points="22,44.9531,99,44.9531,106,67.25,548,67.25,548,153.9531,22,153.9531,22,44.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="22" x2="106" y1="67.25" y2="67.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="71" x="26" y="59.9482">nav_core</text>
        <!--cluster Local Planners in Navigation-->
        <path d="M52,184.9531 C52,181.9531 54,179.9531 57,179.9531 C60,179.9531 62,181.9531 62,184.9531 C62,181.9531 64,179.9531 67,179.9531 C70,179.9531 72,181.9531 72,184.9531 C72,181.9531 74,179.9531 77,179.9531 C80,179.9531 82,181.9531 82,184.9531 C82,181.9531 84,179.9531 87,179.9531 C90,179.9531 92,181.9531 92,184.9531 C92,181.9531 94,179.9531 97,179.9531 C100,179.9531 102,181.9531 102,184.9531 C102,181.9531 104,179.9531 107,179.9531 C110,179.9531 112,181.9531 112,184.9531 C112,181.9531 114,179.9531 117,179.9531 C120,179.9531 122,181.9531 122,184.9531 C122,181.9531 124,179.9531 127,179.9531 C130,179.9531 132,181.9531 132,184.9531 C132,181.9531 134,179.9531 137,179.9531 C140,179.9531 142,181.9531 142,184.9531 C142,181.9531 144,179.9531 147,179.9531 C150,179.9531 152,181.9531 152,184.9531 C152,181.9531 154,179.9531 157,179.9531 C160,179.9531 162,181.9531 162,184.9531 C162,181.9531 164,179.9531 167,179.9531 C170,179.9531 172,181.9531 172,184.9531 C172,181.9531 174,179.9531 177,179.9531 C180,179.9531 182,181.9531 182,184.9531 C182,181.9531 184,179.9531 187,179.9531 C190,179.9531 192,181.9531 192,184.9531 C192,181.9531 194,179.9531 197,179.9531 C200,179.9531 202,181.9531 202,184.9531 C202,181.9531 204,179.9531 207,179.9531 C210,179.9531 212,181.9531 212,184.9531 C212,181.9531 214,179.9531 217,179.9531 C220,179.9531 222,181.9531 222,184.9531 C222,181.9531 224,179.9531 227,179.9531 C230,179.9531 232,181.9531 232,184.9531 C232,181.9531 234,179.9531 237,179.9531 C240,179.9531 242,181.9531 242,184.9531 C242,181.9531 244,179.9531 247,179.9531 C250,179.9531 252,181.9531 252,184.9531 C252,181.9531 254,179.9531 257,179.9531 C260,179.9531 262,181.9531 262,184.9531 C262,181.9531 264,179.9531 267,179.9531 C270,179.9531 272,181.9531 272,184.9531 C272,181.9531 274,179.9531 277,179.9531 C280,179.9531 282,181.9531 282,184.9531 C282,181.9531 284,179.9531 287,179.9531 C290,179.9531 292,181.9531 292,184.9531 C292,181.9531 294,179.9531 297,179.9531 C300,179.9531 302,181.9531 302,184.9531 C302,181.9531 304,179.9531 307,179.9531 C310,179.9531 312,181.9531 312,184.9531 C312,181.9531 314,179.9531 317,179.9531 C320,179.9531 322,181.9531 322,184.9531 C322,181.9531 324,179.9531 327,179.9531 C330,179.9531 332,181.9531 332,184.9531 C332,181.9531 334,179.9531 337,179.9531 C340,179.9531 342,181.9531 342,184.9531 C342,181.9531 344,179.9531 347,179.9531 C350,179.9531 352,181.9531 352,184.9531 C352,181.9531 354,179.9531 357,179.9531 C360,179.9531 362,181.9531 362,184.9531 C362,181.9531 364,179.9531 367,179.9531 C370,179.9531 372,181.9531 372,184.9531 C372,181.9531 374,179.9531 377,179.9531 C380,179.9531 382,181.9531 382,184.9531 C382,181.9531 384,179.9531 387,179.9531 C390,179.9531 392,181.9531 392,184.9531 C392,181.9531 394,179.9531 397,179.9531 C400,179.9531 402,181.9531 402,184.9531 C402,181.9531 404,179.9531 407,179.9531 C410,179.9531 412,181.9531 412,184.9531 C412,181.9531 414,179.9531 417,179.9531 C420,179.9531 422,181.9531 422,184.9531 C422,181.9531 424,179.9531 427,179.9531 C430,179.9531 432,181.9531 432,184.9531 C432,181.9531 434,179.9531 437,179.9531 C440,179.9531 442,181.9531 442,184.9531 C442,181.9531 444,179.9531 447,179.9531 C450,179.9531 452,181.9531 452,184.9531 C452,181.9531 454,179.9531 457,179.9531 C460,179.9531 462,181.9531 462,184.9531 C462,181.9531 464,179.9531 467,179.9531 C470,179.9531 472,181.9531 472,184.9531 C472,181.9531 474,179.9531 477,179.9531 C480,179.9531 482,181.9531 482,184.9531 C482,181.9531 484,179.9531 487,179.9531 C490,179.9531 492,181.9531 492,184.9531 C492,181.9531 494,179.9531 497,179.9531 C500,179.9531 502,181.9531 502,184.9531 C502,181.9531 504,179.9531 507,179.9531 C510,179.9531 512,181.9531 512,184.9531 C512,181.9531 514,179.9531 517,179.9531 C520,179.9531 522,181.9531 522,184.9531 C522,181.9531 524,179.9531 527,179.9531 C530,179.9531 532,181.9531 532,184.9531 C532,181.9531 534,179.9531 537,179.9531 C540,179.9531 542,181.9531 542,184.9531 C542,181.9531 544,179.9531 547,179.9531 C550,179.9531 552,181.9531 552,184.9531 C552,181.9531 554,179.9531 557,179.9531 C560,179.9531 562,181.9531 562,184.9531 C562,181.9531 564,179.9531 567,179.9531 C570,179.9531 572,181.9531 572,184.9531 C572,181.9531 574,179.9531 577,179.9531 C580,179.9531 582,181.9531 582,184.9531 C582,181.9531 584,179.9531 587,179.9531 C590,179.9531 592,181.9531 592,184.9531 C592,181.9531 594,179.9531 597,179.9531 C600,179.9531 602,181.9531 602,184.9531 C602,181.9531 604,179.9531 607,179.9531 C610,179.9531 612,181.9531 612,184.9531 C615,184.9531 617,186.9531 617,189.9531 C617,192.9531 615,194.9531 612,194.9531 C615,194.9531 617,196.9531 617,199.9531 C617,202.9531 615,204.9531 612,204.9531 C615,204.9531 617,206.9531 617,209.9531 C617,212.9531 615,214.9531 612,214.9531 C615,214.9531 617,216.9531 617,219.9531 C617,222.9531 615,224.9531 612,224.9531 C615,224.9531 617,226.9531 617,229.9531 C617,232.9531 615,234.9531 612,234.9531 C615,234.9531 617,236.9531 617,239.9531 C617,242.9531 615,244.9531 612,244.9531 C615,244.9531 617,246.9531 617,249.9531 C617,252.9531 615,254.9531 612,254.9531 C615,254.9531 617,256.9531 617,259.9531 C617,262.9531 615,264.9531 612,264.9531 C615,264.9531 617,266.9531 617,269.9531 C617,272.9531 615,274.9531 612,274.9531 C615,274.9531 617,276.9531 617,279.9531 C617,282.9531 615,284.9531 612,284.9531 C615,284.9531 617,286.9531 617,289.9531 C617,292.9531 615,294.9531 612,294.9531 C615,294.9531 617,296.9531 617,299.9531 C617,302.9531 615,304.9531 612,304.9531 C615,304.9531 617,306.9531 617,309.9531 C617,312.9531 615,314.9531 612,314.9531 C615,314.9531 617,316.9531 617,319.9531 C617,322.9531 615,324.9531 612,324.9531 C612,327.9531 609,329.9531 607,329.9531 C604,329.9531 602,327.9531 602,324.9531 C602,327.9531 599,329.9531 597,329.9531 C594,329.9531 592,327.9531 592,324.9531 C592,327.9531 589,329.9531 587,329.9531 C584,329.9531 582,327.9531 582,324.9531 C582,327.9531 579,329.9531 577,329.9531 C574,329.9531 572,327.9531 572,324.9531 C572,327.9531 569,329.9531 567,329.9531 C564,329.9531 562,327.9531 562,324.9531 C562,327.9531 559,329.9531 557,329.9531 C554,329.9531 552,327.9531 552,324.9531 C552,327.9531 549,329.9531 547,329.9531 C544,329.9531 542,327.9531 542,324.9531 C542,327.9531 539,329.9531 537,329.9531 C534,329.9531 532,327.9531 532,324.9531 C532,327.9531 529,329.9531 527,329.9531 C524,329.9531 522,327.9531 522,324.9531 C522,327.9531 519,329.9531 517,329.9531 C514,329.9531 512,327.9531 512,324.9531 C512,327.9531 509,329.9531 507,329.9531 C504,329.9531 502,327.9531 502,324.9531 C502,327.9531 499,329.9531 497,329.9531 C494,329.9531 492,327.9531 492,324.9531 C492,327.9531 489,329.9531 487,329.9531 C484,329.9531 482,327.9531 482,324.9531 C482,327.9531 479,329.9531 477,329.9531 C474,329.9531 472,327.9531 472,324.9531 C472,327.9531 469,329.9531 467,329.9531 C464,329.9531 462,327.9531 462,324.9531 C462,327.9531 459,329.9531 457,329.9531 C454,329.9531 452,327.9531 452,324.9531 C452,327.9531 449,329.9531 447,329.9531 C444,329.9531 442,327.9531 442,324.9531 C442,327.9531 439,329.9531 437,329.9531 C434,329.9531 432,327.9531 432,324.9531 C432,327.9531 429,329.9531 427,329.9531 C424,329.9531 422,327.9531 422,324.9531 C422,327.9531 419,329.9531 417,329.9531 C414,329.9531 412,327.9531 412,324.9531 C412,327.9531 409,329.9531 407,329.9531 C404,329.9531 402,327.9531 402,324.9531 C402,327.9531 399,329.9531 397,329.9531 C394,329.9531 392,327.9531 392,324.9531 C392,327.9531 389,329.9531 387,329.9531 C384,329.9531 382,327.9531 382,324.9531 C382,327.9531 379,329.9531 377,329.9531 C374,329.9531 372,327.9531 372,324.9531 C372,327.9531 369,329.9531 367,329.9531 C364,329.9531 362,327.9531 362,324.9531 C362,327.9531 359,329.9531 357,329.9531 C354,329.9531 352,327.9531 352,324.9531 C352,327.9531 349,329.9531 347,329.9531 C344,329.9531 342,327.9531 342,324.9531 C342,327.9531 339,329.9531 337,329.9531 C334,329.9531 332,327.9531 332,324.9531 C332,327.9531 329,329.9531 327,329.9531 C324,329.9531 322,327.9531 322,324.9531 C322,327.9531 319,329.9531 317,329.9531 C314,329.9531 312,327.9531 312,324.9531 C312,327.9531 309,329.9531 307,329.9531 C304,329.9531 302,327.9531 302,324.9531 C302,327.9531 299,329.9531 297,329.9531 C294,329.9531 292,327.9531 292,324.9531 C292,327.9531 289,329.9531 287,329.9531 C284,329.9531 282,327.9531 282,324.9531 C282,327.9531 279,329.9531 277,329.9531 C274,329.9531 272,327.9531 272,324.9531 C272,327.9531 269,329.9531 267,329.9531 C264,329.9531 262,327.9531 262,324.9531 C262,327.9531 259,329.9531 257,329.9531 C254,329.9531 252,327.9531 252,324.9531 C252,327.9531 249,329.9531 247,329.9531 C244,329.9531 242,327.9531 242,324.9531 C242,327.9531 239,329.9531 237,329.9531 C234,329.9531 232,327.9531 232,324.9531 C232,327.9531 229,329.9531 227,329.9531 C224,329.9531 222,327.9531 222,324.9531 C222,327.9531 219,329.9531 217,329.9531 C214,329.9531 212,327.9531 212,324.9531 C212,327.9531 209,329.9531 207,329.9531 C204,329.9531 202,327.9531 202,324.9531 C202,327.9531 199,329.9531 197,329.9531 C194,329.9531 192,327.9531 192,324.9531 C192,327.9531 189,329.9531 187,329.9531 C184,329.9531 182,327.9531 182,324.9531 C182,327.9531 179,329.9531 177,329.9531 C174,329.9531 172,327.9531 172,324.9531 C172,327.9531 169,329.9531 167,329.9531 C164,329.9531 162,327.9531 162,324.9531 C162,327.9531 159,329.9531 157,329.9531 C154,329.9531 152,327.9531 152,324.9531 C152,327.9531 149,329.9531 147,329.9531 C144,329.9531 142,327.9531 142,324.9531 C142,327.9531 139,329.9531 137,329.9531 C134,329.9531 132,327.9531 132,324.9531 C132,327.9531 129,329.9531 127,329.9531 C124,329.9531 122,327.9531 122,324.9531 C122,327.9531 119,329.9531 117,329.9531 C114,329.9531 112,327.9531 112,324.9531 C112,327.9531 109,329.9531 107,329.9531 C104,329.9531 102,327.9531 102,324.9531 C102,327.9531 99,329.9531 97,329.9531 C94,329.9531 92,327.9531 92,324.9531 C92,327.9531 89,329.9531 87,329.9531 C84,329.9531 82,327.9531 82,324.9531 C82,327.9531 79,329.9531 77,329.9531 C74,329.9531 72,327.9531 72,324.9531 C72,327.9531 69,329.9531 67,329.9531 C64,329.9531 62,327.9531 62,324.9531 C62,327.9531 59,329.9531 57,329.9531 C54,329.9531 52,327.9531 52,324.9531 C49,324.9531 47,322.9531 47,319.9531 C47,316.9531 49,314.9531 52,314.9531 C49,314.9531 47,312.9531 47,309.9531 C47,306.9531 49,304.9531 52,304.9531 C49,304.9531 47,302.9531 47,299.9531 C47,296.9531 49,294.9531 52,294.9531 C49,294.9531 47,292.9531 47,289.9531 C47,286.9531 49,284.9531 52,284.9531 C49,284.9531 47,282.9531 47,279.9531 C47,276.9531 49,274.9531 52,274.9531 C49,274.9531 47,272.9531 47,269.9531 C47,266.9531 49,264.9531 52,264.9531 C49,264.9531 47,262.9531 47,259.9531 C47,256.9531 49,254.9531 52,254.9531 C49,254.9531 47,252.9531 47,249.9531 C47,246.9531 49,244.9531 52,244.9531 C49,244.9531 47,242.9531 47,239.9531 C47,236.9531 49,234.9531 52,234.9531 C49,234.9531 47,232.9531 47,229.9531 C47,226.9531 49,224.9531 52,224.9531 C49,224.9531 47,222.9531 47,219.9531 C47,216.9531 49,214.9531 52,214.9531 C49,214.9531 47,212.9531 47,209.9531 C47,206.9531 49,204.9531 52,204.9531 C49,204.9531 47,202.9531 47,199.9531 C47,196.9531 49,194.9531 52,194.9531 C49,194.9531 47,192.9531 47,189.9531 C47,186.9531 49,184.9531 52,184.9531 " fill="#FFFFFF" filter="url(#f1b29cy80af1bi)" style="stroke: #000000; stroke-width: 1.5;" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="221" x="219.5" y="203.9482">Local Planners in Navigation</text>
        <!--cluster base_local_planner-->
        <polygon fill="#DDDDDD" filter="url(#f1b29cy80af1bi)" points="339,220.9531,493,220.9531,500,243.25,587,243.25,587,307.9531,339,307.9531,339,220.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="339" x2="500" y1="243.25" y2="243.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="148" x="343" y="235.9482">base_local_planner</text>
        <!--cluster dwa_local_planner-->
        <polygon fill="#FFFFFF" filter="url(#f1b29cy80af1bi)" points="73,220.9531,222,220.9531,229,243.25,315,243.25,315,307.9531,73,307.9531,73,220.9531" style="stroke: #000000; stroke-width: 1.5;" />
        <line style="stroke: #000000; stroke-width: 1.5;" x1="73" x2="229" y1="243.25" y2="243.25" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="143" x="77" y="235.9482">dwa_local_planner</text>
        <!--cluster Robot Navigation (DWB)-->
        <path d="M638,227.9531 C638,224.9531 640,222.9531 643,222.9531 C646,222.9531 648,224.9531 648,227.9531 C648,224.9531 650,222.9531 653,222.9531 C656,222.9531 658,224.9531 658,227.9531 C658,224.9531 660,222.9531 663,222.9531 C666,222.9531 668,224.9531 668,227.9531 C668,224.9531 670,222.9531 673,222.9531 C676,222.9531 678,224.9531 678,227.9531 C678,224.9531 680,222.9531 683,222.9531 C686,222.9531 688,224.9531 688,227.9531 C688,224.9531 690,222.9531 693,222.9531 C696,222.9531 698,224.9531 698,227.9531 C698,224.9531 700,222.9531 703,222.9531 C706,222.9531 708,224.9531 708,227.9531 C708,224.9531 710,222.9531 713,222.9531 C716,222.9531 718,224.9531 718,227.9531 C718,224.9531 720,222.9531 723,222.9531 C726,222.9531 728,224.9531 728,227.9531 C728,224.9531 730,222.9531 733,222.9531 C736,222.9531 738,224.9531 738,227.9531 C738,224.9531 740,222.9531 743,222.9531 C746,222.9531 748,224.9531 748,227.9531 C748,224.9531 750,222.9531 753,222.9531 C756,222.9531 758,224.9531 758,227.9531 C758,224.9531 760,222.9531 763,222.9531 C766,222.9531 768,224.9531 768,227.9531 C768,224.9531 770,222.9531 773,222.9531 C776,222.9531 778,224.9531 778,227.9531 C778,224.9531 780,222.9531 783,222.9531 C786,222.9531 788,224.9531 788,227.9531 C788,224.9531 790,222.9531 793,222.9531 C796,222.9531 798,224.9531 798,227.9531 C798,224.9531 800,222.9531 803,222.9531 C806,222.9531 808,224.9531 808,227.9531 C808,224.9531 810,222.9531 813,222.9531 C816,222.9531 818,224.9531 818,227.9531 C818,224.9531 820,222.9531 823,222.9531 C826,222.9531 828,224.9531 828,227.9531 C828,224.9531 830,222.9531 833,222.9531 C836,222.9531 838,224.9531 838,227.9531 C838,224.9531 840,222.9531 843,222.9531 C846,222.9531 848,224.9531 848,227.9531 C848,224.9531 850,222.9531 853,222.9531 C856,222.9531 858,224.9531 858,227.9531 C858,224.9531 860,222.9531 863,222.9531 C866,222.9531 868,224.9531 868,227.9531 C868,224.9531 870,222.9531 873,222.9531 C876,222.9531 878,224.9531 878,227.9531 C878,224.9531 880,222.9531 883,222.9531 C886,222.9531 888,224.9531 888,227.9531 C888,224.9531 890,222.9531 893,222.9531 C896,222.9531 898,224.9531 898,227.9531 C898,224.9531 900,222.9531 903,222.9531 C906,222.9531 908,224.9531 908,227.9531 C911,227.9531 913,229.9531 913,232.9531 C913,235.9531 911,237.9531 908,237.9531 C911,237.9531 913,239.9531 913,242.9531 C913,245.9531 911,247.9531 908,247.9531 C911,247.9531 913,249.9531 913,252.9531 C913,255.9531 911,257.9531 908,257.9531 C911,257.9531 913,259.9531 913,262.9531 C913,265.9531 911,267.9531 908,267.9531 C911,267.9531 913,269.9531 913,272.9531 C913,275.9531 911,277.9531 908,277.9531 C911,277.9531 913,279.9531 913,282.9531 C913,285.9531 911,287.9531 908,287.9531 C911,287.9531 913,289.9531 913,292.9531 C913,295.9531 911,297.9531 908,297.9531 C908,300.9531 905,302.9531 903,302.9531 C900,302.9531 898,300.9531 898,297.9531 C898,300.9531 895,302.9531 893,302.9531 C890,302.9531 888,300.9531 888,297.9531 C888,300.9531 885,302.9531 883,302.9531 C880,302.9531 878,300.9531 878,297.9531 C878,300.9531 875,302.9531 873,302.9531 C870,302.9531 868,300.9531 868,297.9531 C868,300.9531 865,302.9531 863,302.9531 C860,302.9531 858,300.9531 858,297.9531 C858,300.9531 855,302.9531 853,302.9531 C850,302.9531 848,300.9531 848,297.9531 C848,300.9531 845,302.9531 843,302.9531 C840,302.9531 838,300.9531 838,297.9531 C838,300.9531 835,302.9531 833,302.9531 C830,302.9531 828,300.9531 828,297.9531 C828,300.9531 825,302.9531 823,302.9531 C820,302.9531 818,300.9531 818,297.9531 C818,300.9531 815,302.9531 813,302.9531 C810,302.9531 808,300.9531 808,297.9531 C808,300.9531 805,302.9531 803,302.9531 C800,302.9531 798,300.9531 798,297.9531 C798,300.9531 795,302.9531 793,302.9531 C790,302.9531 788,300.9531 788,297.9531 C788,300.9531 785,302.9531 783,302.9531 C780,302.9531 778,300.9531 778,297.9531 C778,300.9531 775,302.9531 773,302.9531 C770,302.9531 768,300.9531 768,297.9531 C768,300.9531 765,302.9531 763,302.9531 C760,302.9531 758,300.9531 758,297.9531 C758,300.9531 755,302.9531 753,302.9531 C750,302.9531 748,300.9531 748,297.9531 C748,300.9531 745,302.9531 743,302.9531 C740,302.9531 738,300.9531 738,297.9531 C738,300.9531 735,302.9531 733,302.9531 C730,302.9531 728,300.9531 728,297.9531 C728,300.9531 725,302.9531 723,302.9531 C720,302.9531 718,300.9531 718,297.9531 C718,300.9531 715,302.9531 713,302.9531 C710,302.9531 708,300.9531 708,297.9531 C708,300.9531 705,302.9531 703,302.9531 C700,302.9531 698,300.9531 698,297.9531 C698,300.9531 695,302.9531 693,302.9531 C690,302.9531 688,300.9531 688,297.9531 C688,300.9531 685,302.9531 683,302.9531 C680,302.9531 678,300.9531 678,297.9531 C678,300.9531 675,302.9531 673,302.9531 C670,302.9531 668,300.9531 668,297.9531 C668,300.9531 665,302.9531 663,302.9531 C660,302.9531 658,300.9531 658,297.9531 C658,300.9531 655,302.9531 653,302.9531 C650,302.9531 648,300.9531 648,297.9531 C648,300.9531 645,302.9531 643,302.9531 C640,302.9531 638,300.9531 638,297.9531 C635,297.9531 633,295.9531 633,292.9531 C633,289.9531 635,287.9531 638,287.9531 C635,287.9531 633,285.9531 633,282.9531 C633,279.9531 635,277.9531 638,277.9531 C635,277.9531 633,275.9531 633,272.9531 C633,269.9531 635,267.9531 638,267.9531 C635,267.9531 633,265.9531 633,262.9531 C633,259.9531 635,257.9531 638,257.9531 C635,257.9531 633,255.9531 633,252.9531 C633,249.9531 635,247.9531 638,247.9531 C635,247.9531 633,245.9531 633,242.9531 C633,239.9531 635,237.9531 638,237.9531 C635,237.9531 633,235.9531 633,232.9531 C633,229.9531 635,227.9531 638,227.9531 " fill="#FFFFFF" filter="url(#f1b29cy80af1bi)" style="stroke: #000000; stroke-width: 1.5;" /><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacingAndGlyphs" textLength="186" x="679" y="246.9482">Robot Navigation (DWB)</text>
        <!--entity BaseGlobalPlanner-->
        <ellipse cx="286" cy="108.9531" fill="#FEFECE" filter="url(#f1b29cy80af1bi)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="130" x="221" y="138.9482">BaseGlobalPlanner</text>
        <!--entity BaseLocalPlanner-->
        <ellipse cx="463" cy="108.9531" fill="#FEFECE" filter="url(#f1b29cy80af1bi)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="122" x="402" y="138.9482">BaseLocalPlanner</text>
        <!--entity RecoveryBehavior-->
        <ellipse cx="108" cy="108.9531" fill="#FEFECE" filter="url(#f1b29cy80af1bi)" rx="8" ry="8" style="stroke: #A80036; stroke-width: 2.0;" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="124" x="46" y="138.9482">RecoveryBehavior</text>
        <!--entity base_local_planner <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1b29cy80af1bi)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="215" x="355.5" y="255.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="350.5" y="260.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="350.5" y="282.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="195" x="365.5" y="278.9482">base_local_planner «plugin»</text>
        <!--entity dwa_local_planner <<plugin>>-->
        <rect fill="#FEFECE" filter="url(#f1b29cy80af1bi)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="209" x="89.5" y="255.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="84.5" y="260.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="84.5" y="282.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="189" x="99.5" y="278.9482">dwa_local_planner «plugin»</text>
        <!--entity DWB components and adapters-->
        <rect fill="#FEFECE" filter="url(#f1b29cy80af1bi)" height="36.2969" style="stroke: #A80036; stroke-width: 1.5;" width="241" x="651.5" y="255.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="646.5" y="260.9531" />
        <rect fill="#FEFECE" height="5" style="stroke: #A80036; stroke-width: 1.5;" width="10" x="646.5" y="282.25" /><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="221" x="661.5" y="278.9482">DWB components and adapters</text>
        <!--link BaseLocalPlanner to base_local_planner <<plugin>>-->
        <path d="M463,123.6271 C463,153.2761 463,223.4191 463,255.9 " fill="none" id="BaseLocalPlanner-base_local_planner &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="463,118.2601,459,127.2601,463,123.2601,467,127.2601,463,118.2601" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link BaseLocalPlanner to dwa_local_planner <<plugin>>-->
        <path d="M449.567,119.8951 C433.066,131.6411 403.829,150.9721 376,161.9531 C360.35,168.1281 354.394,163.1651 339,169.9531 C288.991,192.0041 238.819,232.8011 212.616,255.8804 " fill="none" id="BaseLocalPlanner-dwa_local_planner &lt;&lt;plugin&gt;&gt;" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="453.872,116.7961,444.2311,118.8092,449.8145,119.7178,448.9058,125.3012,453.872,116.7961" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--link BaseLocalPlanner to DWB components and adapters-->
        <path d="M476.914,116.3671 C499.988,127.0751 547.544,149.4451 587,169.9531 C642.613,198.8591 706.209,234.9501 742.487,255.8329 " fill="none" id="BaseLocalPlanner-DWB components and adapters" style="stroke: #A80036; stroke-width: 1.0;" />
        <polygon fill="#A80036" points="472.112,114.1431,478.5975,121.5552,476.649,116.2445,481.9597,114.296,472.112,114.1431" style="stroke: #A80036; stroke-width: 1.0;" />
        <!--
@startuml LocalPlanner
title Local Planner
package "nav_core" {
  interface BaseGlobalPlanner
  interface BaseLocalPlanner
  interface RecoveryBehavior
}
cloud "Local Planners in Navigation" {
  package "base_local_planner" #DDDDDD {
    [base_local_planner <<plugin>>] -up-> BaseLocalPlanner
  }

  package "dwa_local_planner" {
    [dwa_local_planner <<plugin>>] -up-> BaseLocalPlanner
  }
}

cloud "Robot Navigation (DWB)" {
 [DWB components and adapters] -up-> BaseLocalPlanner
}

@enduml

PlantUML version 1.2017.20(Mon Dec 11 08:57:05 PST 2017)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 1.8.0_181-8u181-b13-0ubuntu0.16.04.1-b13
Operating System: Linux
OS Version: 4.15.0-36-generic
Default Encoding: UTF-8
Language: en
Country: US
-->
    </g>
</svg>