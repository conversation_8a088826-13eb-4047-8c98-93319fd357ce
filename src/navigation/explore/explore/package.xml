<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>explore_lite</name>
  <version>1.0.0</version>

  <description>Lightweight frontier-based exploration ROS2 port.</description>

  <author email="<EMAIL>"><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <depend>ament_cmake</depend>
  <depend>map_msgs</depend>
  <depend>nav2_costmap_2d</depend>
  <depend>nav2_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>rclcpp</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>visualization_msgs</depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
