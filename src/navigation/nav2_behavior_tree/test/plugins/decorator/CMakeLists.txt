ament_add_gtest(test_decorator_distance_controller test_distance_controller.cpp)
target_link_libraries(test_decorator_distance_controller nav2_distance_controller_bt_node)
ament_target_dependencies(test_decorator_distance_controller ${dependencies})

ament_add_gtest(test_decorator_speed_controller test_speed_controller.cpp)
target_link_libraries(test_decorator_speed_controller nav2_speed_controller_bt_node)
ament_target_dependencies(test_decorator_speed_controller ${dependencies})

ament_add_gtest(test_decorator_rate_controller test_rate_controller.cpp)
target_link_libraries(test_decorator_rate_controller nav2_rate_controller_bt_node)
ament_target_dependencies(test_decorator_rate_controller ${dependencies})

ament_add_gtest(test_goal_updater_node test_goal_updater_node.cpp)
target_link_libraries(test_goal_updater_node nav2_goal_updater_node_bt_node)
ament_target_dependencies(test_goal_updater_node ${dependencies})

ament_add_gtest(test_single_trigger_node test_single_trigger_node.cpp)
target_link_libraries(test_single_trigger_node nav2_single_trigger_bt_node)
ament_target_dependencies(test_single_trigger_node ${dependencies})

ament_add_gtest(test_goal_updated_controller test_goal_updated_controller.cpp)
target_link_libraries(test_goal_updated_controller nav2_goal_updated_controller_bt_node)
ament_target_dependencies(test_goal_updated_controller ${dependencies})

ament_add_gtest(test_decorator_path_longer_on_approach test_path_longer_on_approach.cpp)
target_link_libraries(test_decorator_path_longer_on_approach nav2_path_longer_on_approach_bt_node)
ament_target_dependencies(test_decorator_path_longer_on_approach ${dependencies})
