ament_add_gtest(test_condition_distance_traveled test_distance_traveled.cpp)
target_link_libraries(test_condition_distance_traveled nav2_distance_traveled_condition_bt_node)
ament_target_dependencies(test_condition_distance_traveled ${dependencies})

ament_add_gtest(test_condition_time_expired test_time_expired.cpp)
target_link_libraries(test_condition_time_expired nav2_time_expired_condition_bt_node)
ament_target_dependencies(test_condition_time_expired ${dependencies})

ament_add_gtest(test_condition_path_expiring_timer test_path_expiring_timer.cpp)
target_link_libraries(test_condition_path_expiring_timer nav2_path_expiring_timer_condition)
ament_target_dependencies(test_condition_time_expired ${dependencies})

ament_add_gtest(test_condition_goal_reached test_goal_reached.cpp)
target_link_libraries(test_condition_goal_reached nav2_goal_reached_condition_bt_node)
ament_target_dependencies(test_condition_goal_reached ${dependencies})

ament_add_gtest(test_condition_goal_updated test_goal_updated.cpp)
target_link_libraries(test_condition_goal_updated nav2_goal_updated_condition_bt_node)
ament_target_dependencies(test_condition_goal_updated ${dependencies})

ament_add_gtest(test_condition_globally_updated_goal test_globally_updated_goal.cpp)
target_link_libraries(test_condition_globally_updated_goal nav2_globally_updated_goal_condition_bt_node)
ament_target_dependencies(test_condition_globally_updated_goal ${dependencies})

ament_add_gtest(test_condition_initial_pose_received test_initial_pose_received.cpp)
target_link_libraries(test_condition_initial_pose_received nav2_initial_pose_received_condition_bt_node)
ament_target_dependencies(test_condition_initial_pose_received ${dependencies})

ament_add_gtest(test_condition_transform_available test_transform_available.cpp)
target_link_libraries(test_condition_transform_available nav2_transform_available_condition_bt_node)
ament_target_dependencies(test_condition_transform_available ${dependencies})

ament_add_gtest(test_condition_is_stuck test_is_stuck.cpp)
target_link_libraries(test_condition_is_stuck nav2_is_stuck_condition_bt_node)
ament_target_dependencies(test_condition_is_stuck ${dependencies})

ament_add_gtest(test_condition_is_battery_charging test_is_battery_charging.cpp)
target_link_libraries(test_condition_is_battery_charging nav2_is_battery_charging_condition_bt_node)
ament_target_dependencies(test_condition_is_battery_charging ${dependencies})

ament_add_gtest(test_condition_is_battery_low test_is_battery_low.cpp)
target_link_libraries(test_condition_is_battery_low nav2_is_battery_low_condition_bt_node)
ament_target_dependencies(test_condition_is_battery_low ${dependencies})

ament_add_gtest(test_condition_is_path_valid test_is_path_valid.cpp)
target_link_libraries(test_condition_is_path_valid nav2_is_path_valid_condition_bt_node)
ament_target_dependencies(test_condition_is_path_valid ${dependencies})
