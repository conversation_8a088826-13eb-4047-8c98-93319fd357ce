find_package(test_msgs REQUIRED)

ament_add_gtest(test_bt_action_node test_bt_action_node.cpp)
ament_target_dependencies(test_bt_action_node ${dependencies} test_msgs)

ament_add_gtest(test_action_spin_action test_spin_action.cpp)
target_link_libraries(test_action_spin_action nav2_spin_action_bt_node)
ament_target_dependencies(test_action_spin_action ${dependencies})

ament_add_gtest(test_action_back_up_action test_back_up_action.cpp)
target_link_libraries(test_action_back_up_action nav2_back_up_action_bt_node)
ament_target_dependencies(test_action_back_up_action ${dependencies})

ament_add_gtest(test_action_drive_on_heading test_drive_on_heading_action.cpp)
target_link_libraries(test_action_drive_on_heading nav2_drive_on_heading_bt_node)
ament_target_dependencies(test_action_drive_on_heading ${dependencies})

ament_add_gtest(test_action_wait_action test_wait_action.cpp)
target_link_libraries(test_action_wait_action nav2_wait_action_bt_node)
ament_target_dependencies(test_action_wait_action ${dependencies})

ament_add_gtest(test_action_assisted_teleop_action test_assisted_teleop_action.cpp)
target_link_libraries(test_action_assisted_teleop_action nav2_assisted_teleop_action_bt_node)
ament_target_dependencies(test_action_assisted_teleop_action ${dependencies})

ament_add_gtest(test_action_controller_cancel_action test_controller_cancel_node.cpp)
target_link_libraries(test_action_controller_cancel_action nav2_controller_cancel_bt_node)
ament_target_dependencies(test_action_controller_cancel_action ${dependencies})

ament_add_gtest(test_action_wait_cancel_action test_wait_cancel_node.cpp)
target_link_libraries(test_action_wait_cancel_action nav2_wait_cancel_bt_node)
ament_target_dependencies(test_action_wait_cancel_action ${dependencies})

ament_add_gtest(test_action_spin_cancel_action test_spin_cancel_node.cpp)
target_link_libraries(test_action_spin_cancel_action nav2_spin_cancel_bt_node)
ament_target_dependencies(test_action_spin_cancel_action ${dependencies})


ament_add_gtest(test_action_back_up_cancel_action test_back_up_cancel_node.cpp)
target_link_libraries(test_action_back_up_cancel_action nav2_back_up_cancel_bt_node)
ament_target_dependencies(test_action_back_up_cancel_action ${dependencies})

ament_add_gtest(test_action_assisted_teleop_cancel_action test_assisted_teleop_cancel_node.cpp)
target_link_libraries(test_action_assisted_teleop_cancel_action nav2_assisted_teleop_cancel_bt_node)
ament_target_dependencies(test_action_assisted_teleop_cancel_action ${dependencies})

ament_add_gtest(test_action_drive_on_heading_cancel_action test_drive_on_heading_cancel_node.cpp)
target_link_libraries(test_action_drive_on_heading_cancel_action nav2_drive_on_heading_cancel_bt_node)
ament_target_dependencies(test_action_drive_on_heading_cancel_action ${dependencies})

ament_add_gtest(test_action_clear_costmap_service test_clear_costmap_service.cpp)
target_link_libraries(test_action_clear_costmap_service nav2_clear_costmap_service_bt_node)
ament_target_dependencies(test_action_clear_costmap_service ${dependencies})

ament_add_gtest(test_action_reinitialize_global_localization_service test_reinitialize_global_localization_service.cpp)
target_link_libraries(test_action_reinitialize_global_localization_service nav2_reinitialize_global_localization_service_bt_node)
ament_target_dependencies(test_action_reinitialize_global_localization_service ${dependencies})

ament_add_gtest(test_action_compute_path_to_pose_action test_compute_path_to_pose_action.cpp)
target_link_libraries(test_action_compute_path_to_pose_action nav2_compute_path_to_pose_action_bt_node)
ament_target_dependencies(test_action_compute_path_to_pose_action ${dependencies})

ament_add_gtest(test_action_compute_path_through_poses_action test_compute_path_through_poses_action.cpp)
target_link_libraries(test_action_compute_path_through_poses_action nav2_compute_path_through_poses_action_bt_node)
ament_target_dependencies(test_action_compute_path_through_poses_action ${dependencies})

ament_add_gtest(test_action_smooth_path_action test_smooth_path_action.cpp)
target_link_libraries(test_action_smooth_path_action nav2_smooth_path_action_bt_node)
ament_target_dependencies(test_action_smooth_path_action ${dependencies})

ament_add_gtest(test_action_follow_path_action test_follow_path_action.cpp)
target_link_libraries(test_action_follow_path_action nav2_follow_path_action_bt_node)
ament_target_dependencies(test_action_follow_path_action ${dependencies})

ament_add_gtest(test_action_navigate_to_pose_action test_navigate_to_pose_action.cpp)
target_link_libraries(test_action_navigate_to_pose_action nav2_navigate_to_pose_action_bt_node)
ament_target_dependencies(test_action_navigate_to_pose_action ${dependencies})

ament_add_gtest(test_action_navigate_through_poses_action test_navigate_through_poses_action.cpp)
target_link_libraries(test_action_navigate_through_poses_action nav2_navigate_through_poses_action_bt_node)
ament_target_dependencies(test_action_navigate_through_poses_action ${dependencies})

ament_add_gtest(test_truncate_path_action test_truncate_path_action.cpp)
target_link_libraries(test_truncate_path_action nav2_truncate_path_action_bt_node)
ament_target_dependencies(test_truncate_path_action ${dependencies})

ament_add_gtest(test_truncate_path_local_action test_truncate_path_local_action.cpp)
target_link_libraries(test_truncate_path_local_action nav2_truncate_path_local_action_bt_node)
ament_target_dependencies(test_truncate_path_local_action ${dependencies})

ament_add_gtest(test_remove_passed_goals_action test_remove_passed_goals_action.cpp)
target_link_libraries(test_remove_passed_goals_action nav2_remove_passed_goals_action_bt_node)
ament_target_dependencies(test_remove_passed_goals_action ${dependencies})

ament_add_gtest(test_get_pose_from_path_action test_get_pose_from_path_action.cpp)
target_link_libraries(test_get_pose_from_path_action nav2_get_pose_from_path_action_bt_node)
ament_target_dependencies(test_get_pose_from_path_action ${dependencies})

ament_add_gtest(test_planner_selector_node test_planner_selector_node.cpp)
target_link_libraries(test_planner_selector_node nav2_planner_selector_bt_node)
ament_target_dependencies(test_planner_selector_node ${dependencies})

ament_add_gtest(test_controller_selector_node test_controller_selector_node.cpp)
target_link_libraries(test_controller_selector_node nav2_controller_selector_bt_node)
ament_target_dependencies(test_controller_selector_node ${dependencies})

ament_add_gtest(test_smoother_selector_node test_smoother_selector_node.cpp)
target_link_libraries(test_smoother_selector_node nav2_smoother_selector_bt_node)
ament_target_dependencies(test_smoother_selector_node ${dependencies})

ament_add_gtest(test_goal_checker_selector_node test_goal_checker_selector_node.cpp)
target_link_libraries(test_goal_checker_selector_node nav2_goal_checker_selector_bt_node)
ament_target_dependencies(test_goal_checker_selector_node ${dependencies})

ament_add_gtest(test_progress_checker_selector_node test_progress_checker_selector_node.cpp)
target_link_libraries(test_progress_checker_selector_node nav2_progress_checker_selector_bt_node)
ament_target_dependencies(test_progress_checker_selector_node ${dependencies})
