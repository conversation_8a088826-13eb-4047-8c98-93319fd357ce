#pragma once

#include <string>
#include <memory>
#include <mutex>

#include "rclcpp/rclcpp.hpp" 
#include "behaviortree_cpp_v3/action_node.h"
#include "nav2_msgs/msg/costmap.hpp"
#include "nav2_costmap_2d/costmap_subscriber.hpp"
#include "tf2_ros/buffer.h"
#include "tf2_ros/transform_listener.h"
#include "geometry_msgs/msg/transform_stamped.hpp"

namespace nav2_behavior_tree
{

/**
 * @brief A BT::SyncActionNode monitor path cost to determine if it's a narrow corridor
 */
class PathMonitorNode : public BT::SyncActionNode 
{
public:
  /**
   * @brief A constructor for nav2_behavior_tree::IsNarrowCorridorCondition
   * @param condition_name Name for the XML tag for this node
   * @param conf BT node configuration
   */
  PathMonitorNode(
    const std::string & condition_name,
    const BT::NodeConfiguration & conf);

  PathMonitorNode() = delete;

  /**
   * @brief The main override required by a BT action
   * @return BT::NodeStatus Status of tick execution
   */
  BT::NodeStatus tick() override;

  /**
   * @brief Creates list of BT ports
   * @return BT::PortsList Containing node-specific ports
   */
  static BT::PortsList providedPorts()
  {
    return {
      BT::InputPort<nav_msgs::msg::Path>("path"),
      BT::InputPort<double>("cost_threshold", 100.0, "Min cost threshold for narrow corridor"),
      BT::InputPort<int>("length_threshold",5, "Min path size threshold for narrow corridor"),
      BT::InputPort<double>("recover_cost_threshold", 5.0, "Cost threshold for restore original parameters"),
      BT::InputPort<double>("inflation_radius_in_narrow_corridor", 0.3, "Inflation_radius in narrow corridor"),
      BT::InputPort<double>("goal_scale_in_narrow_corridor", 80.0, "DWB goal_scale in narrow corridor"),
      BT::InputPort<double>("local_resolution_in_narrow_corridor", 0.02, "local costmap resolution in narrow corridor")
    };
  }

private:
  std::optional<rcl_interfaces::msg::ParameterValue> getParameterValue(
    rclcpp::Client<rcl_interfaces::srv::GetParameters>::SharedPtr client, const std::string & param_name);
  bool storeOriginalParameters();
  void reloadOriginalParameters();
  void setLocalCostmapParam(const std::string& param_name, double value);
  void setControllerParam(const std::string& param_name, double value); 
  bool isNarrowCorridor(std::shared_ptr<nav2_costmap_2d::Costmap2D> costmap, uint mx, uint my);
  bool getRobotPose(geometry_msgs::msg::TransformStamped & pose);
  void enterNarrowCorridor();
  void exitNarrowCorridor();
  bool isRobotInNarrowCorridor();
private:
  struct LineSegment
  {
    geometry_msgs::msg::Point start;
    geometry_msgs::msg::Point end;
    LineSegment(const geometry_msgs::msg::Point & s, const geometry_msgs::msg::Point & e)
      : start(s), end(e) {}
  };
  
  std::mutex mutex_;
  rclcpp::Node::SharedPtr node_;
  
  std::unique_ptr<nav2_costmap_2d::CostmapSubscriber> costmap_sub_;  
  rclcpp::Client<rcl_interfaces::srv::GetParameters>::SharedPtr costmap_get_client_;
  rclcpp::Client<rcl_interfaces::srv::SetParameters>::SharedPtr costmap_set_client_;
  rclcpp::Client<rcl_interfaces::srv::GetParameters>::SharedPtr controller_get_client_;
  rclcpp::Client<rcl_interfaces::srv::SetParameters>::SharedPtr controller_set_client_;
  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
  bool initialized_ = false;
  uint half_robot_width_size_ = 6;
  double original_local_inflation_radius_ = 0.4;
  double original_goal_dist_scale_ = 40.0;
  double original_path_dist_scale_ = 24.0;
  double original_sim_time_ = 1.5;
  double original_local_resolution_ = 0.05;

  double cost_threshold_ = 100.0;
  int length_threshold_ =  3;
  double recover_cost_threshold_ = 1.0;
  double inflation_radius_ = 0.3;
  double goal_scale_ = 80.0;
  double local_resolution_ = 0.02;
  std::optional<LineSegment> narrowCorridor_;
};

}  // namespace nav2_behavior_tree
 
