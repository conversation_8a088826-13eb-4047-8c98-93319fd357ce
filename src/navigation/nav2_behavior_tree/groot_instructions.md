# Instructions on using Groot
[Groot](https://github.com/BehaviorTree/Groot) is the companion application of [BehaviorTree.CPP](https://github.com/BehaviorTree/BehaviorTree.CPP) to create, edit, and monitor behavior trees.

##### Note: Currently fully supports visualization of the behavior trees. It also supports the creation of custom nodes except control flow nodes. Support for custom control flow nodes and real-time monitoring is under development.

### BehaviorTree visualization
To visualize the behavior trees using Groot:
1. Open Groot in editor mode
2. Select the `Load palette from file` option  (import button) near the top left corner.
3. Open the file `/path/to/nav2/nav2_behavior_tree/nav2_tree_nodes.xml` to import all the behavior tree nodes used for navigation.
4. Select `Load tree` option near the top left corner
5. Browse the tree you want to visualize the select ok.
