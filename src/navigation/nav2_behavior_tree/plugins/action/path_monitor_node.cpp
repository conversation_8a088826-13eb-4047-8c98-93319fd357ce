// path_cost_monitor_bt_node.cpp

#include <memory>
#include <string>
#include <vector>
 
#include "nav_msgs/msg/path.hpp"
#include "nav2_behavior_tree/plugins/action/path_monitor_node.hpp"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "nav2_costmap_2d/footprint.hpp"
#include "nav2_costmap_2d/cost_values.hpp"
#include "tf2/utils.h"
#include "angles/angles.h"
 
namespace nav2_behavior_tree
{

  PathMonitorNode::PathMonitorNode(const std::string &name, const BT::NodeConfiguration &conf)
      : BT::SyncActionNode(name, conf) 
  {
    node_ = config().blackboard->get<rclcpp::Node::SharedPtr>("node");

    costmap_sub_ = std::make_unique<nav2_costmap_2d::CostmapSubscriber>(node_,"/local_costmap/costmap_raw");  
    
    costmap_get_client_ = node_->create_client<rcl_interfaces::srv::GetParameters>("/local_costmap/local_costmap/get_parameters");
    costmap_set_client_ = node_->create_client<rcl_interfaces::srv::SetParameters>("/local_costmap/local_costmap/set_parameters");
    controller_get_client_ = node_->create_client<rcl_interfaces::srv::GetParameters>("/controller_server/get_parameters");
    controller_set_client_ = node_->create_client<rcl_interfaces::srv::SetParameters>("/controller_server/set_parameters");

    tf_buffer_ = std::make_shared<tf2_ros::Buffer>(node_->get_clock());
    tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
  }

  std::optional<rcl_interfaces::msg::ParameterValue> PathMonitorNode::getParameterValue(
    rclcpp::Client<rcl_interfaces::srv::GetParameters>::SharedPtr client, const std::string & param_name)
  {
    auto req = std::make_shared<rcl_interfaces::srv::GetParameters::Request>();
    req->names = {param_name};
 
    auto future = client->async_send_request(req);
    if (rclcpp::spin_until_future_complete(node_, future) != rclcpp::FutureReturnCode::SUCCESS) {
      RCLCPP_WARN(node_->get_logger(), "Failed to call get_parameters for %s", param_name.c_str());
      return std::nullopt;
    }

    auto res = future.get();
    if (res->values.empty()) {
      RCLCPP_WARN(node_->get_logger(), "Parameter %s not found", param_name.c_str());
      return std::nullopt;
    }
    return res->values[0];
  }

  bool PathMonitorNode::storeOriginalParameters()
  {
    auto inflation_param = getParameterValue(costmap_get_client_, "inflation_layer.inflation_radius");
    if (inflation_param.has_value()) { 
      original_local_inflation_radius_ = inflation_param->double_value;
    }else {
      RCLCPP_WARN(node_->get_logger(), "Failed to get local inflation radius");
      return false;
    }

    auto footprint = getParameterValue(costmap_get_client_, "footprint");
    auto resolution = getParameterValue(costmap_get_client_, "resolution");
    if (footprint.has_value() && resolution.has_value()) {  
      original_local_resolution_ = resolution->double_value;
      std::string footprint_str = footprint->string_value;
      std::vector<geometry_msgs::msg::Point> footprint;
      if(nav2_costmap_2d::makeFootprintFromString(footprint_str, footprint))
      {
        double min_y = std::numeric_limits<double>::max();
        double max_y = std::numeric_limits<double>::lowest();
        for (const auto &point : footprint) {
          min_y = std::min(min_y, point.y);
          max_y = std::max(max_y, point.y);
        }
        double width_in_meters = max_y - min_y;
        half_robot_width_size_ = static_cast<int>(width_in_meters * 0.5 / resolution->double_value);
      }
    }

    auto goal_dist_param = getParameterValue(controller_get_client_, "FollowPath.GoalDist.scale");
    if (goal_dist_param.has_value()) {
      original_goal_dist_scale_ = goal_dist_param->double_value;
    }else {
      RCLCPP_WARN(node_->get_logger(), "Failed to get goal dist scale parameter");
      return false;
    }

    auto path_dist_param = getParameterValue(controller_get_client_, "FollowPath.PathDist.scale");
    if (path_dist_param.has_value()) {
      original_path_dist_scale_ = path_dist_param->double_value;
    }else {
      RCLCPP_WARN(node_->get_logger(), "Failed to get path dist scale parameter");
      return false;
    }

    auto sim_time_param = getParameterValue(controller_get_client_, "FollowPath.sim_time");
    if (sim_time_param.has_value()) {
      original_sim_time_ = sim_time_param->double_value;
    }else {
      RCLCPP_WARN(node_->get_logger(), "Failed to get FollowPath.sim_time");
      return false;
    }

    RCLCPP_INFO(node_->get_logger(), "get original parameters, inflation radius:%.4f, resolution:%.4f, half robot width size:%d",
      original_local_inflation_radius_, original_local_resolution_, half_robot_width_size_);
    return true; 
  }

  void PathMonitorNode::setLocalCostmapParam(const std::string& param_name, double value)
  {
    auto req = std::make_shared<rcl_interfaces::srv::SetParameters::Request>();
    req->parameters.push_back(rclcpp::Parameter(param_name, value).to_parameter_msg());
    costmap_set_client_->async_send_request(req);
  }

  void PathMonitorNode::setControllerParam(const std::string& param_name, double value)
  {
    auto req = std::make_shared<rcl_interfaces::srv::SetParameters::Request>();
    req->parameters.push_back(rclcpp::Parameter(param_name, value).to_parameter_msg());
    controller_set_client_->async_send_request(req);
  }
  
  bool PathMonitorNode::isNarrowCorridor(std::shared_ptr<nav2_costmap_2d::Costmap2D> costmap, uint mx, uint my)
  {
    auto cost = costmap->getCost(mx, my);
    if (!narrowCorridor_ && cost < cost_threshold_) {
      return false;
    }

    auto left_x = mx - half_robot_width_size_;
    auto right_x = mx + half_robot_width_size_;
    auto down_y = my - half_robot_width_size_;
    auto up_y = my + half_robot_width_size_;
    if (mx >= half_robot_width_size_ && right_x < costmap->getSizeInCellsX()  
      && costmap->getCost(left_x, my) >= nav2_costmap_2d::MAX_NON_OBSTACLE
      && costmap->getCost(right_x, my) >= nav2_costmap_2d::MAX_NON_OBSTACLE) {
      return true;
    }
    if (my >= half_robot_width_size_ && up_y < costmap->getSizeInCellsY()
      && costmap->getCost(mx, up_y) >= nav2_costmap_2d::MAX_NON_OBSTACLE
      && costmap->getCost(mx, down_y) >= nav2_costmap_2d::MAX_NON_OBSTACLE) {
      return true;
    }
    return false;
  }

  bool PathMonitorNode::getRobotPose(geometry_msgs::msg::TransformStamped & pose)
  {
    if (!tf_buffer_->canTransform("map", "base_link", tf2::TimePointZero)) {
      RCLCPP_WARN(node_->get_logger(), "Cannot transform from map to base_link");
      return false;
    }
    pose = tf_buffer_->lookupTransform("map", "base_link", tf2::TimePointZero);
    return true;
  }

  void PathMonitorNode::enterNarrowCorridor()
  {
    setLocalCostmapParam("inflation_layer.inflation_radius", inflation_radius_);
    if(fabs(local_resolution_ - original_local_resolution_) > 1e-6)
    {
      setLocalCostmapParam("resolution", local_resolution_);
    }
    setControllerParam("FollowPath.GoalDist.scale", goal_scale_);
    setControllerParam("FollowPath.sim_time", 1.0);
  }

  void PathMonitorNode::exitNarrowCorridor()
  {
    if(narrowCorridor_)
    {
      setLocalCostmapParam("inflation_layer.inflation_radius", original_local_inflation_radius_);
      if(fabs(local_resolution_ - original_local_resolution_) > 1e-6)
      {
        setLocalCostmapParam("resolution", original_local_resolution_);
      }
      setControllerParam("FollowPath.GoalDist.scale", original_goal_dist_scale_);
      setControllerParam("FollowPath.sim_time", original_sim_time_);
      narrowCorridor_.reset();
    }
  }

  bool PathMonitorNode::isRobotInNarrowCorridor()
  {
    if(!narrowCorridor_)
    {
      return false;
    }
    geometry_msgs::msg::TransformStamped pose;
    if (!getRobotPose(pose)) {
      RCLCPP_WARN(node_->get_logger(), "Failed to get robot pose");
      return false;
    }   
    double dx = narrowCorridor_->end.x - narrowCorridor_->start.x;
    double dy = narrowCorridor_->end.y - narrowCorridor_->start.y;
  
    double rx = pose.transform.translation.x - narrowCorridor_->start.x;
    double ry = pose.transform.translation.y - narrowCorridor_->start.y;
    
    double d_norm_sq = dx*dx + dy*dy;
    if (d_norm_sq < 1e-6)
    {
      RCLCPP_WARN(node_->get_logger(), "narrow corridor length is too small");
      return false;
    }
     
    double projection = (rx * dx + ry * dy) / d_norm_sq;
    if (projection > 1.0)
    { 
      RCLCPP_INFO(node_->get_logger(), "Robot has passed through narrow corridor");
      return false; 
    }
    else if (projection < 0.0)
    {      
      double corridor_yaw = atan2(dy, dx);
      double angle_diff = angles::shortest_angular_distance(tf2::getYaw(pose.transform.rotation), corridor_yaw);
      if (fabs(angle_diff) < M_PI_2)
      {
        RCLCPP_INFO(node_->get_logger(), "Robot is moving to narrow corridor");
        return true; 
      }
      else{
        RCLCPP_INFO(node_->get_logger(), "Robot switch to another path angle diff: %.4f", angle_diff);
        return false; 
      }
    }
    RCLCPP_INFO(node_->get_logger(), "Robot is in narrow corridor");
    return true;
  }

  BT::NodeStatus PathMonitorNode::tick()
  {
    nav_msgs::msg::Path path;
    if (!getInput("path", path)) {
      RCLCPP_WARN(node_->get_logger(), "Path input not found");
      exitNarrowCorridor(); 
      return BT::NodeStatus::SUCCESS;
    }

    rclcpp::spin_some(node_);

    if (!initialized_) {
      if (!storeOriginalParameters()) {
        RCLCPP_ERROR(node_->get_logger(), "Stored original parameters"); 
        return BT::NodeStatus::SUCCESS;
      }  
      cost_threshold_ = getInput<double>("cost_threshold").value_or(100.0);
      length_threshold_ = getInput<int>("length_threshold").value_or(5); 
      recover_cost_threshold_ = getInput<double>("recover_cost_threshold").value_or(5.0);
      inflation_radius_ = getInput<double>("inflation_radius_in_narrow_corridor").value_or(0.3);
      goal_scale_ = getInput<double>("goal_scale_in_narrow_corridor").value_or(80.0); 
      local_resolution_ = getInput<double>("local_resolution_in_narrow_corridor").value_or(0.01);
      RCLCPP_INFO(node_->get_logger(), "narrow corridor threshold(cost:%.4f, path size:%d), recover cost threshold:%f", 
        cost_threshold_, length_threshold_, recover_cost_threshold_); 
      initialized_ = true;
    }

    if (narrowCorridor_ && path.poses.size() < 3) {
      RCLCPP_WARN(node_->get_logger(), "robot is close to the target point, exit narrow corridor");
      exitNarrowCorridor();
      return BT::NodeStatus::SUCCESS;
    }

    double total_cost = 0.0;
    int local_path_size = 0;
    bool is_narrow_corridor = false;
    geometry_msgs::msg::Point narrowCorridorStart;
    geometry_msgs::msg::Point narrowCorridorEnd;

    try { 
      auto costmap = costmap_sub_->getCostmap();
      if (!costmap) { 
        RCLCPP_WARN(node_->get_logger(), "No costmap available");
        return BT::NodeStatus::SUCCESS;
      }
      
      auto costmap_frame = costmap_sub_->getFrameID();
      if (costmap_frame.empty()) {
        RCLCPP_WARN(node_->get_logger(), "costmap frame id is empty");
        return BT::NodeStatus::SUCCESS;
      }
      auto transform = tf_buffer_->lookupTransform(costmap_frame, path.header.frame_id, tf2::TimePointZero);
        
      int count = 0;
      for (const auto & pathPoint : path.poses) {
        geometry_msgs::msg::PoseStamped transformed_pose;
        tf2::doTransform(pathPoint, transformed_pose, transform);
        uint mx, my;
        if(!costmap->worldToMap(transformed_pose.pose.position.x, transformed_pose.pose.position.y, mx, my)) {
          RCLCPP_DEBUG(node_->get_logger(), "Path point (%.4f, %.4f) odom(%.4f,%.4f) is out of costmap bounds", 
            pathPoint.pose.position.x, pathPoint.pose.position.y, 
            transformed_pose.pose.position.x, transformed_pose.pose.position.y);
          break; 
        }
        total_cost += costmap->getCost(mx, my);
        local_path_size++;
        
        if(isNarrowCorridor(costmap, mx, my)) {
          if (!is_narrow_corridor) {
            is_narrow_corridor = true;
            narrowCorridorStart.x = pathPoint.pose.position.x;
            narrowCorridorStart.y = pathPoint.pose.position.y;
          }
          narrowCorridorEnd.x = pathPoint.pose.position.x;
          narrowCorridorEnd.y = pathPoint.pose.position.y;
          if(narrowCorridor_)
          {
            narrowCorridor_->end.x = narrowCorridorEnd.x;
            narrowCorridor_->end.y = narrowCorridorEnd.y;
          }
          count++;
        }
        if(count >0)
        {
          RCLCPP_DEBUG(node_->get_logger(), "narrow corridor path size: %d, total cost: %.4f", count, total_cost);
        }
        if (count >= length_threshold_) { 
          if (!narrowCorridor_)
          {
            enterNarrowCorridor(); 
            narrowCorridor_ = std::make_optional<LineSegment>(narrowCorridorStart, narrowCorridorEnd);
            RCLCPP_INFO(node_->get_logger(), "Entering narrow corridor(%.4f,%.4f) to (%.4f,%.4f)",
              narrowCorridorStart.x, narrowCorridorStart.y, 
              narrowCorridorEnd.x, narrowCorridorEnd.y);
          }
          else{
            RCLCPP_INFO(node_->get_logger(), "Update narrow corridor(%.4f,%.4f) to (%.4f,%.4f)",
              narrowCorridor_->start.x, narrowCorridor_->start.y,
              narrowCorridor_->end.x, narrowCorridor_->end.y);
          }
          return BT::NodeStatus::SUCCESS;
        }
      } 

    } catch (tf2::TransformException & ex) {
      RCLCPP_WARN(node_->get_logger(), "Could not transform path: %s", ex.what()); 
      return BT::NodeStatus::SUCCESS;
    }
    catch (std::exception & ex) {
      RCLCPP_WARN(node_->get_logger(), "exception: %s", ex.what()); 
      return BT::NodeStatus::SUCCESS;
    }

    if (narrowCorridor_ && !isRobotInNarrowCorridor())
    {
      auto average_cost = total_cost / local_path_size;
      if ((average_cost < recover_cost_threshold_)) {
        exitNarrowCorridor();
        RCLCPP_INFO(node_->get_logger(), "Exiting narrow corridor, average path cost: %.4f", average_cost); 
      }
    }
    return BT::NodeStatus::SUCCESS;
  }
} 

#include "behaviortree_cpp_v3/bt_factory.h"
BT_REGISTER_NODES(factory)
{
  factory.registerNodeType<nav2_behavior_tree::PathMonitorNode>("PathMonitorNode");
}
