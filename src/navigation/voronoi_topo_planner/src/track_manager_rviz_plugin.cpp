#include "voronoi_topo_planner/track_manager_rviz_plugin.hpp"
#include <pluginlib/class_list_macros.hpp>
#include <QApplication>
#include <QTimer>
#include <cmath>
namespace voronoi_topo_planner
{

TrackManagerRvizPlugin::TrackManagerRvizPlugin(QWidget* parent)
    : rviz_common::Panel(parent),
      selected_track_index_(-1),
      is_editing_(false),
      marker_id_counter_(0)
{
    // 创建ROS2节点
    node_ = std::make_shared<rclcpp::Node>("track_manager_rviz_plugin");
    
    // 创建服务客户端
    add_track_client_ = node_->create_client<voronoi_topo_planner::srv::AddTrack>("add_track");
    delete_track_client_ = node_->create_client<voronoi_topo_planner::srv::DeleteTrack>("delete_track");
    move_track_client_ = node_->create_client<voronoi_topo_planner::srv::MoveTrack>("move_track");
    get_tracks_client_ = node_->create_client<voronoi_topo_planner::srv::GetTracks>("get_tracks");
    
    // 创建发布器
    track_markers_pub_ = node_->create_publisher<visualization_msgs::msg::MarkerArray>("track_markers", 10);
    
    // 创建UI
    QVBoxLayout* main_layout = new QVBoxLayout(this);
    
    // 添加轨道输入区域
    main_layout->addWidget(createTrackInputWidget());
    
    // 添加轨道列表区域
    main_layout->addWidget(createTrackListWidget());
    
    // 添加轨道信息区域
    main_layout->addWidget(createTrackInfoWidget());
    
    // 设置定时器定期更新轨道列表
    QTimer* timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &TrackManagerRvizPlugin::onRefreshTracksClicked);
    timer->start(2000); // 每2秒更新一次
    
    // 初始化时刷新一次
    QTimer::singleShot(1000, this, &TrackManagerRvizPlugin::onRefreshTracksClicked);
}

TrackManagerRvizPlugin::~TrackManagerRvizPlugin()
{
    clearTrackMarkers();
}

void TrackManagerRvizPlugin::onInitialize()
{
    // 初始化完成后的操作
    onRefreshTracksClicked();
}

QWidget* TrackManagerRvizPlugin::createTrackInputWidget()
{
    QGroupBox* group_box = new QGroupBox("添加/编辑轨道");
    QVBoxLayout* layout = new QVBoxLayout(group_box);
    
    // 轨道ID输入
    QHBoxLayout* id_layout = new QHBoxLayout();
    id_layout->addWidget(new QLabel("轨道ID:"));
    track_id_edit_ = new QLineEdit();
    track_id_edit_->setPlaceholderText("输入轨道ID");
    id_layout->addWidget(track_id_edit_);
    layout->addLayout(id_layout);
    
    // 起点坐标输入
    QHBoxLayout* start_layout = new QHBoxLayout();
    start_layout->addWidget(new QLabel("起点:"));
    start_x_spin_ = new QDoubleSpinBox();
    start_x_spin_->setRange(-1000.0, 1000.0);
    start_x_spin_->setDecimals(2);
    start_x_spin_->setSuffix(" m");
    start_layout->addWidget(new QLabel("X:"));
    start_layout->addWidget(start_x_spin_);
    
    start_y_spin_ = new QDoubleSpinBox();
    start_y_spin_->setRange(-1000.0, 1000.0);
    start_y_spin_->setDecimals(2);
    start_y_spin_->setSuffix(" m");
    start_layout->addWidget(new QLabel("Y:"));
    start_layout->addWidget(start_y_spin_);
    layout->addLayout(start_layout);
    
    // 终点坐标输入
    QHBoxLayout* end_layout = new QHBoxLayout();
    end_layout->addWidget(new QLabel("终点:"));
    end_x_spin_ = new QDoubleSpinBox();
    end_x_spin_->setRange(-1000.0, 1000.0);
    end_x_spin_->setDecimals(2);
    end_x_spin_->setSuffix(" m");
    end_layout->addWidget(new QLabel("X:"));
    end_layout->addWidget(end_x_spin_);
    
    end_y_spin_ = new QDoubleSpinBox();
    end_y_spin_->setRange(-1000.0, 1000.0);
    end_y_spin_->setDecimals(2);
    end_y_spin_->setSuffix(" m");
    end_layout->addWidget(new QLabel("Y:"));
    end_layout->addWidget(end_y_spin_);
    layout->addLayout(end_layout);
    
    // 方向选择
    QHBoxLayout* direction_layout = new QHBoxLayout();
    direction_layout->addWidget(new QLabel("方向:"));
    direction_combo_ = new QComboBox();
    direction_combo_->addItem("双向", 0);
    direction_combo_->addItem("起点→终点", 1);
    direction_combo_->addItem("终点→起点", -1);
    direction_layout->addWidget(direction_combo_);
    layout->addLayout(direction_layout);
    
    // 按钮区域
    QHBoxLayout* button_layout = new QHBoxLayout();
    add_track_button_ = new QPushButton("添加轨道");
    edit_track_button_ = new QPushButton("编辑轨道");
    edit_track_button_->setEnabled(false);
    
    connect(add_track_button_, &QPushButton::clicked, this, &TrackManagerRvizPlugin::onAddTrackClicked);
    connect(edit_track_button_, &QPushButton::clicked, this, &TrackManagerRvizPlugin::onEditTrackClicked);
    
    button_layout->addWidget(add_track_button_);
    button_layout->addWidget(edit_track_button_);
    layout->addLayout(button_layout);
    
    return group_box;
}

QWidget* TrackManagerRvizPlugin::createTrackListWidget()
{
    QGroupBox* group_box = new QGroupBox("轨道列表");
    QVBoxLayout* layout = new QVBoxLayout(group_box);
    
    // 工具栏
    QHBoxLayout* toolbar_layout = new QHBoxLayout();
    refresh_button_ = new QPushButton("刷新");
    delete_track_button_ = new QPushButton("删除选中");
    delete_track_button_->setEnabled(false);
    
    connect(refresh_button_, &QPushButton::clicked, this, &TrackManagerRvizPlugin::onRefreshTracksClicked);
    connect(delete_track_button_, &QPushButton::clicked, this, &TrackManagerRvizPlugin::onDeleteTrackClicked);
    
    toolbar_layout->addWidget(refresh_button_);
    toolbar_layout->addWidget(delete_track_button_);
    toolbar_layout->addStretch();
    
    track_count_label_ = new QLabel("轨道数量: 0");
    toolbar_layout->addWidget(track_count_label_);
    layout->addLayout(toolbar_layout);
    
    // 轨道表格
    track_table_ = new QTableWidget();
    track_table_->setColumnCount(5);
    track_table_->setHorizontalHeaderLabels({"轨道ID", "起点X", "起点Y", "终点X", "终点Y"});
    track_table_->setSelectionBehavior(QAbstractItemView::SelectRows);
    track_table_->setSelectionMode(QAbstractItemView::SingleSelection);
    
    connect(track_table_, &QTableWidget::itemSelectionChanged, 
            this, &TrackManagerRvizPlugin::onTrackTableSelectionChanged);
    
    layout->addWidget(track_table_);
    
    return group_box;
}

QWidget* TrackManagerRvizPlugin::createTrackInfoWidget()
{
    QGroupBox* group_box = new QGroupBox("轨道信息");
    QVBoxLayout* layout = new QVBoxLayout(group_box);
    
    selected_track_info_ = new QLabel("未选择轨道");
    selected_track_info_->setWordWrap(true);
    layout->addWidget(selected_track_info_);
    
    return group_box;
}

void TrackManagerRvizPlugin::onAddTrackClicked()
{
    QString track_id = track_id_edit_->text().trimmed();
    if (track_id.isEmpty()) {
        showMessage("错误", "请输入轨道ID", true);
        return;
    }
    
    geometry_msgs::msg::Point start_point = createPoint(start_x_spin_->value(), start_y_spin_->value());
    geometry_msgs::msg::Point end_point = createPoint(end_x_spin_->value(), end_y_spin_->value());
    int8_t direction = static_cast<int8_t>(direction_combo_->currentData().toInt());
    
    if (callAddTrackService(track_id.toStdString(), start_point, end_point, direction)) {
        showMessage("成功", "轨道添加成功");
        clearTrackInputs();
        onRefreshTracksClicked();
    } else {
        showMessage("错误", "轨道添加失败", true);
    }
}

void TrackManagerRvizPlugin::onDeleteTrackClicked()
{
    if (selected_track_index_ < 0 || selected_track_index_ >= static_cast<int>(current_tracks_.size())) {
        showMessage("错误", "请先选择要删除的轨道", true);
        return;
    }
    
    QString track_id = QString::fromStdString(current_tracks_[selected_track_index_].track_id);
    QMessageBox::StandardButton reply = QMessageBox::question(this, "确认删除", 
        QString("确定要删除轨道 '%1' 吗？").arg(track_id),
        QMessageBox::Yes | QMessageBox::No);
    
    if (reply == QMessageBox::Yes) {
        if (callDeleteTrackService(current_tracks_[selected_track_index_].track_id)) {
            showMessage("成功", "轨道删除成功");
            onRefreshTracksClicked();
        } else {
            showMessage("错误", "轨道删除失败", true);
        }
    }
}

void TrackManagerRvizPlugin::onEditTrackClicked()
{
    if (selected_track_index_ < 0 || selected_track_index_ >= static_cast<int>(current_tracks_.size())) {
        showMessage("错误", "请先选择要编辑的轨道", true);
        return;
    }
    
    if (!is_editing_) {
        // 开始编辑模式
        const auto& track = current_tracks_[selected_track_index_];
        track_id_edit_->setText(QString::fromStdString(track.track_id));
        start_x_spin_->setValue(track.start_point.x);
        start_y_spin_->setValue(track.start_point.y);
        end_x_spin_->setValue(track.end_point.x);
        end_y_spin_->setValue(track.end_point.y);
        
        // 设置方向
        for (int i = 0; i < direction_combo_->count(); ++i) {
            if (direction_combo_->itemData(i).toInt() == track.direction) {
                direction_combo_->setCurrentIndex(i);
                break;
            }
        }
        
        edit_track_button_->setText("保存编辑");
        add_track_button_->setEnabled(false);
        is_editing_ = true;
    } else {
        // 保存编辑
        QString track_id = track_id_edit_->text().trimmed();
        if (track_id.isEmpty()) {
            showMessage("错误", "请输入轨道ID", true);
            return;
        }
        
        geometry_msgs::msg::Point new_start_point = createPoint(start_x_spin_->value(), start_y_spin_->value());
        geometry_msgs::msg::Point new_end_point = createPoint(end_x_spin_->value(), end_y_spin_->value());
        
        if (callMoveTrackService(current_tracks_[selected_track_index_].track_id, new_start_point, new_end_point)) {
            showMessage("成功", "轨道编辑成功");
            clearTrackInputs();
            edit_track_button_->setText("编辑轨道");
            add_track_button_->setEnabled(true);
            is_editing_ = false;
            onRefreshTracksClicked();
        } else {
            showMessage("错误", "轨道编辑失败", true);
        }
    }
}

void TrackManagerRvizPlugin::onRefreshTracksClicked()
{
    std::vector<voronoi_topo_planner::msg::Track> tracks;
    if (callGetTracksService(tracks)) {
        current_tracks_ = tracks;
        updateTrackTable();
        publishTrackMarkers();
    }
}

void TrackManagerRvizPlugin::onTrackTableSelectionChanged()
{
    QList<QTableWidgetItem*> selected_items = track_table_->selectedItems();
    if (selected_items.isEmpty()) {
        selected_track_index_ = -1;
        delete_track_button_->setEnabled(false);
        edit_track_button_->setEnabled(false);
        updateTrackInfo();
        return;
    }

    selected_track_index_ = selected_items.first()->row();
    delete_track_button_->setEnabled(true);
    edit_track_button_->setEnabled(true);
    updateTrackInfo();
}

void TrackManagerRvizPlugin::onStartPointChanged()
{
    // 起点坐标变化时的处理逻辑
    // 可以在这里添加实时预览或验证逻辑
}

void TrackManagerRvizPlugin::onEndPointChanged()
{
    // 终点坐标变化时的处理逻辑
    // 可以在这里添加实时预览或验证逻辑
}

bool TrackManagerRvizPlugin::callAddTrackService(const std::string& track_id, 
                                                const geometry_msgs::msg::Point& start_point,
                                                const geometry_msgs::msg::Point& end_point,
                                                int8_t direction)
{
    if (!add_track_client_->wait_for_service(std::chrono::seconds(1))) {
        return false;
    }
    
    auto request = std::make_shared<voronoi_topo_planner::srv::AddTrack::Request>();
    request->track_id = track_id;
    request->start_point = start_point;
    request->end_point = end_point;
    request->direction = direction;
    
    auto future = add_track_client_->async_send_request(request);
    
    if (rclcpp::spin_until_future_complete(node_, future, std::chrono::seconds(5)) == rclcpp::FutureReturnCode::SUCCESS) {
        return future.get()->success;
    }
    
    return false;
}

bool TrackManagerRvizPlugin::callDeleteTrackService(const std::string& track_id)
{
    if (!delete_track_client_->wait_for_service(std::chrono::seconds(1))) {
        return false;
    }
    
    auto request = std::make_shared<voronoi_topo_planner::srv::DeleteTrack::Request>();
    request->track_id = track_id;
    
    auto future = delete_track_client_->async_send_request(request);
    
    if (rclcpp::spin_until_future_complete(node_, future, std::chrono::seconds(5)) == rclcpp::FutureReturnCode::SUCCESS) {
        return future.get()->success;
    }
    
    return false;
}

bool TrackManagerRvizPlugin::callMoveTrackService(const std::string& track_id,
                                                 const geometry_msgs::msg::Point& new_start_point,
                                                 const geometry_msgs::msg::Point& new_end_point)
{
    if (!move_track_client_->wait_for_service(std::chrono::seconds(1))) {
        return false;
    }
    
    auto request = std::make_shared<voronoi_topo_planner::srv::MoveTrack::Request>();
    request->track_id = track_id;
    request->new_start_point = new_start_point;
    request->new_end_point = new_end_point;
    
    auto future = move_track_client_->async_send_request(request);
    
    if (rclcpp::spin_until_future_complete(node_, future, std::chrono::seconds(5)) == rclcpp::FutureReturnCode::SUCCESS) {
        return future.get()->success;
    }
    
    return false;
}

bool TrackManagerRvizPlugin::callGetTracksService(std::vector<voronoi_topo_planner::msg::Track>& tracks)
{
    if (!get_tracks_client_->wait_for_service(std::chrono::seconds(1))) {
        return false;
    }
    
    auto request = std::make_shared<voronoi_topo_planner::srv::GetTracks::Request>();
    auto future = get_tracks_client_->async_send_request(request);
    
    if (rclcpp::spin_until_future_complete(node_, future, std::chrono::seconds(5)) == rclcpp::FutureReturnCode::SUCCESS) {
        tracks = future.get()->tracks;
        return true;
    }
    
    return false;
}

void TrackManagerRvizPlugin::updateTrackTable()
{
    track_table_->setRowCount(current_tracks_.size());
    track_count_label_->setText(QString("轨道数量: %1").arg(current_tracks_.size()));
    
    for (size_t i = 0; i < current_tracks_.size(); ++i) {
        const auto& track = current_tracks_[i];
        
        track_table_->setItem(i, 0, new QTableWidgetItem(QString::fromStdString(track.track_id)));
        track_table_->setItem(i, 1, new QTableWidgetItem(QString::number(track.start_point.x, 'f', 2)));
        track_table_->setItem(i, 2, new QTableWidgetItem(QString::number(track.start_point.y, 'f', 2)));
        track_table_->setItem(i, 3, new QTableWidgetItem(QString::number(track.end_point.x, 'f', 2)));
        track_table_->setItem(i, 4, new QTableWidgetItem(QString::number(track.end_point.y, 'f', 2)));
    }
    
    track_table_->resizeColumnsToContents();
}

void TrackManagerRvizPlugin::updateTrackInfo()
{
    if (selected_track_index_ < 0 || selected_track_index_ >= static_cast<int>(current_tracks_.size())) {
        selected_track_info_->setText("未选择轨道");
        return;
    }
    
    const auto& track = current_tracks_[selected_track_index_];
    QString direction_str;
    switch (track.direction) {
        case 0: direction_str = "双向"; break;
        case 1: direction_str = "起点→终点"; break;
        case -1: direction_str = "终点→起点"; break;
        default: direction_str = "未知"; break;
    }
    
    double length = std::sqrt(std::pow(track.end_point.x - track.start_point.x, 2) + 
                             std::pow(track.end_point.y - track.start_point.y, 2));
    
    QString info = QString("轨道ID: %1\n"
                          "起点: (%.2f, %.2f)\n"
                          "终点: (%.2f, %.2f)\n"
                          "方向: %2\n"
                          "长度: %.2f m")
                          .arg(QString::fromStdString(track.track_id))
                          .arg(track.start_point.x)
                          .arg(track.start_point.y)
                          .arg(track.end_point.x)
                          .arg(track.end_point.y)
                          .arg(direction_str)
                          .arg(length);
    
    selected_track_info_->setText(info);
}

void TrackManagerRvizPlugin::clearTrackInputs()
{
    track_id_edit_->clear();
    start_x_spin_->setValue(0.0);
    start_y_spin_->setValue(0.0);
    end_x_spin_->setValue(0.0);
    end_y_spin_->setValue(0.0);
    direction_combo_->setCurrentIndex(0);
}

void TrackManagerRvizPlugin::showMessage(const QString& title, const QString& message, bool isError)
{
    QMessageBox::Icon icon = isError ? QMessageBox::Critical : QMessageBox::Information;
    QMessageBox::information(this, title, message, QMessageBox::Ok);
}

geometry_msgs::msg::Point TrackManagerRvizPlugin::createPoint(double x, double y, double z)
{
    geometry_msgs::msg::Point point;
    point.x = x;
    point.y = y;
    point.z = z;
    return point;
}

void TrackManagerRvizPlugin::publishTrackMarkers()
{
    visualization_msgs::msg::MarkerArray marker_array;
    marker_id_counter_ = 0;
    
    for (const auto& track : current_tracks_) {
        // 创建轨道线标记
        visualization_msgs::msg::Marker line_marker;
        line_marker.header.frame_id = "map";
        line_marker.header.stamp = node_->now();
        line_marker.ns = "tracks";
        line_marker.id = marker_id_counter_++;
        line_marker.type = visualization_msgs::msg::Marker::LINE_STRIP;
        line_marker.action = visualization_msgs::msg::Marker::ADD;
        
        // 设置轨道线的几何形状
        geometry_msgs::msg::Point p1, p2;
        p1.x = track.start_point.x;
        p1.y = track.start_point.y;
        p1.z = 0.0;
        p2.x = track.end_point.x;
        p2.y = track.end_point.y;
        p2.z = 0.0;
        
        line_marker.points.push_back(p1);
        line_marker.points.push_back(p2);
        
        // 设置轨道线的样式
        line_marker.scale.x = 0.1; // 线宽
        line_marker.color.r = 0.0;
        line_marker.color.g = 1.0;
        line_marker.color.b = 0.0;
        line_marker.color.a = 0.8;
        
        marker_array.markers.push_back(line_marker);
        
        // 创建起点标记
        visualization_msgs::msg::Marker start_marker;
        start_marker.header.frame_id = "map";
        start_marker.header.stamp = node_->now();
        start_marker.ns = "track_starts";
        start_marker.id = marker_id_counter_++;
        start_marker.type = visualization_msgs::msg::Marker::SPHERE;
        start_marker.action = visualization_msgs::msg::Marker::ADD;
        start_marker.pose.position = track.start_point;
        start_marker.scale.x = 0.2;
        start_marker.scale.y = 0.2;
        start_marker.scale.z = 0.2;
        start_marker.color.r = 0.0;
        start_marker.color.g = 0.0;
        start_marker.color.b = 1.0;
        start_marker.color.a = 1.0;
        
        marker_array.markers.push_back(start_marker);
        
        // 创建终点标记
        visualization_msgs::msg::Marker end_marker;
        end_marker.header.frame_id = "map";
        end_marker.header.stamp = node_->now();
        end_marker.ns = "track_ends";
        end_marker.id = marker_id_counter_++;
        end_marker.type = visualization_msgs::msg::Marker::SPHERE;
        end_marker.action = visualization_msgs::msg::Marker::ADD;
        end_marker.pose.position = track.end_point;
        end_marker.scale.x = 0.2;
        end_marker.scale.y = 0.2;
        end_marker.scale.z = 0.2;
        end_marker.color.r = 1.0;
        end_marker.color.g = 0.0;
        end_marker.color.b = 0.0;
        end_marker.color.a = 1.0;
        
        marker_array.markers.push_back(end_marker);
    }
    
    track_markers_pub_->publish(marker_array);
}

void TrackManagerRvizPlugin::clearTrackMarkers()
{
    visualization_msgs::msg::MarkerArray marker_array;
    
    // 清除轨道线标记
    visualization_msgs::msg::Marker clear_line_marker;
    clear_line_marker.header.frame_id = "map";
    clear_line_marker.header.stamp = node_->now();
    clear_line_marker.ns = "tracks";
    clear_line_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_line_marker);
    
    // 清除起点标记
    visualization_msgs::msg::Marker clear_start_marker;
    clear_start_marker.header.frame_id = "map";
    clear_start_marker.header.stamp = node_->now();
    clear_start_marker.ns = "track_starts";
    clear_start_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_start_marker);
    
    // 清除终点标记
    visualization_msgs::msg::Marker clear_end_marker;
    clear_end_marker.header.frame_id = "map";
    clear_end_marker.header.stamp = node_->now();
    clear_end_marker.ns = "track_ends";
    clear_end_marker.action = visualization_msgs::msg::Marker::DELETEALL;
    marker_array.markers.push_back(clear_end_marker);
    
    track_markers_pub_->publish(marker_array);
}

} // namespace voronoi_topo_planner
