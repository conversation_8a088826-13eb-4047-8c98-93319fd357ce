<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_amcl</name>
  <version>1.1.18</version>
  <description>
    <p>
      amcl is a probabilistic localization system for a robot moving in
      2D. It implements the adaptive (or KLD-sampling) Monte Carlo
      localization approach (as described by <PERSON><PERSON>), which uses a
      particle filter to track the pose of a robot against a known map.
    </p>
    <p>
      This node is derived, with thanks, from <PERSON>'s excellent
      'amcl' Player driver.
    </p>
  </description>
  <!-- <author>Brian <PERSON></author>
  <author><EMAIL></author> -->
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>LGPL-2.1-or-later</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>nav2_common</build_depend>
  <depend>rclcpp</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>message_filters</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>std_srvs</depend>
  <depend>tf2_ros</depend>
  <depend>tf2</depend>
  <depend>nav2_util</depend>
  <depend>nav2_msgs</depend>
  <depend>launch_ros</depend>
  <depend>launch_testing</depend>
  <depend>pluginlib</depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
