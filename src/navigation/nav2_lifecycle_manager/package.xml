<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_lifecycle_manager</name>
  <version>1.1.18</version>
  <description>A controller/manager for the lifecycle nodes of the Navigation 2 system</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>geometry_msgs</build_depend>
  <build_depend>lifecycle_msgs</build_depend>
  <build_depend>nav2_msgs</build_depend>
  <build_depend>nav2_util</build_depend>
  <build_depend>rclcpp_action</build_depend>
  <build_depend>rclcpp_lifecycle</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>std_srvs</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>
  <build_depend>bondcpp</build_depend>
  <build_depend>nav2_common</build_depend>
  <build_depend>diagnostic_updater</build_depend>

  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>lifecycle_msgs</exec_depend>
  <exec_depend>nav2_msgs</exec_depend>
  <exec_depend>nav2_util</exec_depend>
  <exec_depend>rclcpp_action</exec_depend>
  <exec_depend>rclcpp_lifecycle</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>std_srvs</exec_depend>
  <exec_depend>bondcpp</exec_depend>
  <exec_depend>tf2_geometry_msgs</exec_depend>
  <exec_depend>diagnostic_updater</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
