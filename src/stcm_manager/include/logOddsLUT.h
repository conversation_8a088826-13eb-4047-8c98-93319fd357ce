/*
 *  log Odds LookUp Table for Grid Map
 *
 */


#pragma once
#include <system/types.h>

namespace rslamware{

template <typename CELLTYPE> struct LogOddsLUT_traits;


// Specializations:
template <> struct LogOddsLUT_traits<rpos::system::types::_s8>
{
    static const rpos::system::types::_s8 CELLTYPE_MIN  = -127; 
    static const rpos::system::types::_s8 CELLTYPE_MAX  = 127;
    static const rpos::system::types::_s8 P2LTABLE_SIZE = CELLTYPE_MAX;
    static const size_t LOGODDS_LUT_ENTRIES = 1<<8;
};
template <> struct LogOddsLUT_traits<rpos::system::types::_s16>
{
    static const rpos::system::types::_s16 CELLTYPE_MIN  = -32767; 
    static const rpos::system::types::_s16 CELLTYPE_MAX  = 32767;
    static const rpos::system::types::_s16 P2LTABLE_SIZE = CELLTYPE_MAX;
    static const size_t  LOGODDS_LUT_ENTRIES = 1<<16;
};


// LUT Helper functions for manipulate occupancy map
template <typename CELLTYPE>
struct LogOddsLUTHelper : public LogOddsLUT_traits<CELLTYPE>
{
    typedef CELLTYPE cell_t; //!< The type of cells
    typedef LogOddsLUT_traits<CELLTYPE>  traits_t;

    /** Performs the Bayesian fusion of a new observation of a cell, without checking for grid limits nor updateInfoChangeOnly.
    * This method increases the "occupancy-ness" of a cell, managing possible saturation.
    *  \param x Cell index in X axis.
    *  \param y Cell index in Y axis.
    *  \param logodd_obs Observation of the cell, in log-odd form as transformed by p2l.
    *  \param thres  This must be CELLTYPE_MIN+logodd_obs
    * \sa updateCell, updateCell_fast_free
    */
    inline static void  updateCell_fast_occupied(
        const unsigned	x,
        const unsigned	y,
        const cell_t	logodd_obs,
        const cell_t  thres,
        cell_t		*mapArray,
        const unsigned	_size_x)
    {
        cell_t *theCell = mapArray + (x+y*_size_x);
        if (*theCell > thres )
            *theCell -= logodd_obs;
        else	*theCell = traits_t::CELLTYPE_MIN;
    }

    /** Performs the Bayesian fusion of a new observation of a cell, without checking for grid limits nor updateInfoChangeOnly.
    * This method increases the "occupancy-ness" of a cell, managing possible saturation.
    *  \param theCell The cell to modify
    *  \param logodd_obs Observation of the cell, in log-odd form as transformed by p2l.
    *  \param thres  This must be CELLTYPE_MIN+logodd_obs
    * \sa updateCell, updateCell_fast_free
    */
    inline static void  updateCell_fast_occupied(
        cell_t		*theCell,
        const cell_t	logodd_obs,
        const cell_t  thres )
    {
        if (*theCell > thres )
            *theCell -= logodd_obs;
        else	*theCell = traits_t::CELLTYPE_MIN;
    }

    /** Performs the Bayesian fusion of a new observation of a cell, without checking for grid limits nor updateInfoChangeOnly.
    * This method increases the "free-ness" of a cell, managing possible saturation.
    *  \param x Cell index in X axis.
    *  \param y Cell index in Y axis.
    *  \param logodd_obs Observation of the cell, in log-odd form as transformed by p2l.
    *  \param thres  This must be CELLTYPE_MAX-logodd_obs
    * \sa updateCell_fast_occupied
    */
    inline static void  updateCell_fast_free(
        const unsigned	x,
        const unsigned	y,
        const cell_t	logodd_obs,
        const cell_t  thres,
        cell_t		*mapArray,
        const unsigned	_size_x)
    {
        cell_t *theCell = mapArray + (x+y*_size_x);
        if (*theCell < thres )
            *theCell += logodd_obs;
        else	*theCell = traits_t::CELLTYPE_MAX;
    }

    /** Performs the Bayesian fusion of a new observation of a cell, without checking for grid limits nor updateInfoChangeOnly.
    * This method increases the "free-ness" of a cell, managing possible saturation.
    *  \param x Cell index in X axis.
    *  \param y Cell index in Y axis.
    *  \param logodd_obs Observation of the cell, in log-odd form as transformed by p2l.
    *  \param thres  This must be CELLTYPE_MAX-logodd_obs
    * \sa updateCell_fast_occupied
    */
    inline static void  updateCell_fast_free(
        cell_t		*theCell,
        const cell_t	logodd_obs,
        const cell_t  thres)
    {
        if (*theCell < thres )
            *theCell += logodd_obs;
        else	*theCell = traits_t::CELLTYPE_MAX;
    }

};

\
// Main Class of the LogOdds LUT
template <typename CELLTYPE>
struct LogOddsLUT : public LogOddsLUT_traits<CELLTYPE>
{
    typedef CELLTYPE cell_t; //!< The type of
    typedef LogOddsLUT_traits<CELLTYPE>  traits_t;

    // LUT to map occupancy probabilities in [0,1] from integer log-odds values of a cell. Using \f$ p(m_{xy}) = \frac{1}{1+exp(-log_odd)} \f$.
    std::vector<float>    logoddsTable;

    // LUT to map occupancy probabilities in [0,255] from integer log-odds values of a cell. Using \f$ p(m_{xy}) = \frac{1}{1+exp(-log_odd)} \f$.
    std::vector<rpos::system::types::_u8>   logoddsTable_255;

    /** A lookup table for passing from float to log-odds as cell_t. */
    std::vector<cell_t>    p2lTable;

    /** Constructor: computes all the required stuff. */
    LogOddsLUT()
    {
        // The factor for converting log2-odds into integers:
        static const double LOGODD_K  = 16;
        static const double LOGODD_K_INV = 1.0/LOGODD_K;

        logoddsTable.resize( traits_t::LOGODDS_LUT_ENTRIES );
        logoddsTable_255.resize( traits_t::LOGODDS_LUT_ENTRIES );
        for (int i=traits_t::CELLTYPE_MIN;i<=traits_t::CELLTYPE_MAX;i++)
        {
            float f = static_cast<float>(1.0f / (1.0f + exp( - i * LOGODD_K_INV ) ));
            unsigned int idx =  -traits_t::CELLTYPE_MIN+i;
            logoddsTable[idx] = f;
            logoddsTable_255[idx] = (uint8_t)(f*255.0f);
        }

        // Build the p2lTable as well:
        p2lTable.resize( traits_t::P2LTABLE_SIZE+1 );
        double K = 1.0 / traits_t::P2LTABLE_SIZE;
        for (int j=0;j<=traits_t::P2LTABLE_SIZE;j++)
        {
            double p = j*K;
            if (p==0)
                p=1e-14;
            else if (p==1)
                p=1-1e-14;

            double logodd = log(p)-log(1-p);
            int   L = static_cast<int>(floor(logodd * LOGODD_K + 0.5f));
            if (L>traits_t::CELLTYPE_MAX)
                L=traits_t::CELLTYPE_MAX;
            else if (L<traits_t::CELLTYPE_MIN)
                L=traits_t::CELLTYPE_MIN;
            p2lTable[j] = L;
        }

    }

    /** Scales an integer representation of the log-odd into a real valued probability in [0,1], using p=exp(l)/(1+exp(l))
    */
    inline float l2p(const cell_t l)
    {
        if (l<traits_t::CELLTYPE_MIN)	
            return logoddsTable[ 0 ];  // This is needed since min can be -127 and int8_t can be -128.
        else return logoddsTable[ -traits_t::CELLTYPE_MIN+l ];
    }

    /** Scales an integer representation of the log-odd into a linear scale [0,255], using p=exp(l)/(1+exp(l))
    */
    inline rpos::system::types::_u8 l2p_255(const cell_t l)
    {
        if (l<traits_t::CELLTYPE_MIN)	
            return logoddsTable_255[ 0 ]; // This is needed since min can be -127 and int8_t can be -128.
        else return logoddsTable_255[ -traits_t::CELLTYPE_MIN+l ];
    }

    /** Scales a real valued probability in [0,1] to an integer representation of: log(p)-log(1-p)  in the valid range of cell_t.
    */
    inline cell_t p2l(const float p) const
    {
        return p2lTable[ static_cast<unsigned int>(p * traits_t::P2LTABLE_SIZE) ];
    }

}; 

}
