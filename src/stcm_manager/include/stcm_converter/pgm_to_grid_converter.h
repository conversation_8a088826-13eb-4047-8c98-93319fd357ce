#pragma once

#include <string>
#include <vector>
#include <memory>
#include <fstream>
#include <sstream>

// Include rpos_common headers for STCM support
#include <stcm/grid_map_layer.h>
#include <stcm/composite_map.h>
#include <stcm/composite_map_writer.h>
#include <core/geometry.h>
#include <core/pose.h>
#include "../logOddsLUT.h"

namespace rslamware { namespace stcm_manager {

/**
 * @brief Structure to hold PGM file information
 */
struct PgmInfo {
    int width;
    int height;
    int max_value;
    std::string magic_number;
    std::vector<uint8_t> data;
    
    PgmInfo() : width(0), height(0), max_value(0) {}
};

/**
 * @brief Structure to hold grid map parameters
 */
struct GridMapParams {
    std::string image_file; // Path to the image file
    double origin_x;        // Origin X coordinate in meters
    double origin_y;        // Origin Y coordinate in meters
    double origin_theta;    // Origin rotation in radians
    double resolution;      // Resolution in meters per pixel
    bool negate;           // Whether to negate the image (black=free, white=occupied)
    double occupied_thresh; // Threshold for occupied pixels (0-1)
    double free_thresh;    // Threshold for free pixels (0-1)
    double occupied_threshold; // Alias for occupied_thresh
    double free_threshold;     // Alias for free_thresh

    GridMapParams()
        : image_file(""), origin_x(0.0), origin_y(0.0), origin_theta(0.0)
        , resolution(0.05), negate(false)
        , occupied_thresh(0.65), free_thresh(0.196)
        , occupied_threshold(0.65), free_threshold(0.196) {}
};

/**
 * @brief Class for converting PGM files to GridMapLayer
 * 
 * This class reads PGM (Portable Gray Map) files and converts them
 * to GridMapLayer format compatible with STCM files.
 */
class PgmToGridConverter {
public:
    /**
     * @brief Constructor
     */
    PgmToGridConverter();
    
    /**
     * @brief Destructor
     */
    ~PgmToGridConverter();
    
    /**
     * @brief Load PGM file from disk
     * @param file_path Path to the PGM file
     * @return true if successful, false otherwise
     */
    bool loadPgmFile(const std::string& file_path);

    /**
     * @brief Load PGM file and YAML parameters simultaneously
     * @param pgm_file_path Path to the PGM file
     * @param yaml_file_path Path to the YAML file
     * @return true if both files loaded successfully, false otherwise
     */
    bool loadPgmAndParams(const std::string& pgm_file_path, const std::string& yaml_file_path);

    /**
     * @brief Convert loaded PGM to grid map data
     * @return true if successful, false otherwise
     */
    bool convertToGridMap();
    
    /**
     * @brief Get the converted grid map data
     * @return Vector containing grid map data (0=free, 100=occupied, -1=unknown)
     */
    const std::vector<uint8_t>& getGridMapData() const;
    
    /**
     * @brief Get PGM file information
     * @return PGM file information
     */
    const PgmInfo& getPgmInfo() const;
    
    /**
     * @brief Get grid map width
     * @return Width in pixels
     */
    int getWidth() const;
    
    /**
     * @brief Get grid map height
     * @return Height in pixels
     */
    int getHeight() const;
    
    /**
     * @brief Get grid map resolution
     * @return Resolution in meters per pixel
     */
    double getResolution() const;
    
    /**
     * @brief Get grid map origin
     * @param origin_x Output origin X coordinate
     * @param origin_y Output origin Y coordinate
     */
    void getOrigin(double& origin_x, double& origin_y) const;
    
    /**
     * @brief Get error message from last operation
     * @return Error message string
     */
    const std::string& getLastError() const;

    /**
     * @brief Load grid map parameters from YAML file
     * @param yaml_file_path Path to the YAML file
     * @return true if successful, false otherwise
     */
    bool loadParamsFromYaml(const std::string& yaml_file_path);

    /**
     * @brief Create GridMapLayer from loaded PGM and parameters
     * @return Shared pointer to GridMapLayer, nullptr if failed
     */
    std::shared_ptr<rpos::stcm::GridMapLayer> createGridMapLayer();

    /**
     * @brief Save GridMapLayer to PGM and YAML files
     * @param grid_layer Shared pointer to GridMapLayer to save
     * @param pgm_file_path Path to save the PGM file
     * @param yaml_file_path Path to save the YAML file
     * @return true if both files saved successfully, false otherwise
     */
    bool saveGridMapToPgm(std::shared_ptr<rpos::stcm::GridMapLayer> grid_layer,
                          const std::string& pgm_file_path,
                          const std::string& yaml_file_path);

private:
    /**
     * @brief Parse PGM header
     * @param file Input file stream
     * @return true if successful, false otherwise
     */
    bool parsePgmHeader(std::ifstream& file);
    
    /**
     * @brief Read PGM data
     * @param file Input file stream
     * @return true if successful, false otherwise
     */
    bool readPgmData(std::ifstream& file);
    
    /**
     * @brief Skip comments in PGM file
     * @param file Input file stream
     */
    void skipComments(std::ifstream& file);
    
    /**
     * @brief Convert pixel value to occupancy value
     * @param pixel_value Input pixel value (0-max_value)
     * @return Occupancy value 
     */
    int8_t pixelToOccupancy(uint8_t pixel_value) const;

    /**
     * @brief Parse YAML file to extract grid map parameters
     * @param yaml_file_path Path to the YAML file
     * @param params Output parameters
     * @return true if successful, false otherwise
     */
    bool parseYamlFile(const std::string& yaml_file_path, GridMapParams& params);

    /**
     * @brief Save grid data as PGM file
     * @param file_path Path to save the PGM file
     * @param width Image width
     * @param height Image height
     * @param grid_data Grid data to save
     * @return true if successful, false otherwise
     */
    bool savePgmFile(const std::string& file_path, int width, int height,
                     const std::vector<int8_t>& grid_data) const;

    /**
     * @brief Save map parameters as YAML file
     * @param file_path Path to save the YAML file
     * @param origin Map origin
     * @param resolution Map resolution
     * @param metadata Optional metadata containing parameters (can be nullptr)
     * @return true if successful, false otherwise
     */
    bool saveYamlFile(const std::string& file_path,
                      const rpos::core::Location& origin,
                      const rpos::core::Vector2f& resolution,
                      const rpos::core::Metadata* metadata = nullptr) const;

private:
    PgmInfo pgm_info_;
    GridMapParams params_;
    std::vector<uint8_t> grid_data_;
    std::string last_error_;
    bool is_loaded_;
    bool is_converted_;    
    rslamware::LogOddsLUT<rpos::system::types::_s8> logLUT_;
};

}} // namespace stcm_manager
