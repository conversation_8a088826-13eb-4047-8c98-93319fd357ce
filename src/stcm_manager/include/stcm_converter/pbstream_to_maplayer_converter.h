#pragma once

#include <string>
#include <vector>
#include <memory>

#include <stcm/image_features_map_layer.h>
#include <stcm/composite_map_writer.h>

namespace rslamware { namespace stcm_manager {

/**
 * @brief Converter class to read pbstream files and generate ImageFeatureMapLayer
 * 
 * This class reads Cartographer pbstream files and converts them into 
 * ImageFeatureMapLayer objects with FeatureTypeCartographer.
 */
class PbstreamToMapLayerConverter
{
public:
    PbstreamToMapLayerConverter();
    ~PbstreamToMapLayerConverter();

    /**
     * @brief Convert a pbstream file to ImageFeatureMapLayer and save to STCM
     *
     * @param pbstream_file_path Path to the input pbstream file
     * @param layer_name Optional name for the map layer
     * @return true if conversion was successful, false otherwise
     */
    std::shared_ptr<rpos::stcm::ImageFeaturesMapLayer> convertPbstreamToMapLayer(
        const std::string& pbstream_file_path, 
        const std::string& layer_name = "cartographer_features"
    );
private:
    /**
     * @brief Create ImageFeatureMapLayer from pbstream data
     *
     * @param pbstream_data Binary data from pbstream file
     * @param layer_name Optional name for the map layer
     * @return Shared pointer to the created ImageFeatureMapLayer
     */
    std::shared_ptr<rpos::stcm::ImageFeaturesMapLayer> createMapLayerFromPbstream(
        const std::vector<uint8_t>& pbstream_data,
        const std::string& layer_name = "cartographer_features"
    );

    /**
     * @brief Read pbstream file into binary data
     *
     * @param file_path Path to the pbstream file
     * @param data Output vector to store the binary data
     * @return true if reading was successful, false otherwise
     */
    bool readPbstreamFile(const std::string& file_path, std::vector<uint8_t>& data);

    /**
     * @brief Validate pbstream file format
     *
     * @param file_path Path to the pbstream file
     * @return true if file is a valid pbstream, false otherwise
     */
    bool validatePbstreamFile(const std::string& file_path);

    /**
     * @brief Get the last error message
     *
     * @return String containing the last error message
     */
    const std::string& getLastError() const { return last_error_; }

private:
    std::string last_error_;

    /**
     * @brief Set error message
     * 
     * @param error Error message to set
     */
    void setError(const std::string& error);
};

}} // namespace stcm_manager
