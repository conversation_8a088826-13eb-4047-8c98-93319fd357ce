#include <iostream>
#include <filesystem>
#include <fstream>
#include <string>

#include "stcm_converter/pbstream_to_maplayer_converter.h"
#include "stcm_converter/pgm_to_grid_converter.h"
#include <logOddsLUT.h>

// Include rpos_common headers for STCM support
#include <stcm/composite_map.h>
#include <stcm/composite_map_writer.h>
#include <stcm/grid_map_layer.h>
#include <stcm/image_features_map_layer.h>

namespace fs = std::filesystem;
using namespace rslamware::stcm_manager;

/**
 * @brief Test map converter that integrates pbstream and pgm/yaml conversion
 * 
 * This test reads a folder containing:
 * - *.pbstream file (for ImageFeaturesMapLayer)
 * - *.pgm and *.yaml files (for GridMapLayer)
 * 
 * Then creates a CompositeMap with both layers and saves as robot_map.stcm
 */
class MapConverterTest {
public:
    MapConverterTest() = default;
    
    /**
     * @brief Convert maps from a folder and create robot_map.stcm
     * @param folder_path Path to folder containing map files
     * @return true if conversion successful, false otherwise
     */
    bool convertMapsFromFolder(const std::string& folder_path) {
        std::cout << "Converting maps from folder: " << folder_path << std::endl;
        
        if (!fs::exists(folder_path) || !fs::is_directory(folder_path)) {
            std::cerr << "Error: Folder does not exist or is not a directory: " << folder_path << std::endl;
            return false;
        }
        
        // Find required files
        std::string pbstream_file;
        std::string pgm_file;
        std::string yaml_file;
        
        if (!findMapFiles(folder_path, pbstream_file, pgm_file, yaml_file)) {
            return false;
        }
        
        // Convert pbstream to ImageFeaturesMapLayer
        auto image_layer = convertPbstreamToImageLayer(pbstream_file);
        if (!image_layer) {
            std::cerr << "Failed to convert pbstream to ImageFeaturesMapLayer" << std::endl;
            return false;
        }
        
        // Convert pgm/yaml to GridMapLayer
        auto grid_layer = convertPgmYamlToGridLayer(pgm_file, yaml_file);
        if (!grid_layer) {
            std::cerr << "Failed to convert pgm/yaml to GridMapLayer" << std::endl;
            return false;
        }
        
        // Create CompositeMap and add layers
        auto composite_map = createCompositeMap(image_layer, grid_layer);
        if (!composite_map) {
            std::cerr << "Failed to create CompositeMap" << std::endl;
            return false;
        }
        
        // Save as robot_map.stcm
        std::string output_path = (fs::path(folder_path) / "robot_map.stcm").string();
        if (!saveCompositeMap(composite_map, output_path)) {
            std::cerr << "Failed to save CompositeMap to: " << output_path << std::endl;
            return false;
        }
        
        std::cout << "Successfully created robot_map.stcm at: " << output_path << std::endl;
        return true;
    }
    
private:
    /**
     * @brief Find required map files in the folder
     */
    bool findMapFiles(const std::string& folder_path, 
                     std::string& pbstream_file, 
                     std::string& pgm_file, 
                     std::string& yaml_file) {
        
        for (const auto& entry : fs::directory_iterator(folder_path)) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                std::string extension = entry.path().extension().string();
                
                if (filename == "map.pbstream") {
                    pbstream_file = entry.path().string();
                    std::cout << "Found pbstream file: " << filename << std::endl;
                } else if (filename == "map.pgm") {
                    pgm_file = entry.path().string();
                    std::cout << "Found pgm file: " << filename << std::endl;
                } else if (filename == "map.yaml") {
                    yaml_file = entry.path().string();
                    std::cout << "Found yaml file: " << filename << std::endl;
                }
            }
        }
        
        if (pbstream_file.empty()) {
            std::cerr << "Error: No .pbstream file found in folder" << std::endl;
            return false;
        }
        
        if (pgm_file.empty()) {
            std::cerr << "Error: No .pgm file found in folder" << std::endl;
            return false;
        }
        
        if (yaml_file.empty()) {
            std::cerr << "Error: No .yaml file found in folder" << std::endl;
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief Convert pbstream file to ImageFeaturesMapLayer
     */
    std::shared_ptr<rpos::stcm::ImageFeaturesMapLayer> convertPbstreamToImageLayer(const std::string& pbstream_file) {
        std::cout << "Converting pbstream to ImageFeaturesMapLayer..." << std::endl;
        
        PbstreamToMapLayerConverter converter;
        
        // Use the public method to convert pbstream to map layer
        auto layer = converter.convertPbstreamToMapLayer(pbstream_file, "cartographer_features");
        
        if (layer) {
            std::cout << "Successfully converted pbstream to ImageFeaturesMapLayer" << std::endl;
        }
        
        return layer;
    }
    
    /**
     * @brief Convert pgm and yaml files to GridMapLayer
     */
    std::shared_ptr<rpos::stcm::GridMapLayer> convertPgmYamlToGridLayer(const std::string& pgm_file, const std::string& yaml_file) {
        std::cout << "Converting pgm/yaml to GridMapLayer..." << std::endl;
        
        PgmToGridConverter converter;

        // Load PGM file and YAML parameters simultaneously
        if (!converter.loadPgmAndParams(pgm_file, yaml_file)) {
            std::cerr << "Failed to load PGM and YAML files: " << converter.getLastError() << std::endl;
            return nullptr;
        }

        // Convert to grid map
        if (!converter.convertToGridMap()) {
            std::cerr << "Failed to convert to GridMap: " << converter.getLastError() << std::endl;
            return nullptr;
        }

        // Create GridMapLayer
        auto layer = converter.createGridMapLayer();
        if (layer) {
            std::cout << "Successfully converted pgm/yaml to GridMapLayer" << std::endl;
        }
        
        return layer;
    }
    
    /**
     * @brief Create CompositeMap with both layers
     */
    std::shared_ptr<rpos::stcm::CompositeMap> createCompositeMap(
        std::shared_ptr<rpos::stcm::ImageFeaturesMapLayer> image_layer,
        std::shared_ptr<rpos::stcm::GridMapLayer> grid_layer) {
        
        std::cout << "Creating CompositeMap with both layers..." << std::endl;
        
        try {
            auto composite_map = std::make_shared<rpos::stcm::CompositeMap>();

            // Add ImageFeaturesMapLayer
            if (image_layer) {
                composite_map->maps().push_back(image_layer);
                std::cout << "Added ImageFeaturesMapLayer to CompositeMap" << std::endl;
            }

            // Add GridMapLayer
            if (grid_layer) {
                composite_map->maps().push_back(grid_layer);
                std::cout << "Added GridMapLayer to CompositeMap" << std::endl;
            }

            std::cout << "Successfully created CompositeMap with " << composite_map->maps().size() << " layers" << std::endl;
            return composite_map;
            
        } catch (const std::exception& e) {
            std::cerr << "Error creating CompositeMap: " << e.what() << std::endl;
            return nullptr;
        }
    }
    
    /**
     * @brief Save CompositeMap to STCM file
     */
    bool saveCompositeMap(std::shared_ptr<rpos::stcm::CompositeMap> composite_map, const std::string& output_path) {
        std::cout << "Saving CompositeMap to: " << output_path << std::endl;
        
        try {
            rpos::stcm::CompositeMapWriter writer;

            writer.saveFile(output_path, *composite_map);
            std::cout << "Successfully saved CompositeMap" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "Error saving CompositeMap: " << e.what() << std::endl;
            return false;
        }
    }
};

int main(int argc, char** argv) {
    if (argc != 2) {
        std::cout << "Usage: " << argv[0] << " <folder_path>" << std::endl;
        std::cout << "  folder_path: Path to folder containing .pbstream, .pgm, and .yaml files" << std::endl;
        std::cout << std::endl;
        std::cout << "Example:" << std::endl;
        std::cout << "  " << argv[0] << " /path/to/map/folder" << std::endl;
        std::cout << std::endl;
        std::cout << "The folder should contain:" << std::endl;
        std::cout << "  - *.pbstream file (cartographer output)" << std::endl;
        std::cout << "  - *.pgm file (occupancy grid image)" << std::endl;
        std::cout << "  - *.yaml file (map metadata)" << std::endl;
        std::cout << std::endl;
        std::cout << "Output: robot_map.stcm will be created in the same folder" << std::endl;
        return 1;
    }
    
    std::string folder_path = argv[1];
    
    MapConverterTest converter;
    
    if (converter.convertMapsFromFolder(folder_path)) {
        std::cout << "Map conversion completed successfully!" << std::endl;
        return 0;
    } else {
        std::cout << "Map conversion failed!" << std::endl;
        return 1;
    }
}
