#include <rclcpp/rclcpp.hpp>
#include <filesystem>
#include <fstream>
#include <iostream>

#include "stcm_manager/srv/get_stcm_file.hpp"
#include "stcm_manager/srv/upload_stcm_file.hpp"

namespace fs = std::filesystem;

class StcmTestClient : public rclcpp::Node
{
public:
    StcmTestClient() : Node("stcm_test_client")
    {
        // Create service clients
        get_client_ = this->create_client<stcm_manager::srv::GetStcmFile>("get_stcm_file");
        upload_client_ = this->create_client<stcm_manager::srv::UploadStcmFile>("upload_stcm_file");
        
        RCLCPP_INFO(this->get_logger(), "STCM Test Client initialized");
    }
    
    bool testUploadFile(const std::string& file_path, const std::string& target_name = "")
    {
        if (!upload_client_->wait_for_service(std::chrono::seconds(5))) {
            RCLCPP_ERROR(this->get_logger(), "Upload service not available");
            return false;
        }
        
        // Read test file
        if (!fs::exists(file_path)) {
            RCLCPP_ERROR(this->get_logger(), "Test file not found: %s", file_path.c_str());
            return false;
        }
        
        std::ifstream file(file_path, std::ios::binary);
        if (!file.is_open()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to open test file: %s", file_path.c_str());
            return false;
        }
        
        // Read file data
        auto file_size = fs::file_size(file_path);
        std::vector<uint8_t> file_data(file_size);
        file.read(reinterpret_cast<char*>(file_data.data()), file_size);
        file.close();
        
        // Create request
        auto request = std::make_shared<stcm_manager::srv::UploadStcmFile::Request>();
        request->file_data = file_data; 
        
        RCLCPP_INFO(this->get_logger(), "Uploading file(%lu bytes)", file_data.size());
        
        // Send request
        auto future = upload_client_->async_send_request(request);
        
        if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), future) ==
            rclcpp::FutureReturnCode::SUCCESS) {
            auto response = future.get();
            
            if (response->success) {
                RCLCPP_INFO(this->get_logger(), "Upload successful: %s", response->message.c_str()); 
                return true;
            } else {
                RCLCPP_ERROR(this->get_logger(), "Upload failed: %s", response->message.c_str());
                return false;
            }
        } else {
            RCLCPP_ERROR(this->get_logger(), "Upload request failed");
            return false;
        }
    }
    
    bool testGetFile(const std::string& file_path)
    {
        if (!get_client_->wait_for_service(std::chrono::seconds(5))) {
            RCLCPP_ERROR(this->get_logger(), "Get service not available");
            return false;
        }
        
        // Create request
        auto request = std::make_shared<stcm_manager::srv::GetStcmFile::Request>(); 
        
        RCLCPP_INFO(this->get_logger(), "Download stcm to: %s", file_path.c_str());
        
        // Send request
        auto future = get_client_->async_send_request(request);
        
        if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), future) ==
            rclcpp::FutureReturnCode::SUCCESS) {
            auto response = future.get();
            
            if (response->success) {
                 
                // Optionally save the received file
                std::ofstream output_file(file_path, std::ios::binary);
                if (output_file.is_open()) {
                    output_file.write(reinterpret_cast<const char*>(response->file_data.data()), 
                                    response->file_data.size());
                    output_file.close();
                    RCLCPP_INFO(this->get_logger(), "File saved to: %s", file_path.c_str());
                }
                
                return true;
            } else {
                return false;
            }
        } else {
            RCLCPP_ERROR(this->get_logger(), "Get request failed");
            return false;
        }
    }

private:
    rclcpp::Client<stcm_manager::srv::GetStcmFile>::SharedPtr get_client_;
    rclcpp::Client<stcm_manager::srv::UploadStcmFile>::SharedPtr upload_client_;
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    
    if (argc < 3) {
        std::cout << "Usage: " << argv[0] << " <command> <file_path> [target_name]" << std::endl;
        std::cout << "Commands:" << std::endl;
        std::cout << "  upload <file_path> [target_name] - Upload a file" << std::endl;
        std::cout << "  get <file_path> - Download a file" << std::endl;
        std::cout << "  test <file_path> - Test upload then download" << std::endl;
        return 1;
    }
    
    auto client = std::make_shared<StcmTestClient>();
    
    std::string command = argv[1];
    std::string file_path = argv[2];
    std::string target_name = (argc > 3) ? argv[3] : "";
    
    bool success = false;
    
    if (command == "upload") {
        success = client->testUploadFile(file_path, target_name);
    } else if (command == "get") {
        success = client->testGetFile(file_path);
    } else if (command == "test") {
        // Test upload then download
        std::string upload_name = target_name.empty() ? 
            fs::path(file_path).filename().string() : target_name;
        
        if (client->testUploadFile(file_path, upload_name)) {
            success = client->testGetFile(upload_name);
        }
    } else {
        RCLCPP_ERROR(client->get_logger(), "Unknown command: %s", command.c_str());
    }
    
    rclcpp::shutdown();
    return success ? 0 : 1;
}
