#include <iostream>
#include <string>
#include <iomanip>
#include <memory>

// Include rpos_common headers for STCM reading
#include <stcm/composite_map_reader.h>
#include <stcm/grid_map_layer.h>
#include <stcm/line_map_layer.h>
#include <stcm/pose_map_layer.h>
#include <stcm/points_map_layer.h>
#include <stcm/rectangle_area_map_layer.h>
#include <stcm/polygon_area_map_layer.h>
#include <stcm/image_features_map_layer.h>

using namespace rpos::stcm;

// Forward declarations
void printLayerInfo(std::shared_ptr<MapLayer> layer, int index);
void printGridLayerInfo(std::shared_ptr<GridMapLayer> layer);
void printLineLayerInfo(std::shared_ptr<LineMapLayer> layer);
void printPoseLayerInfo(std::shared_ptr<PoseMapLayer> layer);
void printPointsLayerInfo(std::shared_ptr<PointsMapLayer> layer);
void printRectangleAreaLayerInfo(std::shared_ptr<RectangleAreaMapLayer> layer);
void printPolygonAreaLayerInfo(std::shared_ptr<PolygonAreaMapLayer> layer);
void printImageFeaturesLayerInfo(std::shared_ptr<ImageFeaturesMapLayer> layer);

/**
 * @brief Print basic information about a map layer
 * @param layer Pointer to the map layer
 * @param index Layer index
 */
void printLayerInfo(std::shared_ptr<MapLayer> layer, int index) {
    if (!layer) {
        std::cout << "Layer " << index << ": NULL" << std::endl;
        return;
    }
    
    std::cout << "\n--- Layer " << index << " ---" << std::endl;
    
    // Get metadata
    const auto& metadata = layer->metadata();
    std::cout << "Type: " << layer->getType() << std::endl;
    std::cout << "Usage: " << layer->getUsage() << std::endl;
    
    // Print metadata
    std::cout << "Metadata:" << std::endl;
    const auto& metaDict = metadata.dict();
    for (const auto& pair : metaDict) {
        if(pair.first != "usage" && pair.first != "type")
            std::cout << "  " << pair.first << ": " << pair.second << std::endl;
    }
    
    // Try to cast to specific layer types and print detailed info
    if (auto gridLayer = std::dynamic_pointer_cast<GridMapLayer>(layer)) {
        printGridLayerInfo(gridLayer);
    } else if (auto lineLayer = std::dynamic_pointer_cast<LineMapLayer>(layer)) {
        printLineLayerInfo(lineLayer);
    } else if (auto poseLayer = std::dynamic_pointer_cast<PoseMapLayer>(layer)) {
        printPoseLayerInfo(poseLayer);
    } else if (auto pointsLayer = std::dynamic_pointer_cast<PointsMapLayer>(layer)) {
        printPointsLayerInfo(pointsLayer);
    } else if (auto rectAreaLayer = std::dynamic_pointer_cast<RectangleAreaMapLayer>(layer)) {
        printRectangleAreaLayerInfo(rectAreaLayer);
    } else if (auto polyAreaLayer = std::dynamic_pointer_cast<PolygonAreaMapLayer>(layer)) {
        printPolygonAreaLayerInfo(polyAreaLayer);
    } else if (auto imageFeaturesLayer = std::dynamic_pointer_cast<ImageFeaturesMapLayer>(layer)) {
        printImageFeaturesLayerInfo(imageFeaturesLayer);
    } else {
        std::cout << "Unknown layer type" << std::endl;
    }
}

/**
 * @brief Print detailed information about a GridMapLayer
 */
void printGridLayerInfo(std::shared_ptr<GridMapLayer> layer) {
    std::cout << "Grid Map Layer Details:" << std::endl;
    
    const auto& origin = layer->getOrigin();
    const auto& dimension = layer->getDimension();
    const auto& resolution = layer->getResolution();
    const auto& mapData = layer->mapData();
    
    std::cout << "  Origin: (" << origin.x() << ", " << origin.y() << ", " << origin.z() << ")" << std::endl;
    std::cout << "  Dimensions: " << dimension.x() << " x " << dimension.y() << " pixels" << std::endl;
    std::cout << "  Resolution: " << resolution.x() << " x " << resolution.y() << " m/pixel" << std::endl;
    std::cout << "  Physical size: " << (dimension.x() * resolution.x()) << " x " << (dimension.y() * resolution.y()) << " meters" << std::endl;
    std::cout << "  Data size: " << mapData.size() << " bytes" << std::endl;
    
    // Analyze occupancy data
    if (!mapData.empty()) {
        int free_count = 0, occupied_count = 0, unknown_count = 0;
        for (const auto& value : mapData) {
            if (value == 127) {
                free_count++;
            } else if (value == 0x81) {
                occupied_count++;
            } else if(value == 0){
                unknown_count++;
            }
        }
        
        double total = static_cast<double>(mapData.size());
        std::cout << "  Occupancy statistics:" << std::endl;
        std::cout << "    Free: " << free_count << " (" << std::fixed << std::setprecision(1) << (free_count/total*100) << "%)" << std::endl;
        std::cout << "    Occupied: " << occupied_count << " (" << (occupied_count/total*100) << "%)" << std::endl;
        std::cout << "    Unknown: " << unknown_count << " (" << (unknown_count/total*100) << "%)" << std::endl;
    }
}

/**
 * @brief Print detailed information about a LineMapLayer
 */
void printLineLayerInfo(std::shared_ptr<LineMapLayer> layer) {
    std::cout << "Line Map Layer Details:" << std::endl;
    
    const auto& lines = layer->lines();
    std::cout << "  Number of lines: " << lines.size() << std::endl;
}

/**
 * @brief Print detailed information about a PoseMapLayer
 */
void printPoseLayerInfo(std::shared_ptr<PoseMapLayer> layer) {
    std::cout << "Pose Map Layer Details:" << std::endl;
    
    const auto& poses = layer->poses();
    std::cout << "  Number of poses: " << poses.size() << std::endl;
}

/**
 * @brief Print detailed information about a PointsMapLayer
 */
void printPointsLayerInfo(std::shared_ptr<PointsMapLayer> layer) {
    std::cout << "Points Map Layer Details:" << std::endl;
    
    const auto& points = layer->points();
    std::cout << "  Number of points: " << points.size() << std::endl;
    
    if (!points.empty()) {
        std::cout << "  Sample points (first 5):" << std::endl;
        for (size_t i = 0; i < std::min(size_t(5), points.size()); ++i) {
            const auto& point = points[i];
            std::cout << "    Point " << i << ": (" << point.location.x() << ", " << point.location.y() << ")" << std::endl;
        }
    }
}

/**
 * @brief Print detailed information about a RectangleAreaMapLayer
 */
void printRectangleAreaLayerInfo(std::shared_ptr<RectangleAreaMapLayer> layer) {
    std::cout << "Rectangle Area Map Layer Details:" << std::endl;
    
    const auto& areas = layer->areas();
    std::cout << "  Number of rectangle areas: " << areas.size() << std::endl;
    
    if (!areas.empty()) {
        std::cout << "  Sample areas (first 3):" << std::endl;
        for (size_t i = 0; i < std::min(size_t(3), areas.size()); ++i) {
            const auto& area = areas[i];
            std::cout << "    Area " << i << " (ID: " << area.id << ")" << std::endl;
            // Note: area.area is an ORectangleF, detailed printing would require more complex code
        }
    }
}

/**
 * @brief Print detailed information about a PolygonAreaMapLayer
 */
void printPolygonAreaLayerInfo(std::shared_ptr<PolygonAreaMapLayer> layer) {
    std::cout << "Polygon Area Map Layer Details:" << std::endl;

    const auto& areas = layer->areas();
    std::cout << "  Number of polygon areas: " << areas.size() << std::endl;

}

/**
 * @brief Print detailed information about an ImageFeaturesMapLayer
 */
void printImageFeaturesLayerInfo(std::shared_ptr<ImageFeaturesMapLayer> layer) {
    std::cout << "Image Features Map Layer Details:" << std::endl;

    // Get feature type
    auto featureType = layer->getFeatureType();
    std::string featureTypeStr;
    switch (featureType) {
        case FeatureTypeUnknown:
            featureTypeStr = "Unknown";
            break;
        case FeatureTypeDBowORB:
            featureTypeStr = "DBoW ORB";
            break;
        case FeatureTypeVSlamMarker:
            featureTypeStr = "VSLAM Marker";
            break;
        case FeatureTypeSnapshot:
            featureTypeStr = "Snapshot";
            break;
        case FeatureTypeVSlamKeyframe:
            featureTypeStr = "VSLAM Keyframe";
            break;
        case FeatureTypeCartographer:
            featureTypeStr = "Cartographer";
            break;
        default:
            featureTypeStr = "Unknown (" + std::to_string(featureType) + ")";
            break;
    }

    std::cout << "  Feature Type: " << featureTypeStr << " (" << featureType << ")" << std::endl;

    // Get feature observations
    const auto& featureObs = layer->featureObs();
    std::cout << "  Number of feature observations: " << featureObs.size() << std::endl;
}

int main(int argc, char** argv) {
    if (argc < 2) {
        std::cout << "Usage: " << argv[0] << " <stcm_file_path>" << std::endl;
        std::cout << "Example: " << argv[0] << " /path/to/map.stcm" << std::endl;
        return 1;
    }
    
    std::string stcm_file = argv[1];
    
    std::cout << "STCM File Reader Test" << std::endl;
    std::cout << "====================" << std::endl;
    std::cout << "File: " << stcm_file << std::endl;
    
    try {
        // Create CompositeMapReader
        CompositeMapReader reader;
        
        // Load STCM file
        std::cout << "\nLoading STCM file..." << std::endl;
        auto compositeMap = reader.loadFile(stcm_file);
        
        if (!compositeMap) {
            std::cerr << "Failed to load STCM file: " << stcm_file << std::endl;
            return 1;
        }
        
        std::cout << "STCM file loaded successfully!" << std::endl;
        
        // Print composite map metadata
        std::cout << "\n=== Composite Map Information ===" << std::endl;
        const auto& metadata = compositeMap->metadata();
        const auto& metaDict = metadata.dict();
        
        std::cout << "Composite Map Metadata:" << std::endl;
        for (const auto& pair : metaDict) {
            std::cout << "  " << pair.first << ": " << pair.second << std::endl;
        }
        
        // Get all layers
        const auto& layers = compositeMap->maps();
        std::cout << "\nTotal number of layers: " << layers.size() << std::endl;
        
        // Iterate through all layers
        for (size_t i = 0; i < layers.size(); ++i) {
            printLayerInfo(layers[i], static_cast<int>(i));
        }
        
        std::cout << "\n=== Summary ===" << std::endl;
        std::cout << "Successfully processed " << layers.size() << " layers from " << stcm_file << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
