#include "stcm_converter/pbstream_to_maplayer_converter.h"

#include <fstream>
#include <filesystem>
#include <iostream>

#include <stcm/composite_map.h>
#include <stcm/composite_map_writer.h>

namespace fs = std::filesystem;

namespace rslamware { namespace stcm_manager {

PbstreamToMapLayerConverter::PbstreamToMapLayerConverter()
{
}

PbstreamToMapLayerConverter::~PbstreamToMapLayerConverter()
{
}

std::shared_ptr<rpos::stcm::ImageFeaturesMapLayer> PbstreamToMapLayerConverter::convertPbstreamToMapLayer(
    const std::string& pbstream_file_path,
    const std::string& layer_name)
{
    try {
        // Read pbstream file
        std::vector<uint8_t> pbstream_data;
        if (!readPbstreamFile(pbstream_file_path, pbstream_data)) {
            return nullptr;
        }

        // Create ImageFeatureMapLayer from pbstream data
        return createMapLayerFromPbstream(pbstream_data, layer_name); 

    } catch (const std::exception& e) {
        setError("Exception during conversion: " + std::string(e.what()));
        return nullptr;
    }
}

std::shared_ptr<rpos::stcm::ImageFeaturesMapLayer> PbstreamToMapLayerConverter::createMapLayerFromPbstream(
    const std::vector<uint8_t>& pbstream_data,
    const std::string& layer_name)
{
    try {
        // Create ImageFeaturesMapLayer
        auto map_layer = std::make_shared<rpos::stcm::ImageFeaturesMapLayer>();
        
        // Set feature type to Cartographer
        map_layer->setFeatureType(rpos::stcm::FeatureTypeCartographer);
        
        // Set layer name and usage
        map_layer->setName(layer_name);
        map_layer->setUsage(RPOS_COMPOSITEMAP_USAGE_CARTOGRAPHER);  // Use standard usage for image features
        map_layer->setType(rpos::stcm::ImageFeaturesMapLayer::Type);

        // Create a single ImageFeaturesObservation to hold the pbstream data
        rpos::stcm::ImageFeaturesObservation observation;
        observation.ID = 0;  // ID is empty as requested
        // cameraPose is already initialized to default (empty) in constructor
        observation.features = pbstream_data;  // Store binary pbstream data

        // Add the observation to the map layer
        map_layer->featureObs().push_back(observation);

        return map_layer;

    } catch (const std::exception& e) {
        setError("Exception creating map layer: " + std::string(e.what()));
        return nullptr;
    }
}

bool PbstreamToMapLayerConverter::readPbstreamFile(const std::string& file_path, std::vector<uint8_t>& data)
{
    try {
        // Check if file exists
        if (!fs::exists(file_path)) {
            setError("Pbstream file not found: " + file_path);
            return false;
        }

        // Check if it's a regular file
        if (!fs::is_regular_file(file_path)) {
            setError("Path is not a regular file: " + file_path);
            return false;
        }

        // Get file size
        auto file_size = fs::file_size(file_path);
        if (file_size == 0) {
            setError("Pbstream file is empty: " + file_path);
            return false;
        }

        // Read file
        std::ifstream file(file_path, std::ios::binary);
        if (!file.is_open()) {
            setError("Failed to open pbstream file: " + file_path);
            return false;
        }

        // Resize vector and read data
        data.resize(file_size);
        file.read(reinterpret_cast<char*>(data.data()), file_size);
        
        if (!file.good() && !file.eof()) {
            setError("Error reading pbstream file: " + file_path);
            return false;
        }

        file.close();
        return true;

    } catch (const std::exception& e) {
        setError("Exception reading pbstream file: " + std::string(e.what()));
        return false;
    }
}

bool PbstreamToMapLayerConverter::validatePbstreamFile(const std::string& file_path)
{
    try {
        // Basic file existence and format check
        if (!fs::exists(file_path)) {
            setError("File not found: " + file_path);
            return false;
        }

        if (!fs::is_regular_file(file_path)) {
            setError("Not a regular file: " + file_path);
            return false;
        }

        // Check file extension
        std::string extension = fs::path(file_path).extension().string();
        if (extension != ".pbstream") {
            setError("File does not have .pbstream extension: " + file_path);
            return false;
        }

        // Check if file is not empty
        auto file_size = fs::file_size(file_path);
        if (file_size == 0) {
            setError("File is empty: " + file_path);
            return false;
        }

        // Basic pbstream magic number check
        std::ifstream file(file_path, std::ios::binary);
        if (!file.is_open()) {
            setError("Cannot open file for validation: " + file_path);
            return false;
        }

        // Read first 8 bytes to check magic number
        uint64_t magic = 0;
        file.read(reinterpret_cast<char*>(&magic), sizeof(magic));
        file.close();

        // Cartographer pbstream magic number (0x7b1d1f7b1d1f7b1d)
        const uint64_t expected_magic = 0x7b1d1f7b1d1f7b1dULL;
        if (magic != expected_magic) {
            setError("Invalid pbstream magic number in file: " + file_path);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError("Exception validating pbstream file: " + std::string(e.what()));
        return false;
    }
}

void PbstreamToMapLayerConverter::setError(const std::string& error)
{
    last_error_ = error;
    std::cerr << "PbstreamToMapLayerConverter Error: " << error << std::endl;
}

}} // namespace stcm_manager
