cmake_minimum_required(VERSION 3.8)
project(ascamera)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic -O3)
endif()

add_compile_options(-std=c++17)

# Determine target architecture
if (CROSS_COMPILE)
    # Use cross-compile target if specified
    SET(ARCH_TARGET ${CROSS_COMPILE})
else()
    # Auto-detect architecture based on CMAKE_SYSTEM_PROCESSOR
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm64")
        SET(ARCH_TARGET "aarch64-linux-gnu")
    elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "armv7l|arm")
        SET(ARCH_TARGET "arm-linux-gnueabihf")
    elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|amd64")
        SET(ARCH_TARGET "x86_64-linux-gnu")
    elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "i386|i686")
        SET(ARCH_TARGET "i386-linux-gnu")
    else()
        # Fallback: try to detect using uname if available
        execute_process(
            COMMAND uname -m
            OUTPUT_VARIABLE UNAME_MACHINE
            OUTPUT_STRIP_TRAILING_WHITESPACE
            ERROR_QUIET
        )
        if(UNAME_MACHINE MATCHES "aarch64|arm64")
            SET(ARCH_TARGET "aarch64-linux-gnu")
        elseif(UNAME_MACHINE MATCHES "armv7l|arm")
            SET(ARCH_TARGET "arm-linux-gnueabihf")
        elseif(UNAME_MACHINE MATCHES "x86_64|amd64")
            SET(ARCH_TARGET "x86_64-linux-gnu")
        else()
            # Default fallback
            SET(ARCH_TARGET "x86_64-linux-gnu")
            message(WARNING "Unknown architecture: ${CMAKE_SYSTEM_PROCESSOR}, using default: ${ARCH_TARGET}")
        endif()
    endif()
endif()

message(STATUS "Target architecture: ${ARCH_TARGET}")


# find dependencies
find_package(ament_cmake REQUIRED)
# uncomment the following section in order to fill in
# further dependencies manually.
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
# find_package(tf2_geometry_msgs REQUIRED)

include_directories(
  ${catkin_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_SOURCE_DIR}/libs/include
)

link_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/libs/lib/${ARCH_TARGET}
)
add_executable(
  ascamera_node 
  src/ascamera_node.cpp 
  src/CameraPublisher.cpp 
  src/CameraSrv.cpp 
  src/Camera.cpp
  src/TfTreeFrameIdInfo.cpp
  )
ament_target_dependencies(ascamera_node rclcpp std_msgs sensor_msgs geometry_msgs tf2 tf2_ros)

target_include_directories(ascamera_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(ascamera_node PUBLIC c_std_99 cxx_std_17)  # Require C99 and C++17

## Specify libraries to link a library or executable target against
target_link_libraries(ascamera_node
  AngstrongCameraSdk
  zmq
  Filt
  asuvc
  asusb
  turbojpeg
  alg_kunlun
  jpeg
  AngKondyorArith
)

install(TARGETS ascamera_node
  DESTINATION lib/${PROJECT_NAME})

install(DIRECTORY 
  launch
  configurationfiles
  DESTINATION share/${PROJECT_NAME})

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
