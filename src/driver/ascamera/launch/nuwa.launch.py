from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():
    pkg_dir = get_package_share_directory('ascamera')
    config_path = os.path.join(pkg_dir, 'configurationfiles')

    ascamera_node = Node(
        namespace= "ascamera_nuwa",
        package='ascamera',
        executable='ascamera_node',
        output='both',
        parameters=[
            {"usb_bus_no": 2},
            {"usb_path": "1.3"},
            {"confiPath": config_path},
            {"depth_width": 640},
            {"depth_height": 400},
            {"fps": 15},
        ],
        prefix="taskset -c 4-7",
        remappings=[]
    )

    return LaunchDescription([ascamera_node])

