#include "sl_vcu_all/led_display_control_node.hpp"
#include <algorithm>
#include <sstream>
#include <cmath>
#include <iostream>
#include <thread>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace sl_vcu_all
{

    static bool debug_mode = false;

// SK6812Color implementation
void SK6812Color::getScaledGRB(uint8_t& scaled_g, uint8_t& scaled_r, uint8_t& scaled_b) const
{
    scaled_g = (green * green_brightness) / 255;
    scaled_r = (red * red_brightness) / 255;
    scaled_b = (blue * blue_brightness) / 255;
}

// SK6812SpiDriver implementation
SK6812SpiDriver::SK6812SpiDriver(const std::string& spi_device, uint32_t spi_speed)
    : spi_device_(spi_device), spi_speed_(spi_speed), spi_fd_(-1)
{
}

SK6812SpiDriver::~SK6812SpiDriver()
{
    close();
}

bool SK6812SpiDriver::initialize()
{
    std::lock_guard<std::mutex> lock(spi_mutex_);
    
    if (spi_fd_ >= 0) {
        return true;
    }

    spi_fd_ = open(spi_device_.c_str(), O_RDWR);
    if (spi_fd_ < 0) {
        std::cerr << "Failed to open SPI device: " << spi_device_ << std::endl;
        return false;
    }

    uint8_t mode = SPI_MODE_0;
    if (ioctl(spi_fd_, SPI_IOC_WR_MODE, &mode) < 0) {
        std::cerr << "Failed to set SPI mode" << std::endl;
        ::close(spi_fd_);
        spi_fd_ = -1;
        return false;
    }

    uint8_t bits = 8;
    if (ioctl(spi_fd_, SPI_IOC_WR_BITS_PER_WORD, &bits) < 0) {
        std::cerr << "Failed to set SPI bits per word" << std::endl;
        ::close(spi_fd_);
        spi_fd_ = -1;
        return false;
    }

    if (ioctl(spi_fd_, SPI_IOC_WR_MAX_SPEED_HZ, &spi_speed_) < 0) {
        std::cerr << "Failed to set SPI speed" << std::endl;
        ::close(spi_fd_);
        spi_fd_ = -1;
        return false;
    }

    std::cout << "SK6812 SPI driver initialized on " << spi_device_ 
              << " at " << spi_speed_ << " Hz" << std::endl;
    return true;
}

void SK6812SpiDriver::close()
{
    std::lock_guard<std::mutex> lock(spi_mutex_);
    
    if (spi_fd_ >= 0) {
        ::close(spi_fd_);
        spi_fd_ = -1;
    }
}

bool SK6812SpiDriver::sendColors(const std::vector<SK6812Color>& colors)
{
    if (colors.empty()) {
        return true;
    }

    std::vector<uint8_t> grb_data;
    grb_data.reserve(colors.size() * 3);

    for (size_t idx = 0; idx < colors.size(); idx++) {
        const auto& color = colors[idx];
        uint8_t g, r, b;
        color.getScaledGRB(g, r, b);
        grb_data.push_back(g);
        grb_data.push_back(r);
        grb_data.push_back(b);

        // Debug: Log color being sent to hardware, with extra detail for first LED
        if (debug_mode)
        {
            std::cout << "[SK6812SpiDriver] Sending led " << idx << " color to hardware: G=" << static_cast<int>(colors[idx].green_brightness)
                  << " R=" << static_cast<int>(colors[idx].red_brightness)
                  << " B=" << static_cast<int>(colors[idx].blue_brightness) << std::endl;
        }
    }

    return sendRawGRB(grb_data.data(), colors.size());
}

bool SK6812SpiDriver::sendRawGRB(const uint8_t* grb_data, size_t num_leds)
{
    if (!isInitialized()) {
        std::cerr << "SPI driver not initialized" << std::endl;
        return false;
    }

    if (grb_data == nullptr || num_leds == 0) {
        return true;
    }  

    std::lock_guard<std::mutex> lock(spi_mutex_);

    std::vector<uint8_t> spi_data;
    size_t spi_data_size = convertToSpiData(grb_data, num_leds, spi_data);

    if (debug_mode)
    {
        std::cout << "[SK6812SpiDriver] send spi data size: " << spi_data_size << std::endl;
        for (size_t i = 0; i < spi_data_size; i++) {
            std::cout << "[SK6812SpiDriver] spi_data[" << std::dec << i << "]: " << std::hex << static_cast<int>(spi_data[i]) << std::endl;
        }

        std::cout << std::dec << std::endl;
    }

    struct spi_ioc_transfer transfer = {};
    transfer.tx_buf = reinterpret_cast<uintptr_t>(spi_data.data());
    transfer.len = spi_data_size;
    transfer.speed_hz = spi_speed_;
    transfer.bits_per_word = 8;

    if (ioctl(spi_fd_, SPI_IOC_MESSAGE(1), &transfer) < 0) {
        std::cerr << "Failed to send SPI data" << std::endl;
        return false;
    }

    return true;
}

bool SK6812SpiDriver::turnOffAll(size_t num_leds)
{
    if (!isInitialized()) {
        std::cerr << "SPI driver not initialized for turnOffAll" << std::endl;
        return false;
    }

    std::cout << "[SK6812SpiDriver] Turning off all " << num_leds << " LEDs with extended reset" << std::endl;

    std::lock_guard<std::mutex> lock(spi_mutex_);

    // Create extended SPI data with longer reset sequence for shutdown
    std::vector<uint8_t> spi_data;
    size_t extended_reset_bytes = RESET_BYTES * 3; // Triple the reset sequence
    size_t total_bytes = num_leds * 3 * 8 + 2 * extended_reset_bytes;
    spi_data.resize(total_bytes);

    size_t spi_index = 0;

    // Extended reset sequence - fill with zeros
    std::memset(&spi_data[spi_index], 0, extended_reset_bytes);
    spi_index += extended_reset_bytes;


    // Send all zeros for each LED (each bit becomes BIT_0_PATTERN)
    for (size_t led = 0; led < num_leds; led++) {
        for (size_t color_byte = 0; color_byte < 3; color_byte++) {
            // Convert 0x00 byte to SPI pattern (all BIT_0_PATTERN)
            byteToSpiPattern(0x00, &spi_data[spi_index]);
            spi_index += 8;
        }
    }

    // Extended reset sequence - fill with zeros
    std::memset(&spi_data[spi_index], 0, extended_reset_bytes);
    spi_index += extended_reset_bytes;

    struct spi_ioc_transfer transfer = {};
    transfer.tx_buf = reinterpret_cast<uintptr_t>(spi_data.data());
    transfer.len = spi_index;
    transfer.speed_hz = spi_speed_;
    transfer.bits_per_word = 8;

    if (ioctl(spi_fd_, SPI_IOC_MESSAGE(1), &transfer) < 0) {
        std::cerr << "Failed to send turnOffAll SPI data" << std::endl;
        return false;
    }

    std::cout << "[SK6812SpiDriver] TurnOffAll completed with " << spi_index << " bytes sent" << std::endl;
    return true;
}

void SK6812SpiDriver::byteToSpiPattern(uint8_t byte, uint8_t* output)
{
    for (int i = 7; i >= 0; i--) {
        if (byte & (1 << i)) {
            output[7 - i] = BIT_1_PATTERN;
        } else {
            output[7 - i] = BIT_0_PATTERN;
        }
    }
}

size_t SK6812SpiDriver::convertToSpiData(const uint8_t* grb_data, size_t num_leds, 
                                         std::vector<uint8_t>& spi_data)
{
    size_t total_bytes = num_leds * 3 * 8 + 2 * RESET_BYTES;
    spi_data.resize(total_bytes);

    size_t spi_index = 0;
    std::memset(&spi_data[spi_index], 0, RESET_BYTES);
    spi_index += RESET_BYTES;

    for (size_t led = 0; led < num_leds; led++) {
        for (size_t color_byte = 0; color_byte < 3; color_byte++) {
            uint8_t byte = grb_data[led * 3 + color_byte];
            byteToSpiPattern(byte, &spi_data[spi_index]);
            spi_index += 8;
        }
    }

    std::memset(&spi_data[spi_index], 0, RESET_BYTES);
    spi_index += RESET_BYTES;

    return spi_index;
}

// LedStripController implementation
LedStripController::LedStripController(uint8_t channel_id, size_t num_leds, size_t first_part_leds, 
                                     size_t second_part_leds, std::shared_ptr<SK6812SpiDriver> spi_driver)
    : channel_id_(channel_id), num_leds_(num_leds), first_part_leds_(first_part_leds), 
      second_part_leds_(second_part_leds), spi_driver_(spi_driver), effect_thread_running_(false)
{
    led_buffer_.resize(num_leds_);
    
    for (auto& led : led_buffer_) {
        led = SK6812Color(0, 0, 0, 0, 0, 0);
    }
}

LedStripController::~LedStripController()
{
    stopEffectThread();
    // Turn off LEDs only if SPI driver is still available
    if (spi_driver_ && spi_driver_->isInitialized()) {
        //turnOffAll();
    }
}

bool LedStripController::setLightingMode(StripPart part, LightingMode mode, const EffectParams& params, bool send_to_hardware)
{
    std::lock_guard<std::mutex> lock(state_mutex_);

    size_t part_index = static_cast<size_t>(part);
    PartState& state = part_states_[part_index];

    state.mode = mode;
    state.params = params;
    state.start_time = std::chrono::steady_clock::now();
    state.last_update = state.start_time;
    state.phase = 0.0f;
    state.marquee_position = 0;
    state.active = (mode != LightingMode::OFF);

    // Part priority logic: When setting WHOLE strip, deactivate individual parts
    if (part == StripPart::WHOLE && state.active) {
        part_states_[static_cast<size_t>(StripPart::FIRST_HALF)].active = false;
        part_states_[static_cast<size_t>(StripPart::SECOND_HALF)].active = false;

        if (debug_mode) {
            std::cout << "[LedStripController] Setting WHOLE strip - deactivating individual parts" << std::endl;
        }
    }
    // When setting individual parts, deactivate WHOLE strip if it was active
    else if ((part == StripPart::FIRST_HALF || part == StripPart::SECOND_HALF) && state.active) {
        part_states_[static_cast<size_t>(StripPart::WHOLE)].active = false;

        if (debug_mode) {
            std::cout << "[LedStripController] Setting individual part - deactivating WHOLE strip" << std::endl;
        }
    }

    if (mode == LightingMode::ON || mode == LightingMode::OFF) {
        size_t start_led = 0, end_led = 0;
        getPartRange(part, start_led, end_led);

        if (debug_mode)
        {
            std::cout << "[LedStripController] Setting lighting mode for part " << static_cast<int>(part)
                      << ", mode: " << static_cast<int>(mode)
                      << ", start_led: " << start_led
                      << ", end_led: " << end_led
                      << ", color: G=" << static_cast<int>(params.color.green_brightness)
                      << " R=" << static_cast<int>(params.color.red_brightness)
                      << " B=" << static_cast<int>(params.color.blue_brightness) << std::endl;
        }

        if (mode == LightingMode::ON) {
            setSolidColor(start_led, end_led, params.color);
        } else {
            clearRange(start_led, end_led);
        }

        if (send_to_hardware) {
            sendToHardware();
        }
    }

    return true;
}

bool LedStripController::turnOff(StripPart part, bool send_to_hardware)
{
    EffectParams params;
    return setLightingMode(part, LightingMode::OFF, params, send_to_hardware);
}

bool LedStripController::turnOffAll()
{
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    std::cout << "[LedStripController] Turning off all " << led_buffer_.size() << " LEDs with multiple attempts" << std::endl;

    for (auto& led : led_buffer_) {
        led = SK6812Color(0, 0, 0, 0, 0, 0);
    }

    {
        std::lock_guard<std::mutex> state_lock(state_mutex_);
        for (auto& state : part_states_) {
            state.active = false;
            state.mode = LightingMode::OFF;
        }
    }

    // Use the normal sendToHardware method which properly encodes the black colors
    bool result = sendToHardware();
    
    return result;
}

void LedStripController::getPartRange(StripPart part, size_t& start_led, size_t& end_led) const
{
    switch (part) {
        case StripPart::WHOLE:
            start_led = 0;
            end_led = num_leds_;
            break;
            
        case StripPart::FIRST_HALF:
            start_led = 0;
            end_led = first_part_leds_;
            break;
            
        case StripPart::SECOND_HALF:
            start_led = first_part_leds_;
            end_led = first_part_leds_ + second_part_leds_;
            break;
    }
}

bool LedStripController::isActive() const
{
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    for (const auto& state : part_states_) {
        if (state.active) return true;
    }
    
    return false;
}

void LedStripController::startEffectThread()
{
    if (effect_thread_running_) return;
    
    effect_thread_running_ = true;
    effect_thread_ = std::thread(&LedStripController::effectThreadFunction, this);
}

void LedStripController::stopEffectThread()
{
    if (!effect_thread_running_) return;
    
    effect_thread_running_ = false;
    
    if (effect_thread_.joinable()) {
        effect_thread_.join();
    }
}

bool LedStripController::sendToHardware()
{
    if (!spi_driver_ || !spi_driver_->isInitialized()) {
        std::cout << "[LedStripController] Cannot send to hardware: SPI driver not initialized" << std::endl;
        return false;
    }

    if (debug_mode)
    {
        std::cout << "[LedStripController] Sending " << led_buffer_.size() << " LED colors to hardware" << std::endl;
    }
    
    return spi_driver_->sendColors(led_buffer_);
}

bool LedStripController::sendChannelToHardware()
{
    // This method sends the entire channel data to hardware
    // It combines all parts data and sends it as one unit
    if (debug_mode)
    {
        std::cout << "[LedStripController] Sending entire channel data to hardware (channel-based communication)" << std::endl;
    }
    return sendToHardware();
}

void LedStripController::setSolidColor(size_t start_led, size_t end_led, const SK6812Color& color)
{
    if (debug_mode)
    {
        std::cout << "[LedStripController] Setting solid color for LEDs " << start_led
                  << " to " << end_led << ", color: G=" << static_cast<int>(color.green_brightness)
                  << " R=" << static_cast<int>(color.red_brightness)
                  << " B=" << static_cast<int>(color.blue_brightness) << std::endl;
    }

    for (size_t i = start_led; i < end_led && i < num_leds_; i++) {
        led_buffer_[i] = color;
    }
}

void LedStripController::clearRange(size_t start_led, size_t end_led)
{
    for (size_t i = start_led; i < end_led && i < num_leds_; i++) {
        led_buffer_[i] = SK6812Color(0, 0, 0, 0, 0, 0);
    }
}

void LedStripController::effectThreadFunction()
{
    const auto update_interval = std::chrono::milliseconds(1000 / UPDATE_RATE_HZ);

    while (effect_thread_running_) {
        auto start_time = std::chrono::steady_clock::now();

        update();

        auto end_time = std::chrono::steady_clock::now();
        auto elapsed = end_time - start_time;

        if (elapsed < update_interval) {
            std::this_thread::sleep_for(update_interval - elapsed);
        }
    }
}

void LedStripController::update()
{
    std::lock_guard<std::mutex> state_lock(state_mutex_);
    std::lock_guard<std::mutex> buffer_lock(buffer_mutex_);

    auto now = std::chrono::steady_clock::now();
    bool needs_update = false;

    for (size_t i = 0; i < 3; i++) {
        PartState& state = part_states_[i];

        if (!state.active) continue;

        size_t start_led = 0, end_led = 0;
        getPartRange(static_cast<StripPart>(i), start_led, end_led);

        switch (state.mode) {
            case LightingMode::BREATHING:
                updateBreathingEffect(state, start_led, end_led);
                needs_update = true;
                break;

            case LightingMode::FLASHING:
                updateFlashingEffect(state, start_led, end_led);
                needs_update = true;
                break;

            case LightingMode::MARQUEE:
                updateMarqueeEffect(state, start_led, end_led);
                needs_update = true;
                break;

            default:
                break;
        }

        state.last_update = now;
    }

    if (needs_update) {
        sendToHardware();
    }
}

void LedStripController::updateBreathingEffect(PartState& state, size_t start_led, size_t end_led)
{
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration<float>(now - state.start_time).count();

    float phase = fmod(elapsed * state.params.frequency, 1.0f);
    float brightness_factor = (sin(phase * 2.0f * M_PI) + 1.0f) * 0.5f;

    SK6812Color breathing_color = state.params.color;
    breathing_color.green_brightness = static_cast<uint8_t>(breathing_color.green_brightness * brightness_factor);
    breathing_color.red_brightness = static_cast<uint8_t>(breathing_color.red_brightness * brightness_factor);
    breathing_color.blue_brightness = static_cast<uint8_t>(breathing_color.blue_brightness * brightness_factor);

    setSolidColor(start_led, end_led, breathing_color);
}

void LedStripController::updateFlashingEffect(PartState& state, size_t start_led, size_t end_led)
{
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration<float>(now - state.start_time).count();

    float phase = fmod(elapsed * state.params.frequency, 1.0f);

    if (phase < state.params.on_time_duty) {
        setSolidColor(start_led, end_led, state.params.color);
    } else {
        clearRange(start_led, end_led);
    }
}

void LedStripController::updateMarqueeEffect(PartState& state, size_t start_led, size_t end_led)
{
    auto now = std::chrono::steady_clock::now();
    auto dt = std::chrono::duration<float>(now - state.last_update).count();

    size_t range_size = end_led - start_led;
    if (range_size == 0) return;

    // Use floating point position for smooth movement
    float movement = state.params.speed * dt;

    // Update the floating point phase for smooth movement
    if (state.params.marquee_direction) {
        state.phase += movement;
        // Keep phase in range [0, range_size)
        while (state.phase >= static_cast<float>(range_size)) {
            state.phase -= static_cast<float>(range_size);
        }
    } else {
        state.phase -= movement;
        // Keep phase in range [0, range_size)
        while (state.phase < 0.0f) {
            state.phase += static_cast<float>(range_size);
        }
    }

    // Convert floating point position to integer position
    state.marquee_position = static_cast<size_t>(state.phase) % range_size;

    clearRange(start_led, end_led);

    // Marquee with trail effect - light up LEDs based on movement distance
    size_t current_position = state.marquee_position;

#if 0
    size_t movement_distance = static_cast<size_t>(movement + 0.5f); // Round to nearest integer

    if (movement_distance == 0) {
        movement_distance = 1; // Always light at least current position
    }

    // Light up trail based on direction and movement distance
    for (size_t i = 0; i < movement_distance && i < range_size; i++) {
        size_t led_pos;

        if (state.params.marquee_direction) {
            // Forward direction: light from (current - distance + 1) to current
            led_pos = (current_position + range_size - movement_distance + 1 + i) % range_size;
        } else {
            // Reverse direction: light from current to (current + distance - 1)
            led_pos = (current_position + i) % range_size;
        }

        size_t led_index = start_led + led_pos;
        if (led_index < end_led) {
            led_buffer_[led_index] = state.params.color;
        }
    }
#else
    if (state.params.marquee_direction) {
        for (size_t i = 0; i < current_position; i++)
        {
            led_buffer_[start_led+i] = state.params.color;
        }
        
    } 
    else 
    {
        for (size_t i = 0; i < range_size - current_position; i++)
        {
            led_buffer_[start_led+current_position+i] = state.params.color;
        }
    }

#endif
}

// LedDisplayControlNode implementation
LedDisplayControlNode::LedDisplayControlNode(const rclcpp::NodeOptions & options)
    : Node("led_display_control_node", options), initialized_(false), shutdown_called_(false)
{
    RCLCPP_INFO(this->get_logger(), "Initializing LED Display Control Node");

    declareParameters();
    initializeParameters();

    if (!initializeLedStrips()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize LED strips");
        return;
    }

    led_control_service_ = this->create_service<srv::LedControl>(
        "led_control",
        std::bind(&LedDisplayControlNode::ledControlServiceCallback, this,
                  std::placeholders::_1, std::placeholders::_2));

    param_callback_handle_ = this->add_on_set_parameters_callback(
        std::bind(&LedDisplayControlNode::parametersCallback, this, std::placeholders::_1));

    initialized_ = true;
    last_command_time_ = std::chrono::steady_clock::now();

    // Set all channels to full white on startup
    setStartupLighting();

    RCLCPP_INFO(this->get_logger(), "LED Display Control Node initialized with %d channels", num_channels_);
}

LedDisplayControlNode::~LedDisplayControlNode()
{
    // Shutdown will be skipped if already called by signal handler
    shutdownLedStrips();
}

void LedDisplayControlNode::declareParameters()
{
    this->declare_parameter("num_channels", 2);
    this->declare_parameter("auto_start_effects", true);
    this->declare_parameter("default_frequency", 1.0);
    this->declare_parameter("default_speed", 10.0);
    this->declare_parameter("default_marquee_direction", true);
    this->declare_parameter("default_green_brightness", 255);
    this->declare_parameter("default_red_brightness", 255);
    this->declare_parameter("default_blue_brightness", 255);
    this->declare_parameter("default_on_time_duty", 0.5);
    this->declare_parameter("debug_mode", false);
}

void LedDisplayControlNode::initializeParameters()
{
    num_channels_ = this->get_parameter("num_channels").as_int();
    auto_start_effects_ = this->get_parameter("auto_start_effects").as_bool();

    default_params_.default_frequency = this->get_parameter("default_frequency").as_double();
    default_params_.default_speed = this->get_parameter("default_speed").as_double();
    default_params_.default_marquee_direction = this->get_parameter("default_marquee_direction").as_bool();
    default_params_.default_green_brightness = this->get_parameter("default_green_brightness").as_int();
    default_params_.default_red_brightness = this->get_parameter("default_red_brightness").as_int();
    default_params_.default_blue_brightness = this->get_parameter("default_blue_brightness").as_int();
    default_params_.default_on_time_duty = this->get_parameter("default_on_time_duty").as_double();
    debug_mode = this->get_parameter("debug_mode").as_bool();

    for (uint8_t i = 0; i < num_channels_; ++i) {
        ChannelConfig config;
        std::string prefix = "channel_" + std::to_string(i) + ".";

        this->declare_parameter(prefix + "spi_device", "/dev/spidev" + std::to_string(i) + ".0");
        this->declare_parameter(prefix + "spi_speed", 6400000);
        this->declare_parameter(prefix + "num_leds", 50);
        this->declare_parameter(prefix + "first_part_leds", 25);
        this->declare_parameter(prefix + "second_part_leds", 25);
        this->declare_parameter(prefix + "enabled", true);

        config.spi_device = this->get_parameter(prefix + "spi_device").as_string();
        config.spi_speed = this->get_parameter(prefix + "spi_speed").as_int();
        config.num_leds = this->get_parameter(prefix + "num_leds").as_int();
        config.first_part_leds = this->get_parameter(prefix + "first_part_leds").as_int();
        config.second_part_leds = this->get_parameter(prefix + "second_part_leds").as_int();
        config.enabled = this->get_parameter(prefix + "enabled").as_bool();

        channel_configs_[i] = config;

        RCLCPP_INFO(this->get_logger(), "Channel %d: device=%s, speed=%d, leds=%zu, first_part=%zu, second_part=%zu, enabled=%s",
                    i, config.spi_device.c_str(), config.spi_speed, config.num_leds,
                    config.first_part_leds, config.second_part_leds, config.enabled ? "true" : "false");
    }
}

bool LedDisplayControlNode::initializeLedStrips()
{
    std::lock_guard<std::mutex> lock(controllers_mutex_);

    for (const auto& [channel_id, config] : channel_configs_) {
        if (!config.enabled) {
            RCLCPP_INFO(this->get_logger(), "Channel %d disabled, skipping", channel_id);
            continue;
        }

        auto spi_driver = std::make_shared<SK6812SpiDriver>(config.spi_device, config.spi_speed);
        if (!spi_driver->initialize()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to initialize SPI driver for channel %d", channel_id);
            continue;
        }

        auto controller = std::make_shared<LedStripController>(
            channel_id, config.num_leds, config.first_part_leds, config.second_part_leds, spi_driver);

        spi_drivers_[channel_id] = spi_driver;
        led_controllers_[channel_id] = controller;

        RCLCPP_INFO(this->get_logger(), "Initialized LED strip channel %d with %zu LEDs (%zu + %zu)",
                    channel_id, config.num_leds, config.first_part_leds, config.second_part_leds);
    }

    return !led_controllers_.empty();
}

void LedDisplayControlNode::shutdownLedStrips()
{
    std::lock_guard<std::mutex> lock(controllers_mutex_);

    if (shutdown_called_) {
        RCLCPP_DEBUG(this->get_logger(), "Shutdown already called, skipping...");
        return;
    }

    shutdown_called_ = true;
    RCLCPP_INFO(this->get_logger(), "Shutting down LED strips - checking enabled channels and turning off LEDs...");

    // Log current channel configuration status
    RCLCPP_INFO(this->get_logger(), "Channel configuration status:");
    for (const auto& [channel_id, config] : channel_configs_) {
        RCLCPP_INFO(this->get_logger(), "  Channel %d: enabled=%s, device=%s",
                    channel_id, config.enabled ? "true" : "false", config.spi_device.c_str());
    }

    // Only turn off LEDs for enabled channels
    for (auto& [channel_id, controller] : led_controllers_) {
        // Check if this channel is enabled in the configuration
        auto config_it = channel_configs_.find(channel_id);
        if (config_it != channel_configs_.end() && config_it->second.enabled) {
            RCLCPP_INFO(this->get_logger(), "Channel %d is enabled - stopping effects and turning off all LEDs", channel_id);
            controller->stopEffectThread();
            controller->turnOffAll();
        } else {
            RCLCPP_INFO(this->get_logger(), "Channel %d is disabled - skipping LED shutdown", channel_id);
            // Still stop effect threads for disabled channels to clean up properly
            controller->stopEffectThread();
        }
    }

    // Add a small delay to ensure SPI communication completes
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    led_controllers_.clear();
    spi_drivers_.clear();

    RCLCPP_INFO(this->get_logger(), "LED strips shut down");
}

void LedDisplayControlNode::setStartupLighting()
{
    RCLCPP_INFO(this->get_logger(), "Setting startup lighting - all channels to full white...");

    std::lock_guard<std::mutex> lock(controllers_mutex_);

    // Set all channels to full white (all color brightness = 255)
    for (auto& [channel_id, controller] : led_controllers_) {
        if (controller) {
            EffectParams params;
            params.color = SK6812Color(255, 255, 255, 255, 255, 255);  // Full white

            // Set whole strip (part 0) to solid white
            RCLCPP_DEBUG(this->get_logger(), "Setting channel %d to full white (startup lighting)", channel_id);
            bool success = controller->setLightingMode(StripPart::WHOLE, LightingMode::ON, params, true);

            if (success) {
                RCLCPP_INFO(this->get_logger(), "Channel %d set to full white (startup complete)", channel_id);
            } else {
                RCLCPP_WARN(this->get_logger(), "Failed to set channel %d to full white", channel_id);
            }
        }
    }
}

LightingMode LedDisplayControlNode::stringToLightingMode(const std::string& mode_str)
{
    if (mode_str == "off") return LightingMode::OFF;
    if (mode_str == "on") return LightingMode::ON;
    if (mode_str == "breathing") return LightingMode::BREATHING;
    if (mode_str == "flashing") return LightingMode::FLASHING;
    if (mode_str == "marquee") return LightingMode::MARQUEE;

    return LightingMode::OFF;
}

std::string LedDisplayControlNode::lightingModeToString(LightingMode mode)
{
    switch (mode) {
        case LightingMode::OFF: return "off";
        case LightingMode::ON: return "on";
        case LightingMode::BREATHING: return "breathing";
        case LightingMode::FLASHING: return "flashing";
        case LightingMode::MARQUEE: return "marquee";
        default: return "unknown";
    }
}

bool LedDisplayControlNode::validateChannels(const std::vector<uint8_t>& channels, std::string& error_msg)
{
    if (channels.empty()) {
        error_msg = "No channels specified";
        return false;
    }

    for (uint8_t channel : channels) {
        if (channel_configs_.find(channel) == channel_configs_.end()) {
            error_msg = "Invalid channel: " + std::to_string(channel);
            return false;
        }

        if (!channel_configs_[channel].enabled) {
            error_msg = "Channel " + std::to_string(channel) + " is disabled";
            return false;
        }
    }

    return true;
}

rcl_interfaces::msg::SetParametersResult LedDisplayControlNode::parametersCallback(
    const std::vector<rclcpp::Parameter> & parameters)
{
    rcl_interfaces::msg::SetParametersResult result;
    result.successful = true;
    result.reason = "";

    for (const auto & param : parameters) {
        RCLCPP_INFO(this->get_logger(), "Parameter changed: %s", param.get_name().c_str());
    }

    return result;
}

void LedDisplayControlNode::ledControlServiceCallback(
    const std::shared_ptr<srv::LedControl::Request> request,
    std::shared_ptr<srv::LedControl::Response> response)
{
    std::lock_guard<std::mutex> lock(controllers_mutex_);

    response->success = false;
    response->message = "Unknown error";
    response->affected_channels.clear();

    // Validate channels
    std::string error_msg;
    if (!validateChannels(request->channels, error_msg)) {
        response->message = error_msg;
        return;
    }

    // Validate channel_data array size
    if (request->sync_channels) {
        if (request->channel_data.size() < 1) {
            response->message = "When sync_channels=true, at least 1 channel_data entry is required";
            return;
        }
        // Extra entries are ignored when sync_channels=true
    } else {
        if (request->channel_data.size() != request->channels.size()) {
            response->message = "channel_data array size must match channels array size when sync_channels=false";
            return;
        }
    }

    std::vector<uint8_t> successful_channels;
    std::vector<std::string> errors;

    // Structure to hold channel processing data for sync mode
    struct ChannelProcessData {
        uint8_t channel_id;
        std::shared_ptr<LedStripController> controller;
        std::vector<std::pair<StripPart, LightingMode>> part_modes;
        std::vector<std::pair<StripPart, EffectParams>> part_params;
    };
    std::vector<ChannelProcessData> channels_to_process;

    // Determine which channels to process
    std::vector<uint8_t> channels_to_apply;
    if (request->sync_channels) {
        // When sync_channels=true, apply to all available channels
        for (const auto& [channel_id, controller] : led_controllers_) {
            channels_to_apply.push_back(channel_id);
        }
    } else {
        // When sync_channels=false, only apply to requested channels
        channels_to_apply = request->channels;
    }

    // Process each channel
    for (size_t i = 0; i < channels_to_apply.size(); ++i) {
        uint8_t channel = channels_to_apply[i];

        // Determine which channel_data to use
        size_t channel_data_index = request->sync_channels ? 0 :
            (std::find(request->channels.begin(), request->channels.end(), channel) - request->channels.begin());

        // For sync mode, always use first channel_data
        // For non-sync mode, use the corresponding index from the original request
        if (request->sync_channels) {
            channel_data_index = 0;
        } else {
            // Find the index of this channel in the original request
            auto it = std::find(request->channels.begin(), request->channels.end(), channel);
            if (it == request->channels.end()) {
                continue; // Skip if channel not in original request
            }
            channel_data_index = it - request->channels.begin();
        }

        const auto& channel_data = request->channel_data[channel_data_index];

        auto it = led_controllers_.find(channel);
        if (it == led_controllers_.end()) {
            errors.push_back("Channel " + std::to_string(channel) + " not found");
            continue;
        }

        bool channel_success = true;

        // Validate parts_data array size for this channel
        if (channel_data.sync_parts) {
            if (channel_data.parts_data.size() < 1) {
                errors.push_back("When sync_parts=true, at least 1 parts_data entry is required for channel " + std::to_string(channel));
                channel_success = false;
                continue;
            }
            // Extra entries are ignored when sync_parts=true
        } else {
            if (channel_data.parts_data.size() != channel_data.parts.size()) {
                errors.push_back("parts_data array size must match parts array size when sync_parts=false for channel " + std::to_string(channel));
                channel_success = false;
                continue;
            }
        }

        // Prepare channel processing data
        ChannelProcessData channel_proc_data;
        channel_proc_data.channel_id = channel;
        channel_proc_data.controller = it->second;

        // Process each part in the channel
        for (size_t part_idx = 0; part_idx < channel_data.parts.size(); ++part_idx) {
            uint8_t part_id = channel_data.parts[part_idx];

            // Validate part ID
            if (part_id > 2) {
                errors.push_back("Invalid part ID " + std::to_string(part_id) + " for channel " + std::to_string(channel));
                channel_success = false;
                continue;
            }

            // Determine which part_data to use
            size_t part_data_index;
            if (channel_data.sync_parts) {
                part_data_index = 0; // Use first part data for all parts
            } else {
                part_data_index = part_idx; // Use corresponding part data
            }

            // Validate part_data array bounds
            if (part_data_index >= channel_data.parts_data.size()) {
                errors.push_back("Insufficient parts_data for channel " + std::to_string(channel) + " part " + std::to_string(part_id));
                channel_success = false;
                continue;
            }

            const auto& part_data = channel_data.parts_data[part_data_index];

            // Convert to internal types
            LightingMode mode = stringToLightingMode(part_data.mode);
            if (mode == LightingMode::OFF && part_data.mode != "off") {
                errors.push_back("Invalid lighting mode: " + part_data.mode);
                channel_success = false;
                continue;
            }

            StripPart strip_part = static_cast<StripPart>(part_id);

            EffectParams params;
            // Apply default values for optional fields if not set
            if ((part_data.green_brightness == 0) && (part_data.red_brightness == 0) && (part_data.blue_brightness == 0)) {
                // If all color values are zero, use default color
                params.color = SK6812Color(
                    255, 255, 255,  // Full color values
                    default_params_.default_green_brightness,
                    default_params_.default_red_brightness,
                    default_params_.default_blue_brightness
                );
            } else {
                // Otherwise, use specified color values
                params.color = SK6812Color(
                    255, 255, 255,  // Full color values
                    part_data.green_brightness,
                    part_data.red_brightness,
                    part_data.blue_brightness
                );
            }

            // Apply default values for optional fields if not set
            params.frequency = (part_data.frequency == 0.0f) ? default_params_.default_frequency : part_data.frequency;
            params.speed = (part_data.speed == 0.0f) ? default_params_.default_speed : part_data.speed;
            params.marquee_direction = part_data.marquee_direction; // bool doesn't need default check
            params.on_time_duty = (part_data.on_time_duty == 0.0f) ? default_params_.default_on_time_duty : part_data.on_time_duty;

            // Store the data for later processing
            channel_proc_data.part_modes.push_back(std::make_pair(strip_part, mode));
            channel_proc_data.part_params.push_back(std::make_pair(strip_part, params));
        }

        if (channel_success) {
            channels_to_process.push_back(channel_proc_data);
        }
    }

    // Apply the collected data to hardware
    if (request->sync_channels) {
        // For sync_channels, latch all channel data first, then send all to hardware simultaneously
        RCLCPP_INFO(this->get_logger(), "Using sync_channels mode: latching %zu channels first, then sending simultaneously", channels_to_process.size());

        // Step 1: Latch all channel data (apply to internal buffers without sending to hardware)
        for (auto& channel_proc : channels_to_process) {
            bool channel_success = true;

            // Apply all part modes and parameters for this channel (without sending to hardware)
            for (size_t i = 0; i < channel_proc.part_modes.size(); ++i) {
                StripPart strip_part = channel_proc.part_modes[i].first;
                LightingMode mode = channel_proc.part_modes[i].second;
                const EffectParams& params = channel_proc.part_params[i].second;

                // Debug: Log mode setting details
                RCLCPP_INFO(this->get_logger(), "Setting mode for channel %d part %d: mode=%d, color=G=%d,R=%d,B=%d",
                            channel_proc.channel_id, static_cast<int>(strip_part), static_cast<int>(mode),
                            static_cast<int>(params.color.green_brightness), static_cast<int>(params.color.red_brightness), static_cast<int>(params.color.blue_brightness));

                bool success = false;
                if (mode == LightingMode::OFF) {
                    success = channel_proc.controller->turnOff(strip_part, false);  // Don't send to hardware yet
                } else {
                    success = channel_proc.controller->setLightingMode(strip_part, mode, params, false);  // Don't send to hardware yet
                }

                if (!success) {
                    errors.push_back("Failed to set mode for channel " + std::to_string(channel_proc.channel_id) + " part " + std::to_string(static_cast<int>(strip_part)));
                    channel_success = false;
                }
            }

            if (channel_success) {
                successful_channels.push_back(channel_proc.channel_id);
            }
        }

        // Step 2: Send all channel data to hardware simultaneously
        RCLCPP_INFO(this->get_logger(), "Latching complete. Now sending all %zu channels to hardware simultaneously", channels_to_process.size());
        for (auto& channel_proc : channels_to_process) {
            if (std::find(successful_channels.begin(), successful_channels.end(), channel_proc.channel_id) != successful_channels.end()) {
                // Send the entire channel data to hardware as one unit
                if (!channel_proc.controller->sendChannelToHardware()) {
                    errors.push_back("Failed to send channel " + std::to_string(channel_proc.channel_id) + " data to hardware");
                    // Remove from successful channels
                    successful_channels.erase(std::remove(successful_channels.begin(), successful_channels.end(), channel_proc.channel_id), successful_channels.end());
                }
            }
        }

    } else {
        // For non-sync mode, apply each channel individually and send to hardware immediately
        RCLCPP_INFO(this->get_logger(), "Using non-sync mode: processing %zu channels individually", channels_to_process.size());
        for (auto& channel_proc : channels_to_process) {
            bool channel_success = true;

            // Apply all part modes and parameters for this channel (without sending to hardware yet)
            for (size_t i = 0; i < channel_proc.part_modes.size(); ++i) {
                StripPart strip_part = channel_proc.part_modes[i].first;
                LightingMode mode = channel_proc.part_modes[i].second;
                const EffectParams& params = channel_proc.part_params[i].second;

                // Debug: Log mode setting details
                RCLCPP_INFO(this->get_logger(), "Setting mode for channel %d part %d: mode=%d, color=G=%d,R=%d,B=%d",
                            channel_proc.channel_id, static_cast<int>(strip_part), static_cast<int>(mode),
                            static_cast<int>(params.color.green_brightness), static_cast<int>(params.color.red_brightness), static_cast<int>(params.color.blue_brightness));

                bool success = false;
                if (mode == LightingMode::OFF) {
                    success = channel_proc.controller->turnOff(strip_part, false);  // Don't send to hardware yet
                } else {
                    success = channel_proc.controller->setLightingMode(strip_part, mode, params, false);  // Don't send to hardware yet
                }

                if (!success) {
                    errors.push_back("Failed to set mode for channel " + std::to_string(channel_proc.channel_id) + " part " + std::to_string(static_cast<int>(strip_part)));
                    channel_success = false;
                }
            }

            // Send this channel's data to hardware immediately after processing all its parts
            if (channel_success) {
                if (channel_proc.controller->sendChannelToHardware()) {
                    successful_channels.push_back(channel_proc.channel_id);
                } else {
                    errors.push_back("Failed to send channel " + std::to_string(channel_proc.channel_id) + " data to hardware");
                }
            }
        }
    }

    // Start effect threads if needed
    if (auto_start_effects_) {
        for (auto& channel_proc : channels_to_process) {
            // Check if any part has an animated mode
            bool has_animated_mode = false;
            for (const auto& part_mode : channel_proc.part_modes) {
                LightingMode mode = part_mode.second;
                if (mode == LightingMode::BREATHING || mode == LightingMode::FLASHING || mode == LightingMode::MARQUEE) {
                    has_animated_mode = true;
                    break;
                }
            }

            if (has_animated_mode) {
                channel_proc.controller->startEffectThread();
            }
        }
    }

    // Prepare response
    response->affected_channels = successful_channels;

    if (!successful_channels.empty()) {
        response->success = true;
        if (errors.empty()) {
            response->message = "Successfully applied LED control to " +
                              std::to_string(successful_channels.size()) + " channel(s)";
        } else {
            response->message = "Partially successful. Errors: ";
            for (size_t i = 0; i < errors.size(); ++i) {
                if (i > 0) response->message += "; ";
                response->message += errors[i];
            }
        }
    } else {
        response->message = "Failed to apply LED control to any channels. Errors: ";
        for (size_t i = 0; i < errors.size(); ++i) {
            if (i > 0) response->message += "; ";
            response->message += errors[i];
        }
    }

    last_command_time_ = std::chrono::steady_clock::now();

    RCLCPP_INFO(this->get_logger(), "LED control request: channels=%zu, success=%s",
                request->channels.size(), response->success ? "true" : "false");
}

} // namespace sl_vcu_all

// Main function
#include <signal.h>

std::shared_ptr<sl_vcu_all::LedDisplayControlNode> g_node = nullptr;

void signalHandler(int signal)
{
    (void)signal;
    if (g_node) {
        RCLCPP_INFO(g_node->get_logger(), "Received shutdown signal - checking enabled channels and turning off LEDs...");

        // Explicitly shutdown LED strips before ROS shutdown
        g_node->shutdownLedStrips();

        RCLCPP_INFO(g_node->get_logger(), "LED shutdown complete. Shutting down ROS node...");
        rclcpp::shutdown();
    }
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    // Set up signal handler
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    try {
        g_node = std::make_shared<sl_vcu_all::LedDisplayControlNode>();

        RCLCPP_INFO(g_node->get_logger(), "LED Display Control Node started");

        rclcpp::spin(g_node);

    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("led_display_control_node"), "Exception: %s", e.what());
        return 1;
    }

    g_node.reset();
    rclcpp::shutdown();

    return 0;
}
