#include "sl_vcu_all/imu_sensor.hpp"
#include <chrono>
#include <cstring>
#include <cmath>
#include <iostream>
#include <thread>

namespace sl_vcu_all
{

// Utility macro
#define CLEAR(x) memset(&(x), 0, sizeof(x))

ImuSensor::ImuSensor(const rclcpp::NodeOptions & options)
: Node("imu_sensor", options),
    gyro_device_fd_(-1),
    accel_device_fd_(-1),
    last_gyro_timestamp_(0),
    last_accel_timestamp_(0),
    keep_monitoring_(true),
    new_data_available_(false),
    bias_initialized_(false),
    current_yaw_(0.0),
    last_yaw_timestamp_(0),
    robot_moving_(false)
{
#if 0
    // Set thread priority to highest
    auto thread_id = pthread_self();
    struct sched_param param;
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    if (pthread_setschedparam(thread_id, SCHED_FIFO, &param) != 0) {
        RCLCPP_WARN(this->get_logger(), "Failed to set thread priority: %s", strerror(errno));
    } else {
        RCLCPP_INFO(this->get_logger(), "Set thread priority to %d", param.sched_priority);
    }
#endif
    // Initialize parameters
    initParameters();

    RCLCPP_INFO(this->get_logger(), "Initializing IMU Sensor");

    // Create publishers
    imu_pub_ = this->create_publisher<sensor_msgs::msg::Imu>(imu_topic_, 10);
    std::string imu_filtered_topic;
    this->get_parameter("imu_filtered_topic", imu_filtered_topic);
    imu_filtered_pub_ = this->create_publisher<sensor_msgs::msg::Imu>(imu_filtered_topic, 10);

    // Create cmd_vel subscriber
    cmd_vel_sub_ = this->create_subscription<geometry_msgs::msg::Twist>(
        cmd_vel_topic_, 10,
        std::bind(&ImuSensor::cmdVelCallback, this, std::placeholders::_1));

    // Create TF broadcaster if enabled
    if (publish_tf_) {
        tf_broadcaster_ = std::make_unique<tf2_ros::TransformBroadcaster>(*this);
        RCLCPP_INFO(this->get_logger(), "TF broadcasting enabled: %s -> %s",
                    parent_frame_id_.c_str(), child_frame_id_.c_str());
    }

    // Initialize IMU data messages
    latest_imu_data_.header.frame_id = imu_frame_id_;
    latest_filtered_imu_data_.header.frame_id = imu_frame_id_;

    // // Initialize covariance matrices with -1 to indicate "not available"
    // latest_imu_data_.orientation_covariance[0] = -1.0;
    // latest_imu_data_.angular_velocity_covariance[0] = -1.0;
    // latest_imu_data_.linear_acceleration_covariance[0] = -1.0;

    // latest_filtered_imu_data_.orientation_covariance[0] = -1.0;
    // latest_filtered_imu_data_.angular_velocity_covariance[0] = -1.0;
    // latest_filtered_imu_data_.linear_acceleration_covariance[0] = -1.0;

    std::array<double, 9> covariance_zero = {0.0};
    latest_imu_data_.orientation_covariance = covariance_zero;
    latest_imu_data_.angular_velocity_covariance = covariance_zero;
    latest_imu_data_.linear_acceleration_covariance = covariance_zero;

    latest_filtered_imu_data_.orientation_covariance = covariance_zero;
    latest_filtered_imu_data_.angular_velocity_covariance = covariance_zero;
    latest_filtered_imu_data_.linear_acceleration_covariance = covariance_zero;


    // Initialize bias with initial offset
    current_bias_.x = 0.0;
    current_bias_.y = 0.0;
    current_bias_.z = initial_bias_offset_;

    // Record start time
    node_start_time_ = std::chrono::steady_clock::now();
    last_cmd_vel_time_ = node_start_time_;
    last_bias_update_time_ = node_start_time_;

    // Open IMU devices (configuration should be done by setup script)
    bool devices_ok = openImuDevices();

    if (!devices_ok) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open IMU devices. Make sure to run setup script as root first:");
        RCLCPP_ERROR(this->get_logger(), "sudo ./scripts/setup_imu_devices.sh");
        return;
    }

    // Allocate data buffers (default size for device frames)
    gyro_data_buf_.resize(4096);  // Reasonable buffer size for device frames
    accel_data_buf_.resize(4096);

    // Start IMU monitoring thread
    imu_thread_ = std::thread(&ImuSensor::monitorImuDevices, this);

    // Create timer for publishing IMU data
    publish_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(publish_period_ms_),
        std::bind(&ImuSensor::publishImuData, this));

    RCLCPP_INFO(this->get_logger(), "IMU Sensor initialized successfully");
}

ImuSensor::~ImuSensor()
{
    // Stop monitoring thread
    keep_monitoring_ = false;

    if (imu_thread_.joinable()) {
        imu_thread_.join();
    }

    // Close devices (buffers will be disabled by setup script)
    if (gyro_device_fd_ >= 0) {
        close(gyro_device_fd_);
        gyro_device_fd_ = -1;
    }

    if (accel_device_fd_ >= 0) {
        close(accel_device_fd_);
        accel_device_fd_ = -1;
    }

    RCLCPP_INFO(this->get_logger(), "IMU Sensor shutdown");
}

void ImuSensor::initParameters()
{
    // Basic IMU device parameters
    this->declare_parameter("gyro_device_path", "/dev/iio:device1");
    this->declare_parameter("accel_device_path", "/dev/iio:device2");
    this->declare_parameter("imu_frame_id", "imu_link");
    this->declare_parameter("publish_period_ms", 5); // Default publish rate
    this->declare_parameter("imu_topic", "imu/data");

    // IMU sensor parameters
    this->declare_parameter("poll_timeout", 1000);
    this->declare_parameter("imu_sensor_acc_sensitivity", 4);
    this->declare_parameter("imu_sensor_gyro_sensitivity", 2000);
    this->declare_parameter("timestamp_sync_tolerance_ns", 10000000); // 5ms tolerance

    // Bias estimation parameters
    this->declare_parameter("initial_bias_offset", 0.1);
    this->declare_parameter("bias_calculation_time", 10.0);
    this->declare_parameter("bias_update_time", 5.0);
    this->declare_parameter("bias_update_threshold", 0.01);
    this->declare_parameter("cmd_vel_timeout", 2.0);

    // Topic parameters
    this->declare_parameter("cmd_vel_topic", "cmd_vel");
    this->declare_parameter("imu_filtered_topic", "sl_vcu_all/imu_data_filtered");

    // TF publishing parameters
    this->declare_parameter("publish_tf", false);
    this->declare_parameter("parent_frame_id", "base_link");
    this->declare_parameter("child_frame_id", "imu_link");

    // Get basic parameters
    gyro_device_path_ = this->get_parameter("gyro_device_path").as_string();
    accel_device_path_ = this->get_parameter("accel_device_path").as_string();
    imu_frame_id_ = this->get_parameter("imu_frame_id").as_string();
    publish_period_ms_ = this->get_parameter("publish_period_ms").as_int();
    imu_topic_ = this->get_parameter("imu_topic").as_string();

    // Get IMU sensor parameters
    poll_timeout_ = this->get_parameter("poll_timeout").as_int();
    imu_sensor_acc_sensitivity_ = this->get_parameter("imu_sensor_acc_sensitivity").as_int();
    imu_sensor_gyro_sensitivity_ = this->get_parameter("imu_sensor_gyro_sensitivity").as_int();
    timestamp_sync_tolerance_ns_ = this->get_parameter("timestamp_sync_tolerance_ns").as_int();

    // Get bias estimation parameters
    initial_bias_offset_ = this->get_parameter("initial_bias_offset").as_double();
    bias_calculation_time_ = this->get_parameter("bias_calculation_time").as_double();
    bias_update_time_ = this->get_parameter("bias_update_time").as_double();
    bias_update_threshold_ = this->get_parameter("bias_update_threshold").as_double();
    cmd_vel_timeout_ = this->get_parameter("cmd_vel_timeout").as_double();

    // Get topic parameters
    cmd_vel_topic_ = this->get_parameter("cmd_vel_topic").as_string();

    // Get TF publishing parameters
    publish_tf_ = this->get_parameter("publish_tf").as_bool();
    parent_frame_id_ = this->get_parameter("parent_frame_id").as_string();
    child_frame_id_ = this->get_parameter("child_frame_id").as_string();

    // Use imu_frame_id as child_frame_id if not explicitly set
    if (child_frame_id_ == "imu_link" && imu_frame_id_ != "imu_link") {
        child_frame_id_ = imu_frame_id_;
    }

    RCLCPP_INFO(this->get_logger(), "Gyro Device: %s", gyro_device_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "Accel Device: %s", accel_device_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "IMU Frame ID: %s", imu_frame_id_.c_str());
    RCLCPP_INFO(this->get_logger(), "IMU Topic: %s", imu_topic_.c_str());
    // RCLCPP_INFO(this->get_logger(), "Publish Rate: %d ms", publish_period_ms_);
    RCLCPP_INFO(this->get_logger(), "Timestamp Sync Tolerance: %d ns", timestamp_sync_tolerance_ns_);
    RCLCPP_INFO(this->get_logger(), "Publish TF: %s", publish_tf_ ? "true" : "false");
    if (publish_tf_) {
        RCLCPP_INFO(this->get_logger(), "TF: %s -> %s", parent_frame_id_.c_str(), child_frame_id_.c_str());
    }
}

bool ImuSensor::openImuDevices()
{
    RCLCPP_INFO(this->get_logger(), "Opening IMU devices...");
    RCLCPP_INFO(this->get_logger(), "Note: Device configuration should be done by setup script first");

    // Open gyroscope device
    gyro_device_fd_ = ::open(gyro_device_path_.c_str(), O_RDONLY | O_NONBLOCK);
    if (gyro_device_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open gyro device %s: %s",
                    gyro_device_path_.c_str(), strerror(errno));
        RCLCPP_ERROR(this->get_logger(), "Make sure the setup script has been run as root:");
        RCLCPP_ERROR(this->get_logger(), "sudo ./scripts/setup_imu_devices.sh");
        return false;
    }

    // Open accelerometer device
    accel_device_fd_ = ::open(accel_device_path_.c_str(), O_RDONLY | O_NONBLOCK);
    if (accel_device_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open accel device %s: %s",
                    accel_device_path_.c_str(), strerror(errno));
        RCLCPP_ERROR(this->get_logger(), "Make sure the setup script has been run as root:");
        RCLCPP_ERROR(this->get_logger(), "sudo ./scripts/setup_imu_devices.sh");
        close(gyro_device_fd_);
        gyro_device_fd_ = -1;
        return false;
    }

    RCLCPP_INFO(this->get_logger(), "Gyro device opened successfully, fd = %d", gyro_device_fd_);
    RCLCPP_INFO(this->get_logger(), "Accel device opened successfully, fd = %d", accel_device_fd_);
    RCLCPP_INFO(this->get_logger(), "IMU devices ready for monitoring");

    return true;
}

uint64_t ImuSensor::findClosestTimestamp(uint64_t target_timestamp, const std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>>& data_queue)
{
    if (data_queue.empty()) {
        return 0;
    }

    uint64_t closest_timestamp = 0;
    uint64_t min_diff = UINT64_MAX;

    for (const auto& data_pair : data_queue) {
        uint64_t timestamp = data_pair.first;
        uint64_t diff = (timestamp > target_timestamp) ? (timestamp - target_timestamp) : (target_timestamp - timestamp);

        if (diff < min_diff) {
            min_diff = diff;
            closest_timestamp = timestamp;
        }
    }

    // Only return the timestamp if it's within tolerance
    if (min_diff <= static_cast<uint64_t>(timestamp_sync_tolerance_ns_)) {
        return closest_timestamp;
    }

    return 0;
}

void ImuSensor::cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg)
{
    // Update last cmd_vel time and motion status
    last_cmd_vel_time_ = std::chrono::steady_clock::now();

    // Check if robot is moving (any non-zero velocity)
    robot_moving_ = (std::abs(msg->linear.x) > 1e-6 ||
                     std::abs(msg->linear.y) > 1e-6 ||
                     std::abs(msg->angular.z) > 1e-6);
}

void ImuSensor::updateBiasEstimation(double gyro_z, uint64_t timestamp)
{
    std::lock_guard<std::mutex> lock(bias_mutex_);

    auto current_time = std::chrono::steady_clock::now();
    auto elapsed_time = std::chrono::duration_cast<std::chrono::duration<double>>(
        current_time - node_start_time_).count();

    // During initial bias calculation period
    if (!bias_initialized_ && elapsed_time < bias_calculation_time_) {
        gyro_z_history_.push_back(std::make_pair(timestamp, gyro_z));

        // Remove old samples (keep only samples from the calculation period)
        auto cutoff_time = timestamp - static_cast<uint64_t>(bias_calculation_time_ * 1e9);
        while (!gyro_z_history_.empty() && gyro_z_history_.front().first < cutoff_time) {
            gyro_z_history_.pop_front();
        }
    }

    // Add to recent samples for standard deviation calculation
    gyro_z_samples_.push_back(gyro_z);

    // Remove old samples (keep only recent samples for bias update analysis)
    while (!gyro_z_samples_.empty() && gyro_z_samples_.size() > 1000) {  // Limit size
        gyro_z_samples_.pop_front();
    }
}

void ImuSensor::calculateInitialBias()
{
    std::lock_guard<std::mutex> lock(bias_mutex_);

    if (bias_initialized_ || gyro_z_history_.empty()) {
        return;
    }

    // Calculate average bias from collected samples
    //double sum_x = 0.0, sum_y = 0.0;
    double sum_z = 0.0;
    int count = 0;

    for (const auto& sample : gyro_z_history_) {
        sum_z += sample.second;
        count++;
    }

    if (count > 0) {
        //initial_bias_.x = 0.0;  // Assuming we only have gyro_z data
        //initial_bias_.y = 0.0;
        initial_bias_.z = sum_z / count;

        current_bias_ = initial_bias_;
        bias_initialized_ = true;

        RCLCPP_INFO(this->get_logger(), "Initial bias calculated: x=%.6f, y=%.6f, z=%.6f",
                    current_bias_.x, current_bias_.y, current_bias_.z);
    }
}

bool ImuSensor::shouldUpdateBias()
{
    auto current_time = std::chrono::steady_clock::now();

    // Check if enough time has passed since last cmd_vel
    auto time_since_cmd_vel = std::chrono::duration_cast<std::chrono::duration<double>>(
        current_time - last_cmd_vel_time_).count();

    // Check if enough time has passed since last bias update
    auto time_since_bias_update = std::chrono::duration_cast<std::chrono::duration<double>>(
        current_time - last_bias_update_time_).count();

    if (robot_moving_ || time_since_cmd_vel < cmd_vel_timeout_ ||
        time_since_bias_update < bias_update_time_) {
        return false;
    }

    std::lock_guard<std::mutex> lock(bias_mutex_);

    // Calculate standard deviation of recent gyro_z samples
    if (gyro_z_samples_.size() < 10) {  // Need minimum samples
        return false;
    }

    double mean = std::accumulate(gyro_z_samples_.begin(), gyro_z_samples_.end(), 0.0) / gyro_z_samples_.size();
    double variance = 0.0;

    for (double sample : gyro_z_samples_) {
        variance += (sample - mean) * (sample - mean);
    }
    variance /= gyro_z_samples_.size();
    double std_dev = std::sqrt(variance);

    return std_dev < bias_update_threshold_;
}

geometry_msgs::msg::Vector3 ImuSensor::applyBiasCorrection(const geometry_msgs::msg::Vector3& gyro_data)
{
    std::lock_guard<std::mutex> lock(bias_mutex_);

    geometry_msgs::msg::Vector3 corrected_data;
    corrected_data.x = gyro_data.x - current_bias_.x;
    corrected_data.y = gyro_data.y - current_bias_.y;
    corrected_data.z = gyro_data.z - current_bias_.z;

    return corrected_data;
}

void ImuSensor::integrateYaw(const geometry_msgs::msg::Vector3& filtered_gyro, uint64_t timestamp)
{
    if (last_yaw_timestamp_ == 0) {
        last_yaw_timestamp_ = timestamp;
        return;
    }

    // Calculate time difference in seconds
    double dt = static_cast<double>(timestamp - last_yaw_timestamp_) / 1e9;

    // Integrate gyro_z to get yaw
    if (bias_initialized_)
    {
        current_yaw_ += filtered_gyro.z * dt;
    }
    
    // Normalize yaw to [-pi, pi]
    while (current_yaw_ > M_PI) current_yaw_ -= 2.0 * M_PI;
    while (current_yaw_ < -M_PI) current_yaw_ += 2.0 * M_PI;

    last_yaw_timestamp_ = timestamp;
}

void ImuSensor::publishTransform(const builtin_interfaces::msg::Time& timestamp)
{
    if (!publish_tf_ || !tf_broadcaster_) {
        return;
    }

    geometry_msgs::msg::TransformStamped transform_stamped;

    // Set header
    transform_stamped.header.stamp = timestamp;
    transform_stamped.header.frame_id = parent_frame_id_;
    transform_stamped.child_frame_id = child_frame_id_;

    // Set translation (assuming IMU is at origin relative to parent frame)
    // These can be made configurable if needed
    transform_stamped.transform.translation.x = 0.0;
    transform_stamped.transform.translation.y = 0.0;
    transform_stamped.transform.translation.z = 0.1;

    // Set rotation from integrated yaw (only yaw, roll and pitch are 0)
    tf2::Quaternion q;
    q.setRPY(0.0, 0.0, 0.0);  // roll, pitch, yaw

    transform_stamped.transform.rotation.x = q.x();
    transform_stamped.transform.rotation.y = q.y();
    transform_stamped.transform.rotation.z = q.z();
    transform_stamped.transform.rotation.w = q.w();

    // Publish the transform
    tf_broadcaster_->sendTransform(transform_stamped);
}

void ImuSensor::monitorImuDevices()
{
    RCLCPP_INFO(this->get_logger(), "Starting IMU monitoring thread");

#if 0
    // Set thread priority to highest
    auto thread_id = pthread_self();
    struct sched_param param;
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    if (pthread_setschedparam(thread_id, SCHED_FIFO, &param) != 0) {
        RCLCPP_WARN(this->get_logger(), "Failed to set IMU thread priority: %s", strerror(errno));
    } else {
        RCLCPP_INFO(this->get_logger(), "Set IMU thread priority to %d", param.sched_priority);
    }
#endif
    // Setup polling for both devices
    struct pollfd fds[2];
    fds[0].fd = gyro_device_fd_;
    fds[0].events = POLLIN;
    fds[0].revents = 0;

    fds[1].fd = accel_device_fd_;
    fds[1].events = POLLIN;
    fds[1].revents = 0;

    // Calculate conversion factors
    double acc_q16_factor = (imu_sensor_acc_sensitivity_ * (1<<16) / (1<<15));
    double gyro_q16_factor = (imu_sensor_gyro_sensitivity_ * (1<<16) / (1<<15));

    while (keep_monitoring_) {
        int ret = poll(fds, 2, poll_timeout_);

        if (ret < 0) {
            RCLCPP_ERROR(this->get_logger(), "Poll error: %s", strerror(errno));
            continue;
        } else if (ret == 0) {
            RCLCPP_WARN(this->get_logger(), "Poll timeout");
            continue;
        }

        // Check gyroscope device
        if (fds[0].revents & POLLIN) {
            // Read data from gyroscope device
            ret = ::read(fds[0].fd, gyro_data_buf_.data(), gyro_data_buf_.size());

            if (ret < 0) {
                RCLCPP_ERROR(this->get_logger(), "Gyro read error: %s", strerror(errno));
            } else if (ret >= static_cast<int>(sizeof(gyro_device_buffer_frame))) {
                // Process gyroscope data
                int num_frames = ret / sizeof(gyro_device_buffer_frame);

                for (int i = 0; i < num_frames; ++i) 
                {
                    gyro_device_buffer_frame* frame = reinterpret_cast<gyro_device_buffer_frame*>(
                        gyro_data_buf_.data() + i * sizeof(gyro_device_buffer_frame));

                    // Extract timestamp
                    uint64_t timestamp = *reinterpret_cast<uint64_t*>(frame->timestamp);

                    // Skip if same timestamp as last frame
                    if (timestamp == last_gyro_timestamp_) {
                        continue;
                    }

                    last_gyro_timestamp_ = timestamp;

                    // Extract gyroscope data (convert from device format to rad/s)
                    int16_t gyro_x = (frame->gyro_x_h << 8) | frame->gyro_x_l;
                    int16_t gyro_y = (frame->gyro_y_h << 8) | frame->gyro_y_l;
                    int16_t gyro_z = (frame->gyro_z_h << 8) | frame->gyro_z_l;

                    // Convert to proper units (rad/s from dps)
                    double gyro_x_rads = static_cast<double>(gyro_x) * gyro_q16_factor / 65536.0 * M_PI / 180.0;
                    double gyro_y_rads = static_cast<double>(gyro_y) * gyro_q16_factor / 65536.0 * M_PI / 180.0;
                    double gyro_z_rads = static_cast<double>(gyro_z) * gyro_q16_factor / 65536.0 * M_PI / 180.0;

                    // Store gyroscope data in queue
                    geometry_msgs::msg::Vector3 gyro_data;
                    gyro_data.x = gyro_x_rads;
                    gyro_data.y = gyro_y_rads;
                    gyro_data.z = gyro_z_rads;

#if 0
                    {
                        std::lock_guard<std::mutex> lock(gyro_data_mutex_);
                        gyro_data_queue_.push_back(std::make_pair(timestamp, gyro_data));

                        // Keep queue size manageable (last 100 samples)
                        if (gyro_data_queue_.size() > 100) {
                            gyro_data_queue_.pop_front();
                        }
                    }
#endif
                    // Update bias estimation with gyro_z data
                    updateBiasEstimation(gyro_z_rads, timestamp);

                    // Check if we should calculate initial bias
                    auto current_time = std::chrono::steady_clock::now();
                    auto elapsed_time = std::chrono::duration_cast<std::chrono::duration<double>>(
                        current_time - node_start_time_).count();

                    if (!bias_initialized_ && elapsed_time >= bias_calculation_time_) {
                        calculateInitialBias();
                    }

                    // Apply bias correction to get filtered data
                    geometry_msgs::msg::Vector3 filtered_gyro_data = applyBiasCorrection(gyro_data);

                    // Check if we should update bias based on recent data
                    if (bias_initialized_ && shouldUpdateBias()) {
                        std::lock_guard<std::mutex> lock(bias_mutex_);

                        // Calculate new bias from recent samples
                        if (!gyro_z_samples_.empty()) {
                            double new_bias_z = std::accumulate(gyro_z_samples_.begin(), gyro_z_samples_.end(), 0.0) / gyro_z_samples_.size();
                            current_bias_.z = new_bias_z;
                            last_bias_update_time_ = current_time;

                            RCLCPP_DEBUG(this->get_logger(), "Updated bias: z=%.6f", current_bias_.z);
                        }
                    }

                    // Integrate yaw from filtered gyro data
                    integrateYaw(filtered_gyro_data, timestamp);

                    // Try to find matching accelerometer data for this gyroscope timestamp
                    uint64_t closest_accel_timestamp = 0;
                    geometry_msgs::msg::Vector3 matched_accel_data;

                    {
                        std::lock_guard<std::mutex> lock(accel_data_mutex_);
                        closest_accel_timestamp = findClosestTimestamp(timestamp, accel_data_queue_);

                        if (closest_accel_timestamp != 0) 
                        {
                            // Find the accel data with the closest timestamp
                            for (const auto& accel_pair : accel_data_queue_) {
                                if (accel_pair.first == closest_accel_timestamp) {
                                    matched_accel_data = accel_pair.second;
                                    break;
                                }
                            }
                        }
                    }

                    // Publish synchronized IMU data (raw and filtered)
                    {
                        std::lock_guard<std::mutex> lock(imu_data_mutex_);

#if 0
                        // Use gyroscope timestamp as the primary timestamp
                        uint64_t sec = timestamp / 1000000000ULL;
                        uint64_t nsec = timestamp % 1000000000ULL;

                        // Update raw IMU data
                        latest_imu_data_.header.stamp.sec = static_cast<int32_t>(sec);
                        latest_imu_data_.header.stamp.nanosec = static_cast<uint32_t>(nsec);
#endif
                        if (closest_accel_timestamp != 0)
                        {
                            // Set linear acceleration
                            latest_imu_data_.linear_acceleration = matched_accel_data;
                            latest_filtered_imu_data_.linear_acceleration = matched_accel_data;
                        }

                        // Set angular velocity (raw)
                        latest_imu_data_.angular_velocity = gyro_data;

                        // Update filtered IMU data
#if 0
                        latest_filtered_imu_data_.header.stamp.sec = static_cast<int32_t>(sec);
                        latest_filtered_imu_data_.header.stamp.nanosec = static_cast<uint32_t>(nsec);
#endif

                        latest_filtered_imu_data_.angular_velocity = filtered_gyro_data;

                        // Set orientation from integrated yaw (only yaw, roll and pitch are 0)
                        // Convert yaw to quaternion
                        double half_yaw = current_yaw_ * 0.5;
                        latest_filtered_imu_data_.orientation.x = 0.0;
                        latest_filtered_imu_data_.orientation.y = 0.0;
                        latest_filtered_imu_data_.orientation.z = std::sin(half_yaw);
                        latest_filtered_imu_data_.orientation.w = std::cos(half_yaw);

                        // Mark that we have new data
                        new_data_available_ = true;
                    }

#if 0
                    // Publish both raw and filtered IMU data
                    imu_pub_->publish(latest_imu_data_);
                    imu_filtered_pub_->publish(latest_filtered_imu_data_);

                    // Publish TF transform if enabled
                    publishTransform(latest_filtered_imu_data_.header.stamp);
#endif
                    auto imu_time = rclcpp::Time(latest_filtered_imu_data_.header.stamp.sec, latest_filtered_imu_data_.header.stamp.nanosec);

                    //auto current_time_test = std::chrono::system_clock::now();

                    //auto current_system_clock = std::chrono::duration_cast<std::chrono::duration<double>>(current_time_test.time_since_epoch()).count();

                    auto current_steady_time = std::chrono::duration_cast<std::chrono::duration<double>>(std::chrono::steady_clock::now().time_since_epoch()).count();


                    // RCLCPP_WARN(this->get_logger(), "New IMU data available system now ts: %f, ros now ts: %f, imu ts: %f, delta_ros: %f, delta_imu: %f", 
                    // current_system_clock, this->now().seconds(), imu_time.seconds(), this->now().seconds() - current_system_clock, imu_time.seconds() - current_system_clock);

                    RCLCPP_DEBUG(this->get_logger(), "New IMU data available steady now ts: %f, ros now ts: %f, imu ts: %f, delta_imu: %f", 
                    current_steady_time, this->now().seconds(), imu_time.seconds(), imu_time.seconds() - current_steady_time);
                }
            }
        }

        // Check accelerometer device
        if (fds[1].revents & POLLIN) {
            // Read data from accelerometer device
            ret = ::read(fds[1].fd, accel_data_buf_.data(), accel_data_buf_.size());

            if (ret < 0) {
                RCLCPP_ERROR(this->get_logger(), "Accel read error: %s", strerror(errno));
            } else if (ret >= static_cast<int>(sizeof(accel_device_buffer_frame))) {
                // Process accelerometer data
                int num_frames = ret / sizeof(accel_device_buffer_frame);

                for (int i = 0; i < num_frames; ++i) {
                    accel_device_buffer_frame* frame = reinterpret_cast<accel_device_buffer_frame*>(
                        accel_data_buf_.data() + i * sizeof(accel_device_buffer_frame));

                    // Extract timestamp
                    uint64_t timestamp = *reinterpret_cast<uint64_t*>(frame->timestamp);

                    // Skip if same timestamp as last frame
                    if (timestamp == last_accel_timestamp_) {
                        continue;
                    }

                    last_accel_timestamp_ = timestamp;

                    // Extract accelerometer data (convert from device format to m/s^2)
                    int16_t acc_x = (frame->acc_x_h << 8) | frame->acc_x_l;
                    int16_t acc_y = (frame->acc_y_h << 8) | frame->acc_y_l;
                    int16_t acc_z = (frame->acc_z_h << 8) | frame->acc_z_l;

                    // Convert to proper units (m/s^2 from g's)
                    double acc_x_ms2 = static_cast<double>(acc_x) * acc_q16_factor / 65536.0 * 9.81;
                    double acc_y_ms2 = static_cast<double>(acc_y) * acc_q16_factor / 65536.0 * 9.81;
                    double acc_z_ms2 = static_cast<double>(acc_z) * acc_q16_factor / 65536.0 * 9.81;

                    // Store accelerometer data in queue
                    geometry_msgs::msg::Vector3 accel_data;
                    accel_data.x = acc_x_ms2;
                    accel_data.y = acc_y_ms2;
                    accel_data.z = acc_z_ms2;

                    {
                        std::lock_guard<std::mutex> lock(accel_data_mutex_);
                        accel_data_queue_.push_back(std::make_pair(timestamp, accel_data));

                        // Keep queue size manageable (last 100 samples)
                        if (accel_data_queue_.size() > 100) {
                            accel_data_queue_.pop_front();
                        }
                    }

#if 0
                    // Try to find matching gyroscope data for this accelerometer timestamp
                    uint64_t closest_gyro_timestamp = 0;
                    geometry_msgs::msg::Vector3 matched_gyro_data;

                    {
                        std::lock_guard<std::mutex> lock(gyro_data_mutex_);
                        closest_gyro_timestamp = findClosestTimestamp(timestamp, gyro_data_queue_);

                        if (closest_gyro_timestamp != 0) {
                            // Find the gyro data with the closest timestamp
                            for (const auto& gyro_pair : gyro_data_queue_) {
                                if (gyro_pair.first == closest_gyro_timestamp) {
                                    matched_gyro_data = gyro_pair.second;
                                    break;
                                }
                            }
                        }
                    }

                    // If we found a matching gyroscope sample, publish synchronized IMU data
                    if (closest_gyro_timestamp != 0) {
                        std::lock_guard<std::mutex> lock(imu_data_mutex_);

                        // Use accelerometer timestamp as the primary timestamp
                        uint64_t sec = timestamp / 1000000000ULL;
                        uint64_t nsec = timestamp % 1000000000ULL;
                        latest_imu_data_.header.stamp.sec = static_cast<int32_t>(sec);
                        latest_imu_data_.header.stamp.nanosec = static_cast<uint32_t>(nsec);

                        // Set linear acceleration
                        latest_imu_data_.linear_acceleration = accel_data;

                        // Set angular velocity
                        latest_imu_data_.angular_velocity = matched_gyro_data;

                        imu_pub_->publish(latest_imu_data_);

                        // Mark that we have new data
                        new_data_available_ = true;
                    }
#endif
                }
            }
        }
    }

    RCLCPP_INFO(this->get_logger(), "IMU monitoring thread stopped");
}

void ImuSensor::publishImuData()
{
    std::lock_guard<std::mutex> lock(imu_data_mutex_);

    if (!new_data_available_) {
        // RCLCPP_WARN(this->get_logger(), "No data update, publish last data");
    }

    auto time_now = this->now();
    latest_imu_data_.header.stamp = time_now;
    latest_filtered_imu_data_.header.stamp = time_now;


    // Publish both raw and filtered IMU data
    imu_pub_->publish(latest_imu_data_);
    imu_filtered_pub_->publish(latest_filtered_imu_data_);

    // Publish TF transform if enabled
    publishTransform(time_now);
    new_data_available_ = false;
}

} // namespace sl_vcu_all

// Main entry point
int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<sl_vcu_all::ImuSensor>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
