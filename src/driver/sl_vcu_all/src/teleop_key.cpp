#include <signal.h>
#include <stdio.h>
#include <cfloat>  // For DBL_EPSILON
#include <chrono>  // For time tracking

#include <functional>
#include <stdexcept>
#include <thread>
#include <algorithm>  // For std::clamp

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>

#ifdef _WIN32
# include <windows.h>
#else
# include <termios.h>
# include <unistd.h>
# include <poll.h>  // For timeout in readOne
#endif

static constexpr char KEYCODE_RIGHT = 'd';
static constexpr char KEYCODE_LEFT = 'a';
static constexpr char KEYCODE_UP =  'w';
static constexpr char KEYCODE_DOWN = 's';
static constexpr char KEYCODE_Q = 'q';
static constexpr char KEYCODE_ESC = 0x1B;

bool running = true;

class KeyboardReader final
{
public:
  KeyboardReader()
  {
#ifdef _WIN32
    hstdin_ = GetStdHandle(STD_INPUT_HANDLE);
    if (hstdin_ == INVALID_HANDLE_VALUE) {
      throw std::runtime_error("Failed to get stdin handle");
    }
    if (!GetConsoleMode(hstdin_, &old_mode_)) {
      throw std::runtime_error("Failed to get old console mode");
    }
    DWORD new_mode = ENABLE_PROCESSED_INPUT;
    if (!SetConsoleMode(hstdin_, new_mode)) {
      throw std::runtime_error("Failed to set new console mode");
    }
#else
    if (tcgetattr(0, &cooked_) < 0) {
      throw std::runtime_error("Failed to get old console mode");
    }
    struct termios raw;
    memcpy(&raw, &cooked_, sizeof(struct termios));
    raw.c_lflag &= ~(ICANON | ECHO);
    raw.c_cc[VEOL] = 1;
    raw.c_cc[VEOF] = 2;
    raw.c_cc[VTIME] = 1;
    raw.c_cc[VMIN] = 0;
    if (tcsetattr(0, TCSANOW, &raw) < 0) {
      throw std::runtime_error("Failed to set new console mode");
    }
#endif
  }

  char readOne()
  {
    char c = 0;

#ifdef _WIN32
    INPUT_RECORD record;
    DWORD num_read;
    switch (WaitForSingleObject(hstdin_, 1000)) {
      case WAIT_OBJECT_0:
        if (!ReadConsoleInput(hstdin_, &record, 1, &num_read)) {
          throw std::runtime_error("Read failed");
        }
        if (record.EventType != KEY_EVENT || !record.Event.KeyEvent.bKeyDown) {
          break;
        }
        if (record.Event.KeyEvent.wVirtualKeyCode == VK_LEFT) {
          c = KEYCODE_LEFT;
        } else if (record.Event.KeyEvent.wVirtualKeyCode == VK_UP) {
          c = KEYCODE_UP;
        } else if (record.Event.KeyEvent.wVirtualKeyCode == VK_RIGHT) {
          c = KEYCODE_RIGHT;
        } else if (record.Event.KeyEvent.wVirtualKeyCode == VK_DOWN) {
          c = KEYCODE_DOWN;
        } else if (record.Event.KeyEvent.wVirtualKeyCode == 0x51) {
          c = KEYCODE_Q;
        }
        break;

      case WAIT_TIMEOUT:
        break;
    }
#else
    struct pollfd fds;
    fds.fd = 0;  // stdin
    fds.events = POLLIN;

    int ret = poll(&fds, 1, 100);
    if (ret > 0 && (fds.revents & POLLIN)) {
      int rc = read(0, &c, 1);
      if (rc < 0) {
        throw std::runtime_error("read failed");
      }
    }
#endif

    return c;
  }

  ~KeyboardReader()
  {
#ifdef _WIN32
    SetConsoleMode(hstdin_, old_mode_);
#else
    tcsetattr(0, TCSANOW, &cooked_);
#endif
  }

private:
#ifdef _WIN32
  HANDLE hstdin_;
  DWORD old_mode_;
#else
  struct termios cooked_;
#endif
};

class TeleopChassis final
{
public:
  TeleopChassis()
  {
    nh_ = rclcpp::Node::make_shared("teleop_chassis");
    nh_->declare_parameter("scale_angular", 1.0);
    nh_->declare_parameter("scale_linear", 1.0);
    nh_->declare_parameter("target_linear", 1.0);
    nh_->declare_parameter("target_angular", 1.0);
    nh_->declare_parameter("max_linear_speed", 1.0);
    nh_->declare_parameter("max_angular_speed", 1.0);
    nh_->declare_parameter("acceleration_linear", 0.5);  // m/s^2
    nh_->declare_parameter("acceleration_angular", 1.0);  // rad/s^2
    nh_->declare_parameter("deceleration_linear", 0.5);  // m/s^2
    nh_->declare_parameter("deceleration_angular", 1.5);  // rad/s^2

    twist_pub_ = nh_->create_publisher<geometry_msgs::msg::Twist>("sl_chassis_teleop/cmd_vel", 10);

    current_linear_speed_ = 0.0;
    current_angular_speed_ = 0.0;
    last_speed_change_time_ = std::chrono::steady_clock::now();
  }

  int keyLoop()
  {
    char c;
    auto prev_time = std::chrono::steady_clock::now();

    std::thread{std::bind(&TeleopChassis::spin, this)}.detach();

    puts("Reading from keyboard");
    puts("---------------------------");
    puts("Use arrow keys to move the chassis.");
    puts("'q' to quit.");

    while (running) {

      auto current_time = std::chrono::steady_clock::now();
      std::chrono::duration<double> dt = current_time - prev_time;

      if (dt.count() < 0.01) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        continue;
      }

      try {
        c = input_.readOne();
      } catch (const std::runtime_error &) {
        perror("read():");
        return -1;
      }

      

      prev_time = current_time;


      double target_linear = 0.0;
      double target_angular = 0.0;

      RCLCPP_DEBUG(nh_->get_logger(), "value: 0x%02X dt: %8.6f", c, dt.count());

      switch (c) {
        case KEYCODE_LEFT:
          target_angular = nh_->get_parameter("target_angular").as_double();
          break;
        case KEYCODE_RIGHT:
          target_angular = -nh_->get_parameter("target_angular").as_double();
          break;
        case KEYCODE_UP:
          target_linear = nh_->get_parameter("target_linear").as_double();
          break;
        case KEYCODE_DOWN:
          target_linear = -nh_->get_parameter("target_linear").as_double();
          break;
        case KEYCODE_Q:
          running = false;
          break;
        case KEYCODE_ESC:
          break;
        default:
          // target_angular = 0.0;
          // target_linear = 0.0;
          break;
      }


      double accel_linear = nh_->get_parameter("acceleration_linear").as_double() * dt.count();
      double accel_angular = nh_->get_parameter("acceleration_angular").as_double() * dt.count();
      double decel_linear = nh_->get_parameter("deceleration_linear").as_double() * dt.count();
      double decel_angular = nh_->get_parameter("deceleration_angular").as_double() * dt.count();

      updateSpeed(target_linear, accel_linear, decel_linear, current_linear_speed_);
      updateSpeed(target_angular, accel_angular, decel_angular, current_angular_speed_);

      double max_linear_speed = nh_->get_parameter("max_linear_speed").as_double();
      double max_angular_speed = nh_->get_parameter("max_angular_speed").as_double();

      current_linear_speed_ = std::clamp(current_linear_speed_, -max_linear_speed, max_linear_speed);
      current_angular_speed_ = std::clamp(current_angular_speed_, -max_angular_speed, max_angular_speed);

      // Check if both linear and angular speeds are zero
      if (fabs(current_linear_speed_) < DBL_EPSILON && fabs(current_angular_speed_) < DBL_EPSILON)
      {
        // Check time elapsed since both speeds became zero
        auto elapsed = std::chrono::steady_clock::now() - last_speed_change_time_;
        if (elapsed > std::chrono::seconds(5)) {
          // If 5 seconds have passed, stop publishing cmd_vel
          continue;
        }
      } 
      else 
      {
        // Reset the time tracker if speeds are not zero
        last_speed_change_time_ = std::chrono::steady_clock::now();
      }


      if (running) {
        geometry_msgs::msg::Twist twist;
        twist.linear.x = current_linear_speed_;
        twist.angular.z = current_angular_speed_;
        twist_pub_->publish(twist);

        RCLCPP_DEBUG(nh_->get_logger(), "linear: %8.4f, angular: %8.4f \n", twist.linear.x, twist.angular.z);
      }
    }

    return 0;
  }

private:
  void spin()
  {
    rclcpp::spin(nh_);
  }

  void updateSpeed(double target, double accel, double decel, double &current) 
  {
    // If target is not zero (i.e., target speed exists)
    if (fabs(target) > DBL_EPSILON) {
      // If current and target have opposite signs (different directions)
      if ((target > 0 && current < 0) || (target < 0 && current > 0)) 
      {
        // Decelerate using accel + decel
        double deceleration = accel + decel;
        if (current > 0) 
        {
          current -= deceleration;
          if (current < 0) current = 0;
        } 
        else if (current < 0) 
        {
          current += deceleration;
          if (current > 0) current = 0;
        }
      } 
      // If in the same direction or current is zero, apply acceleration
      if (current == 0 || (target > 0 && current >= 0) || (target < 0 && current <= 0)) 
      {
        current += (target > 0 ? accel : -accel);
      }
    } 
    // If target is zero, decelerate to stop
    else 
    {
      if (current > 0) 
      {
        current -= decel;
        if (current < 0) current = 0;
      } 
      else if (current < 0) 
      {
        current += decel;
        if (current > 0) current = 0;
      }
    }
  }

  rclcpp::Node::SharedPtr nh_;
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr twist_pub_;

  KeyboardReader input_;
  double current_linear_speed_;
  double current_angular_speed_;
   std::chrono::steady_clock::time_point last_speed_change_time_;
};

#ifdef _WIN32
BOOL WINAPI quit(DWORD ctrl_type)
{
  running = false;
  return true;
}
#else
void quit(int sig)
{
  (void)sig;
  running = false;
}
#endif

int main(int argc, char **argv)
{
  rclcpp::init(argc, argv);

#ifdef _WIN32
  SetConsoleCtrlHandler(quit, TRUE);
#else
  signal(SIGINT, quit);
#endif

  TeleopChassis teleop_chassis;

  int rc = teleop_chassis.keyLoop();

  rclcpp::shutdown();

  return rc;
}
