#include "sl_vcu_all/zl_motor_controller.hpp"
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <cstring>
#include <chrono>
#include <functional>
#include <sstream>
#include <iomanip>
#include <pthread.h>
#include <sys/resource.h>
#include <fcntl.h>
#include <errno.h>



using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

namespace sl_vcu_all
{

ZLMotorController::ZLMotorController(const rclcpp::NodeOptions & options)
    : Node("zl_motor_controller", options),
    motors_enabled_(false),
    left_pos_(0),
    right_pos_(0),
    left_pos_prev_(0),
    right_pos_prev_(0),
    left_speed_(0),
    right_speed_(0),
    left_current_(0),
    right_current_(0),
    gpio_status_(0),
    left_temp_(0),
    right_temp_(0),
    driver_temp_(0),
    last_alarm_(0),
    x_(0.0),
    y_(0.0),
    theta_(0.0),
    cycle_counter_(0),
    dispatcher_ready_(false),
    control_state_(ControlState::INIT),
    socket_fd_(-1),
    is_socket_initialized_(false),
    use_sockcan_direct_(false),
    running_(false),
    state_counter_(0),
    cmd_vel_timeout_ms_(500),
    bumper_timeout_ms_(5000),
    status_update_cycle_ms_(1000),
    sdo_response_timeout_ms_(1000),
    publish_motor_info_(true)    
{
    // Initialize parameters
    initParameters();

    RCLCPP_INFO(this->get_logger(), "Initializing ZL Motor Controller");
    last_cmd_vel_time_ = this->now();
    last_bumper_time_ = this->now();

    // Initialize bumper states to false (not triggered)
    latest_bumper_state_.front_bumper_triggered = false;
    latest_bumper_state_.back_bumper_triggered = false;

    // Create publishers
    odom_pub_ = this->create_publisher<nav_msgs::msg::Odometry>(odom_topic_, 10);
    motor_info_pub_ = this->create_publisher<sl_vcu_all::msg::MotorInfo>(motor_info_topic_, 10);
    motor_state_pub_ = this->create_publisher<sl_vcu_all::msg::MotorState>(motor_state_topic_, 10);

    // Create CAN TX publisher only if not using direct sockcan
    if (!use_sockcan_direct_) {
        can_tx_pub_ = this->create_publisher<sl_vcu_all::msg::CanFrame>(can_tx_topic_, 10);
    }

    // Initialize socket CAN if enabled
    if (use_sockcan_direct_) {
        is_socket_initialized_ = initSocketCan();
        if (!is_socket_initialized_) {
            RCLCPP_ERROR(this->get_logger(), "Failed to initialize SocketCAN, falling back to ROS topics");
            use_sockcan_direct_ = false;
            can_tx_pub_ = this->create_publisher<sl_vcu_all::msg::CanFrame>(can_tx_topic_, 10);
        } else {
            RCLCPP_INFO(this->get_logger(), "SocketCAN initialized successfully on interface %s", can_interface_.c_str());

            // Initialize timing
            last_send_time_ = std::chrono::steady_clock::now();

            // Start the threads
            running_ = true;
            receive_thread_ = std::thread(&ZLMotorController::receiveCanFramesThread, this);
            send_thread_ = std::thread(&ZLMotorController::sendCanFramesThread, this);
            RCLCPP_INFO(this->get_logger(), "SocketCAN receive and send threads started");
        }
    }


    // Create subscribers
    cmd_vel_sub_ = this->create_subscription<geometry_msgs::msg::Twist>(
        cmd_vel_topic_, 10,
        std::bind(&ZLMotorController::cmdVelCallback, this, std::placeholders::_1));

    bumper_sub_ = this->create_subscription<sl_vcu_all::msg::BumperState>(
        bumper_topic_, 10,
        std::bind(&ZLMotorController::bumperCallback, this, std::placeholders::_1));

    alarm_clear_sub_ = this->create_subscription<std_msgs::msg::Bool>(
        "clear_alarm", 10,
        std::bind(&ZLMotorController::clearAlarmCallback, this, std::placeholders::_1));

#if 0
    // Subscribe to filtered odometry if topic is specified
    if (!filtered_odom_topic_.empty()) {
        filtered_odom_sub_ = this->create_subscription<nav_msgs::msg::Odometry>(
            filtered_odom_topic_, 10,
            std::bind(&ZLMotorController::filteredOdomCallback, this, std::placeholders::_1));
        RCLCPP_INFO(this->get_logger(), "Subscribed to filtered odometry topic: %s", filtered_odom_topic_.c_str());
    }
#endif 

    {
        filtered_imu_sub_ = this->create_subscription<sensor_msgs::msg::Imu>(
            "sl_vcu_all/imu_data_filtered", 10,
            std::bind(&ZLMotorController::filteredImuCallback, this, std::placeholders::_1));
        RCLCPP_INFO(this->get_logger(), "Subscribed to filtered imu topic: %s", "sl_vcu_all/imu_data_filtered");
    }

    // Create service clients if dispatcher check is enabled
    if (check_dispatcher_node_) {
        check_status_client_ = this->create_client<sl_vcu_all::srv::CheckNodeStatus>(check_status_service_);
        add_filter_client_ = this->create_client<sl_vcu_all::srv::AddCanFilter>(add_filter_service_);
    }

    // Create TF broadcaster if enabled
    if (publish_tf_) {
        tf_broadcaster_ = std::make_unique<tf2_ros::TransformBroadcaster>(this);
    }

    // Set up the control timer with the specified control cycle
    control_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(control_cycle_ms_),
        std::bind(&ZLMotorController::controlTimerCallback, this));

    // Set up the status timer for temperature and current updates
    status_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(status_update_cycle_ms_),
        std::bind(&ZLMotorController::statusTimerCallback, this));

    last_odom_time_ = this->now();

    // Set real-time priority
    //setRealTimeProcessPriority();

    RCLCPP_INFO(this->get_logger(), "ZL Motor Controller initialized");
}

ZLMotorController::~ZLMotorController()
{
    // Disable motors when node is shutting down
    disableMotors();

    // Stop the threads
    running_ = false;

    // Notify send thread to wake up and exit
    {
        //std::lock_guard<std::mutex> lock(send_queue_mutex_);
        send_queue_cv_.notify_all();
    }

    if (receive_thread_.joinable()) {
        receive_thread_.join();
        RCLCPP_INFO(this->get_logger(), "SocketCAN receive thread stopped");
    }

    if (send_thread_.joinable()) {
        send_thread_.join();
        RCLCPP_INFO(this->get_logger(), "SocketCAN send thread stopped");
    }

    // Close socket if initialized
    if (socket_fd_ >= 0) {
        close(socket_fd_);
        socket_fd_ = -1;
    }
}

void ZLMotorController::initParameters()
{
    // CAN communication parameters
    this->declare_parameter("can_id_tx", 0x601);
    this->declare_parameter("can_id_rx", 0x581);

    // Robot physical parameters
    // this->declare_parameter("wheel_diameter", 0.140);  // meters
    this->declare_parameter("wheel_diameter_left", 0.140);  // meters
    this->declare_parameter("wheel_diameter_right", 0.140);  // meters
    this->declare_parameter("wheel_separation", 0.390);  // meters
    this->declare_parameter("gear_ratio", 1.0);  // motor gear ratio
    this->declare_parameter("encoder_resolution", 16384.0);  // encoder ticks per revolution

    // Frame IDs
    this->declare_parameter("odom_frame_id", "odom");
    this->declare_parameter("base_frame_id", "base_link");
    this->declare_parameter("publish_tf", true);

    // Topic names
    this->declare_parameter("cmd_vel_topic", "cmd_vel");
    this->declare_parameter("odom_topic", "odom");
    this->declare_parameter("filtered_odom_topic", "filtered_odom");
    this->declare_parameter("can_tx_topic", "can_tx");
    this->declare_parameter("can_rx_topic", "");  // Will be set based on can_id_rx
    this->declare_parameter("publish_motor_info", false);  // Whether to publish motor info
    this->declare_parameter("motor_info_topic", "motor_info");
    this->declare_parameter("motor_state_topic", "motor_state");

    // Service names
    this->declare_parameter("check_dispatcher_node", false);
    this->declare_parameter("check_status_service", "check_node_status");
    this->declare_parameter("add_filter_service", "add_can_filter");

    // Control parameters
    this->declare_parameter("control_cycle_ms", 20);
    this->declare_parameter("print_status_out", false);
    this->declare_parameter("status_update_cycle_ms", 1000);  // Status update cycle for temps and currents
    this->declare_parameter("cmd_vel_timeout_ms", 500);  // Command velocity timeout in ms
    this->declare_parameter("bumper_timeout_ms", 5000);  // Bumper state timeout in ms
    this->declare_parameter("sdo_response_timeout_ms", 1000);  // SDO response timeout in ms
    

    // Socket CAN parameters
    this->declare_parameter("use_sockcan_direct", true);  // Use direct sockcan instead of dispatcher
    this->declare_parameter("can_interface", "can0");  // CAN interface name
    this->declare_parameter("min_send_interval_ms", 2);  // Minimum interval between CAN frame sends in ms

    // Get parameter values
    can_id_tx_ = this->get_parameter("can_id_tx").as_int();
    can_id_rx_ = this->get_parameter("can_id_rx").as_int();
    // wheel_diameter_ = this->get_parameter("wheel_diameter").as_double();
    // wheel_radius_ = wheel_diameter_ / 2.0;  // Calculate radius from diameter

    wheel_diameter_left_ = this->get_parameter("wheel_diameter_left").as_double();
    wheel_diameter_right_ = this->get_parameter("wheel_diameter_right").as_double();

    wheel_radius_left_ = wheel_diameter_left_ / 2.0;  // Calculate radius from diameter
    wheel_radius_right_ = wheel_diameter_right_ / 2.0;  // Calculate radius from diameter


    wheel_separation_ = this->get_parameter("wheel_separation").as_double();
    gear_ratio_ = this->get_parameter("gear_ratio").as_double();
    encoder_resolution_ = this->get_parameter("encoder_resolution").as_double();

    odom_frame_id_ = this->get_parameter("odom_frame_id").as_string();
    base_frame_id_ = this->get_parameter("base_frame_id").as_string();
    publish_tf_ = this->get_parameter("publish_tf").as_bool();
    print_status_ = this->get_parameter("print_status_out").as_bool();

    cmd_vel_topic_ = this->get_parameter("cmd_vel_topic").as_string();
    odom_topic_ = this->get_parameter("odom_topic").as_string();
    filtered_odom_topic_ = this->get_parameter("filtered_odom_topic").as_string();
    can_tx_topic_ = this->get_parameter("can_tx_topic").as_string();
    motor_info_topic_ = this->get_parameter("motor_info_topic").as_string();
    motor_state_topic_ = this->get_parameter("motor_state_topic").as_string();
    cmd_vel_timeout_ms_ = this->get_parameter("cmd_vel_timeout_ms").as_int();

    // Set can_rx_topic based on can_id_rx if not specified
    can_rx_topic_ = this->get_parameter("can_rx_topic").as_string();
    if (can_rx_topic_.empty()) {
        can_rx_topic_ = "can_rx_" + std::to_string(can_id_rx_);
    }

    // Bumper topic
    this->declare_parameter("bumper_topic", "bumper_state");
    bumper_topic_ = this->get_parameter("bumper_topic").as_string();

    check_status_service_ = this->get_parameter("check_status_service").as_string();
    add_filter_service_ = this->get_parameter("add_filter_service").as_string();

    control_cycle_ms_ = this->get_parameter("control_cycle_ms").as_int();
    status_update_cycle_ms_ = this->get_parameter("status_update_cycle_ms").as_int();
    check_dispatcher_node_ = this->get_parameter("check_dispatcher_node").as_bool();
    bumper_timeout_ms_ = this->get_parameter("bumper_timeout_ms").as_int();
    sdo_response_timeout_ms_ = this->get_parameter("sdo_response_timeout_ms").as_int();
    publish_motor_info_ = this->get_parameter("publish_motor_info").as_bool();

    // Get Socket CAN parameters
    use_sockcan_direct_ = this->get_parameter("use_sockcan_direct").as_bool();
    can_interface_ = this->get_parameter("can_interface").as_string();
    min_send_interval_ms_ = this->get_parameter("min_send_interval_ms").as_int();

    RCLCPP_INFO(this->get_logger(), "Parameters initialized:");
    RCLCPP_INFO(this->get_logger(), "  CAN ID TX: 0x%X", can_id_tx_);
    RCLCPP_INFO(this->get_logger(), "  CAN ID RX: 0x%X", can_id_rx_);
    // RCLCPP_INFO(this->get_logger(), "  Wheel diameter: %.4f m", wheel_diameter_);
    RCLCPP_INFO(this->get_logger(), "  Wheel separation: %.4f m", wheel_separation_);
    RCLCPP_INFO(this->get_logger(), "  Gear ratio: %.2f", gear_ratio_);
    RCLCPP_INFO(this->get_logger(), "  Encoder resolution: %.2f ticks/rev", encoder_resolution_);
    RCLCPP_INFO(this->get_logger(), "  Control cycle: %d ms", control_cycle_ms_);
    RCLCPP_INFO(this->get_logger(), "  Status update cycle: %d ms", status_update_cycle_ms_);
    RCLCPP_INFO(this->get_logger(), "  Check dispatcher node: %s", check_dispatcher_node_ ? "true" : "false");
    RCLCPP_INFO(this->get_logger(), "  Publish TF: %s", publish_tf_ ? "true" : "false");
    RCLCPP_INFO(this->get_logger(), "  Cmd vel timeout: %d ms", cmd_vel_timeout_ms_);
    RCLCPP_INFO(this->get_logger(), "  Bumper timeout: %d ms", bumper_timeout_ms_);
    RCLCPP_INFO(this->get_logger(), "  SDO response timeout: %d ms", sdo_response_timeout_ms_);
    RCLCPP_INFO(this->get_logger(), "  Publish motor info: %s", publish_motor_info_ ? "true" : "false");
    RCLCPP_INFO(this->get_logger(), "  Use sockcan direct: %s", use_sockcan_direct_ ? "true" : "false");
    RCLCPP_INFO(this->get_logger(), "  CAN interface: %s", can_interface_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Min send interval: %d ms", min_send_interval_ms_);
}

void ZLMotorController::setRealTimeProcessPriority()
{
    // Set process scheduling policy to SCHED_FIFO (real-time)
    struct sched_param param;
    param.sched_priority = 80;  // High priority (0-99, higher is more priority)

    #if 0

    if (pthread_setschedparam(pthread_self(), SCHED_FIFO, &param) != 0) {
        RCLCPP_WARN(this->get_logger(), "Failed to set thread priority: %s", strerror(errno));
    } else {
        RCLCPP_INFO(this->get_logger(), "Set thread to real-time priority");
    }


    // Set process priority
    if (setpriority(PRIO_PROCESS, 0, -20) != 0) {
        RCLCPP_WARN(this->get_logger(), "Failed to set process priority: %s", strerror(errno));
    } else {
        RCLCPP_INFO(this->get_logger(), "Set process to high priority");
    }
    #endif
}

void ZLMotorController::cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg)
{
    // Store the twist message for processing in the control loop
    {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        latest_cmd_vel_ = *msg;
    }
    last_cmd_vel_time_ = this->now();
}

void ZLMotorController::canRxCallback(const sl_vcu_all::msg::CanFrame::SharedPtr msg)
{
    // Process only frames from our motor controller
    if (msg->id == can_id_rx_) {
        processSDOResponse(msg);
    }
}

void ZLMotorController::bumperCallback(const sl_vcu_all::msg::BumperState::SharedPtr msg)
{
    bool front_state_changed = false;
    bool back_state_changed = false;

    {
        //std::lock_guard<std::mutex> lock(motor_mutex_);

        // Check for state changes (compare with current latest_bumper_state_)
        front_state_changed = (latest_bumper_state_.front_bumper_triggered != msg->front_bumper_triggered);
        back_state_changed = (latest_bumper_state_.back_bumper_triggered != msg->back_bumper_triggered);

        // Update states
        latest_bumper_state_ = *msg;
    }

    // Log state changes
    if (front_state_changed) {
        RCLCPP_INFO(this->get_logger(), "Front bumper state changed: %s",
                   msg->front_bumper_triggered ? "TRIGGERED" : "RELEASED");
    }

    if (back_state_changed) {
        RCLCPP_INFO(this->get_logger(), "Back bumper state changed: %s",
                   msg->back_bumper_triggered ? "TRIGGERED" : "RELEASED");
    }

    last_bumper_time_ = this->now();
}


void ZLMotorController::clearAlarmCallback(const std_msgs::msg::Bool::SharedPtr msg)
{
    if(msg->data)
    {
        // control_state_ = ControlState::CLEAR_ALARM;
        auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
        {
            (void) data;
            if (success) {
                control_state_ = ControlState::CLEAR_ALARM;
                state_counter_ = 0;
                RCLCPP_INFO(this->get_logger(), "Motor clear alarm command sent successfully");
            } else {
                RCLCPP_ERROR(this->get_logger(), "Failed to send motor Motor clear alarm command= 0x%02X , index= 0x%04X, subindex= 0x%02X", 
                            request->command, request->index, request->subindex);
                control_state_ = ControlState::ERROR;
            }
        };
        sendSDO(ZL_INDEX_CTRL, ZL_SUBINDEX_DEFAULT, ZL_CTRL_ALARM_CLEAR, SDO_WRITE_REQUEST_2BYTE, callback);
    }
    RCLCPP_INFO(this->get_logger(), "Received: %s", msg->data ? "true" : "false");
}

void ZLMotorController::filteredOdomCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
{
    // Extract orientation quaternion
    // tf2::Quaternion q(
    //     msg->pose.pose.orientation.x,
    //     msg->pose.pose.orientation.y,
    //     msg->pose.pose.orientation.z,
    //     msg->pose.pose.orientation.w);

    // Convert to Euler angles
    double roll, pitch, yaw;
    // tf2::Matrix3x3 m(q);
    // m.getRPY(roll, pitch, yaw);

    tf2::Quaternion tf_quat;
    tf2::fromMsg(msg->pose.pose.orientation, tf_quat);
    tf_quat.normalize();

    tf2::Matrix3x3(tf_quat).getRPY(roll, pitch, yaw);

    {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        // Update theta with the yaw from filtered odometry
        theta_ = yaw;
        // Normalize to [-pi, pi]
        theta_ = normalizeAngle(theta_);
    }

    RCLCPP_DEBUG(this->get_logger(), "Updated theta from filtered odometry: %.4f", theta_);
}

void ZLMotorController::filteredImuCallback(const sensor_msgs::msg::Imu::SharedPtr msg)
{
    // Extract orientation quaternion
    // tf2::Quaternion q(
    //     msg->pose.pose.orientation.x,
    //     msg->pose.pose.orientation.y,
    //     msg->pose.pose.orientation.z,
    //     msg->pose.pose.orientation.w);

    // Convert to Euler angles
    double roll, pitch, yaw;
    // tf2::Matrix3x3 m(q);
    // m.getRPY(roll, pitch, yaw);

    tf2::Quaternion tf_quat;
    tf2::fromMsg(msg->orientation, tf_quat);
    tf_quat.normalize();

    tf2::Matrix3x3(tf_quat).getRPY(roll, pitch, yaw);

    {
        std::lock_guard<std::mutex> lock(motor_mutex_);
        // Update theta with the yaw from filtered odometry
        theta_ = yaw;
        // Normalize to [-pi, pi]
        theta_ = normalizeAngle(theta_);
    }

    RCLCPP_DEBUG(this->get_logger(), "Updated theta from filtered Imu: %.4f", theta_);
}


void ZLMotorController::checkDispatcherNode()
{
    if (!check_dispatcher_node_) {
        // Skip check if disabled
        dispatcher_ready_ = true;
        control_state_ = ControlState::REGISTER_FILTER;
        return;
    }

    if (!check_status_client_->wait_for_service(std::chrono::milliseconds(100))) {
        RCLCPP_WARN(this->get_logger(), "CAN Frame Dispatcher service not available, waiting...");
        return;
    }

    auto request = std::make_shared<sl_vcu_all::srv::CheckNodeStatus::Request>();

    auto future = check_status_client_->async_send_request(
        request,
        [this](rclcpp::Client<sl_vcu_all::srv::CheckNodeStatus>::SharedFuture future) {
        auto response = future.get();
        dispatcher_ready_ = response->is_ready;

        if (dispatcher_ready_) {
            RCLCPP_INFO(this->get_logger(), "CAN Frame Dispatcher is ready: %s", response->status.c_str());
            control_state_ = ControlState::REGISTER_FILTER;
            state_counter_ = 0;
        } else {
            RCLCPP_ERROR(this->get_logger(), "CAN Frame Dispatcher not ready: %s", response->status.c_str());
        }
        });
}

void ZLMotorController::registerCanFilter()
{
    if (  !check_dispatcher_node_) {
        // Skip registration if dispatcher check is disabled
        // Create CAN frame subscriber directly
        if (!can_rx_sub_)
        {
            can_rx_sub_ = this->create_subscription<sl_vcu_all::msg::CanFrame>(
            can_rx_topic_, 10,
            std::bind(&ZLMotorController::canRxCallback, this, std::placeholders::_1));
        }

        control_state_ = ControlState::SET_MODE;
        state_counter_ = 0;
        return;
    }

    if (!add_filter_client_->wait_for_service(std::chrono::milliseconds(100))) {
        RCLCPP_WARN(this->get_logger(), "Add CAN Filter service not available, waiting...");
        return;
    }

    auto request = std::make_shared<sl_vcu_all::srv::AddCanFilter::Request>();
    request->can_id = can_id_rx_;
    request->topic_name = can_rx_topic_;

    auto future = add_filter_client_->async_send_request(
        request,
        [this](rclcpp::Client<sl_vcu_all::srv::AddCanFilter>::SharedFuture future) {
        auto response = future.get();

        if (response->success) {
            RCLCPP_INFO(this->get_logger(), "Successfully registered CAN filter: %s", response->message.c_str());

            // Create CAN frame subscriber
            if (!can_rx_sub_)
            {
                can_rx_sub_ = this->create_subscription<sl_vcu_all::msg::CanFrame>(
                can_rx_topic_, 10,
                std::bind(&ZLMotorController::canRxCallback, this, std::placeholders::_1));
            }

            control_state_ = ControlState::SET_MODE;
            state_counter_ = 0;
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to register CAN filter: %s", response->message.c_str());
            control_state_ = ControlState::ERROR;
        }
        });
}

void ZLMotorController::publishMotorInfo()
{
    auto msg = sl_vcu_all::msg::MotorInfo();
    msg.header.stamp = this->now();
    msg.left_current = left_current_/10.0;
    msg.right_current = right_current_/10.0;
    msg.left_temp = left_temp_/10.0;
    msg.right_temp = right_temp_/10.0;
    msg.driver_temp = driver_temp_/10.0;
    msg.left_pos_encoder = left_pos_;
    msg.right_pos_encoder = right_pos_;
    msg.alarm_code = last_alarm_;
    motor_info_pub_->publish(msg);
}

void ZLMotorController::publishMotorState()
{
    auto msg = sl_vcu_all::msg::MotorState();
    msg.header.stamp = this->now();

    // Set brake and emergency status based on GPIO bits
    msg.brake_release = (gpio_status_ & GPIO_BRAKE_RELEASE) != 0;
    msg.emergency_stop = ((gpio_status_ & GPIO_EMERGENCY_BIT0) != 0) || ((gpio_status_ & GPIO_EMERGENCY_BIT1) != 0);

    // Set motor state based on control state and alarm status
    if (last_alarm_ != 0) {
        msg.state = "error";
        msg.error_code = static_cast<int32_t>(last_alarm_);

        // Format alarm code as hex string
        std::stringstream ss;
        ss << "Motor alarm detected: 0x" << std::hex << std::uppercase << last_alarm_;
        msg.error_info = ss.str();
    } else if (control_state_ == ControlState::RUNNING && motors_enabled_) {
        msg.state = "running";
        msg.error_code = 0;
        msg.error_info = "";
    } else {
        msg.state = "error";
        msg.error_code = 0;
        msg.error_info = "Motor not running or not enabled";
    }

    motor_state_pub_->publish(msg);
}


void ZLMotorController::controlTimerCallback()
{
    //std::lock_guard<std::mutex> lock(motor_mutex_);

    // Check for SDO response timeouts
    checkSdoTimeouts();

    // Check for cmd_vel timeout
    if ((control_state_ == ControlState::RUNNING) && motors_enabled_) {
        auto now = this->now();
        auto elapsed = (now - last_cmd_vel_time_).nanoseconds() / 1000000;  // Convert to ms

        if (elapsed > cmd_vel_timeout_ms_) 
        {
            RCLCPP_DEBUG(this->get_logger(), "Command velocity timeout, stopping motors");
            //setMotorSpeeds(0.0, 0.0);
            latest_cmd_vel_.linear.x = 0.0;
            latest_cmd_vel_.angular.z = 0.0;
        }
    }
    else
    {
        latest_cmd_vel_.linear.x = 0.0;
        latest_cmd_vel_.angular.z = 0.0;
    }

    // Check for bumper timeout
    {
        auto now = this->now();
        auto elapsed = (now - last_bumper_time_).nanoseconds() / 1000000;  // Convert to ms

        if (elapsed > bumper_timeout_ms_) {
            RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                "Bumper state timeout, assuming safe state");
            // Reset bumper state to safe (not triggered) when timeout occurs
            latest_bumper_state_.front_bumper_triggered = false;
            latest_bumper_state_.back_bumper_triggered = false;
        }
    }


    // Process the latest cmd_vel if in running state
    if ((control_state_ == ControlState::RUNNING) && motors_enabled_ )
    {
        // Convert twist message to differential drive wheel speeds
        double linear_velocity = latest_cmd_vel_.linear.x;  // m/s
        double angular_velocity = latest_cmd_vel_.angular.z;  // rad/s

        // Calculate wheel velocities using differential drive kinematics
        double left_wheel_linear = linear_velocity - (angular_velocity * wheel_separation_ / 2.0);
        double right_wheel_linear = linear_velocity + (angular_velocity * wheel_separation_ / 2.0);

        // Convert linear velocity to angular velocity (rad/s)
        // double left_wheel_angular = left_wheel_linear / wheel_radius_;
        // double right_wheel_angular = right_wheel_linear / wheel_radius_;

        double left_wheel_angular = left_wheel_linear / wheel_radius_left_;
        double right_wheel_angular = right_wheel_linear / wheel_radius_right_;

        // Convert to RPM
        double left_rpm = radPerSecToRpm(left_wheel_angular);
        double right_rpm = radPerSecToRpm(right_wheel_angular);

        // Check for emergency stop or brake release - filter speed to 0
        bool emergency_stop = (gpio_status_ & GPIO_EMERGENCY_BIT0) || (gpio_status_ & GPIO_EMERGENCY_BIT1);
        bool brake_release = (gpio_status_ & GPIO_BRAKE_RELEASE);

        if (emergency_stop || brake_release) {
            left_rpm = 0.0;
            right_rpm = 0.0;
        }

        // Set motor speeds
        // check bumper state and log warnings if speed is limited
        bool front_speed_limited = false;
        bool back_speed_limited = false;

        if (latest_bumper_state_.front_bumper_triggered)
        {
            if (left_rpm > 0)
            {
                left_rpm = 0;
                front_speed_limited = true;
            }
            if (right_rpm > 0)
            {
                right_rpm = 0;
                front_speed_limited = true;
            }
        }
        if (latest_bumper_state_.back_bumper_triggered)
        {
            if (left_rpm < 0)
            {
                left_rpm = 0;
                back_speed_limited = true;
            }
            if (right_rpm < 0)
            {
                right_rpm = 0;
                back_speed_limited = true;
            }
        }

        // Log warnings every 1 second if speed is limited due to bumper triggers
        if (front_speed_limited) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                "Forward movement limited due to front bumper trigger");
        }

        if (back_speed_limited) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                "Backward movement limited due to back bumper trigger");
        }

        setMotorSpeeds(left_rpm, right_rpm);
    }



    // State machine for initialization and control
    switch (control_state_) {
        case ControlState::INIT:
        {
            // Initial state, wait for a few cycles before starting
            if (state_counter_++ > 10) {
                if (use_sockcan_direct_) {
                    // Skip dispatcher checks when using direct sockcan
                    control_state_ = ControlState::SET_MODE;
                    RCLCPP_INFO(this->get_logger(), "Using direct SocketCAN, skipping dispatcher registration");
                } else if (check_dispatcher_node_) {
                    control_state_ = ControlState::CHECK_DISPATCHER;
                    RCLCPP_INFO(this->get_logger(), "Checking CAN Frame Dispatcher status");
                } else {
                    control_state_ = ControlState::REGISTER_FILTER;
                }
                state_counter_ = 0;
            }
        }
        break;
        case ControlState::CHECK_DISPATCHER:
        {
            // Check if the dispatcher node is ready
            checkDispatcherNode();
            // State transition happens in the callback
            state_counter_++;
            if (state_counter_ > 50) {  // Timeout after ~5 seconds
                RCLCPP_ERROR(this->get_logger(), "Timeout waiting for CAN Frame Dispatcher");
                control_state_ = ControlState::ERROR;
                state_counter_ = 0;
            }
            break;
        }
        case ControlState::REGISTER_FILTER:
        {
            // Register the CAN filter with the dispatcher
            registerCanFilter();
            // State transition happens in the callback
            state_counter_++;
            if (state_counter_ > 50) {  // Timeout after ~5 seconds
                RCLCPP_ERROR(this->get_logger(), "Timeout registering CAN filter");
                control_state_ = ControlState::ERROR;
                state_counter_ = 0;
            }
        }
        break;
        case ControlState::SET_MODE:
        {
            // Set speed mode
            setSpeedMode();
            // State transition happens in the callback
            if (state_counter_++ > 5) {
                control_state_ = ControlState::ERROR;
                state_counter_ = 0;
                RCLCPP_INFO(this->get_logger(), "Motors ready");
            }
        }
        break;
        case ControlState::ENABLE_MOTORS_STEP_1:
        {
            // Enable motors
            enableMotors_step_1();
            // State transition happens in the callback
            if (state_counter_++ > 5) {
                control_state_ = ControlState::ERROR;
                state_counter_ = 0;
            }
        }
        break;
        case ControlState::ENABLE_MOTORS_STEP_2:
        {
            // Enable motors
            enableMotors_step_2();
            // State transition happens in the callback
            if (state_counter_++ > 5) {
                control_state_ = ControlState::ERROR;
                state_counter_ = 0;
            }
        }
        break;
        case ControlState::ENABLE_MOTORS_STEP_3:
        {
            // Enable motors
            enableMotors_step_3();
            // State transition happens in the callback
            if (state_counter_++ > 5) {
                control_state_ = ControlState::ERROR;
                state_counter_ = 0;
            }
        }
        break;
        case ControlState::RUNNING:
        {
            // Normal operation - update status and odometry

            // Always update positions every cycle
            updateMotorPositions();

            // Update speeds every cycle
            updateMotorSpeeds();

            // Update GPIO status every cycle
            updateGpioStatus();

            // Update last alarm every cycle
            updateLastAlarm();

            // Update and publish odometry
            updateOdometry();
            publishOdometry();

            // Increment cycle counter
            cycle_counter_ = 0; // Reset cycle counter
        }
        break;
        case ControlState::CLEAR_ALARM:
        {
            control_state_ = ControlState::INIT;
            state_counter_ = 0;
            disableMotors();
            RCLCPP_INFO(this->get_logger(), "Attempting to clear the alarm %08X", last_alarm_);
            last_alarm_ = 0;
        }
        break;
        case ControlState::ERROR:
        {
            // Error state, try to recover after a delay
            if (state_counter_++ > 100) {  // Wait longer in error state
                control_state_ = ControlState::INIT;
                state_counter_ = 0;
                disableMotors();
                RCLCPP_INFO(this->get_logger(), "Attempting to recover from error");
            }
        }
        break;
        default:
        {
            RCLCPP_ERROR(this->get_logger(), "Unknown control state: %d", static_cast<int>(control_state_));
        }
    }

}

void ZLMotorController::statusTimerCallback()
{
    // Only update temperatures and currents when in running state
    if (control_state_ == ControlState::RUNNING)
    {
        // Update motor currents
        updateMotorCurrents();

        // Update motor temperatures
        updateMotorTemperatures();

        // Publish motor info if enabled
        if (publish_motor_info_) {
            publishMotorInfo();
        }
    }

    // Always publish motor state (regardless of running state)
    publishMotorState();
}


bool ZLMotorController::sendSDO(uint16_t index, uint8_t subindex, uint32_t data, uint8_t command, SdoCallback callback)
{
    auto frame = std::make_unique<sl_vcu_all::msg::CanFrame>();

    frame->id = can_id_tx_;
    frame->is_rtr = false;
    frame->is_extended = false;
    frame->is_error = false;
    frame->dlc = 8;

    // Fill the CAN frame data according to CANopen SDO protocol
    frame->data[0] = command;
    frame->data[1] = static_cast<uint8_t>(index & 0xFF);
    frame->data[2] = static_cast<uint8_t>((index >> 8) & 0xFF);
    frame->data[3] = subindex;
    frame->data[4] = static_cast<uint8_t>(data & 0xFF);
    frame->data[5] = static_cast<uint8_t>((data >> 8) & 0xFF);
    frame->data[6] = static_cast<uint8_t>((data >> 16) & 0xFF);
    frame->data[7] = static_cast<uint8_t>((data >> 24) & 0xFF);

    // Add to pending requests list for timeout tracking
    if (command == SDO_READ_REQUEST || command == SDO_WRITE_REQUEST_1BYTE ||
        command == SDO_WRITE_REQUEST_2BYTE || command == SDO_WRITE_REQUEST_4BYTE) {

        if (callback)
        // if (true)
        {
            bool is_already_in_pending = true;
            auto request = std::make_shared<SdoRequest>();

            {
                std::lock_guard<std::mutex> lock(sdo_mutex_);

                // Check if there is already a pending request for this index/subindex
                auto it = std::find_if(pending_sdo_requests_.begin(), pending_sdo_requests_.end(),
                                    [index, subindex](const std::shared_ptr<SdoRequest>& req) {
                                        return req->index == index && req->subindex == subindex && req->awaiting_response;
                                    });

                // Only add if there isn't already a pending request for this index/subindex
                if (it == pending_sdo_requests_.end())
                {
                    is_already_in_pending = false;
                    //auto request = std::make_shared<SdoRequest>();
                    request->index = index;
                    request->subindex = subindex;
                    request->command = command;
                    request->data = data;  // Store the data for reference
                    request->timestamp = this->now();  // Use ROS time instead of std::chrono
                    request->awaiting_response = true;
                    request->callback = callback;
                    pending_sdo_requests_.push_back(request);
                }
                else
                {
                    request = *it;
                }
            }

            if (!is_already_in_pending)
            {
                // Send frame via sockcan or ROS topic
                if (use_sockcan_direct_) {
                    return queueCanFrameForSend(*frame);
                } else {
                    can_tx_pub_->publish(std::move(frame));

                    RCLCPP_DEBUG(this->get_logger(), "SDO request %02X %02X %02X %02X %02X %02X %02X %02X ",
                             command, index & 0xFF, (index >> 8) & 0xFF, subindex, data & 0xFF, (data >> 8) & 0xFF, (data >> 16) & 0xFF, (data >> 24) & 0xFF);
                    return true;
                }
            }
            else
            {
                RCLCPP_WARN(this->get_logger(), "SDO request already in progress, last ts: %f, now: %f, delta: %f : %02X %02X %02X %02X %02X %02X %02X %02X ",
                         request->timestamp.seconds(), this->now().seconds(), (this->now() - request->timestamp).seconds(), frame->data[0], frame->data[1], frame->data[2], frame->data[3], frame->data[4], frame->data[5], frame->data[6], frame->data[7]);
            }
        }
        else
        {
            // Send frame via sockcan or ROS topic (no callback case)
            if (use_sockcan_direct_) {
                return queueCanFrameForSend(*frame);
            } else {
                can_tx_pub_->publish(std::move(frame));
                return true;
            }
        }
    }

    return false;
}

void ZLMotorController::checkSdoTimeouts()
{
    std::lock_guard<std::mutex> lock(sdo_mutex_);
    auto now = this->now();  // Use ROS time

    auto it = pending_sdo_requests_.begin();
    while (it != pending_sdo_requests_.end()) {
        // Only check timeout for requests that are awaiting response
        if ((*it)->awaiting_response) {
            auto elapsed = (now - (*it)->timestamp).nanoseconds() / 1000000;  // Convert to milliseconds

            if (elapsed > sdo_response_timeout_ms_) {
                RCLCPP_ERROR(this->get_logger(), "SDO timeout st: %f now: %f delta: %ld: index=0x%04X, subindex=0x%02X, command=0x%02X",
                        (*it)->timestamp.seconds(), now.seconds(), elapsed, (*it)->index, (*it)->subindex, (*it)->command);

                // Call the timeout callback if one was provided
                if ((*it)->callback) {
                    (*it)->callback(false, *it, 0);
                }

                // Remove the timed out request
                it = pending_sdo_requests_.erase(it);

                // If in initialization states, go to error state
                if (control_state_ != ControlState::RUNNING) {
                    control_state_ = ControlState::ERROR;
                    state_counter_ = 0;
                }
            } else {
                ++it;
            }
        } else {
            ++it;
        }
    }
}

void ZLMotorController::processSDOResponse(const sl_vcu_all::msg::CanFrame::SharedPtr msg)
{
    //std::lock_guard<std::mutex> lock(motor_mutex_);

    uint8_t command = msg->data[0];
    uint16_t index = static_cast<uint16_t>(msg->data[1]) | (static_cast<uint16_t>(msg->data[2]) << 8);
    uint8_t subindex = msg->data[3];
    uint32_t data = 0;


    if (msg->dlc == 8)
    {
        // 4 byte data
        data = static_cast<uint32_t>(msg->data[4]) |
                (static_cast<uint32_t>(msg->data[5]) << 8) |
                (static_cast<uint32_t>(msg->data[6]) << 16) |
                (static_cast<uint32_t>(msg->data[7]) << 24);
    }
    else if (msg->dlc >= 6)
    {
        // 2 byte data
        data = static_cast<uint32_t>(msg->data[4]) |
            (static_cast<uint32_t>(msg->data[5]) << 8);
    }
    else
    {
        // 1 byte data
        data = static_cast<uint32_t>(msg->data[4]);
    }

    std::shared_ptr<SdoRequest> request = nullptr;
    bool success = false;

    // Remove from pending requests list
    {
        std::lock_guard<std::mutex> lock(sdo_mutex_);
        auto it = std::find_if(pending_sdo_requests_.begin(), pending_sdo_requests_.end(),
                            [index, subindex](const std::shared_ptr<SdoRequest>& req) {
                                return req->index == index && req->subindex == subindex && req->awaiting_response;
                            });

        if (it != pending_sdo_requests_.end()) 
        {
            request = *it;
            pending_sdo_requests_.erase(it);
        }
    }


    // Check if this was a control command that affects motor enabled state
    if (index == ZL_INDEX_CTRL && (command == SDO_WRITE_RESPONSE_OK)) {
        if (request->data == ZL_CTRL_ENABLE || request->data == ZL_CTRL_SPEED_ENABLE) {
            motors_enabled_ = true;
        } else if (request->data == ZL_CTRL_RELEASE) {
            motors_enabled_ = false;
        }
    }

    // Determine success based on response command
    success = (IS_SDO_READ_RESPONSE_OK(command) || command == SDO_WRITE_RESPONSE_OK);


    // Check for error responses
    if (command == SDO_READ_RESPONSE_ERROR || command == SDO_WRITE_RESPONSE_ERROR) {
        RCLCPP_ERROR(this->get_logger(), "SDO error response: index=0x%04X, subindex=0x%02X, error=0x%08X",
                     index, subindex, data);
        if (control_state_ != ControlState::RUNNING) {
            control_state_ = ControlState::ERROR;
            state_counter_ = 0;
        }
    }

#if 0
    // Process the response based on the index
    switch (index) {
        case ZL_INDEX_POS:
            if (subindex == ZL_SUBINDEX_LEFT) {
                left_pos_ = static_cast<int32_t>(data);
            } else if (subindex == ZL_SUBINDEX_RIGHT) {
                right_pos_ = static_cast<int32_t>(data);
            }
            break;
        case ZL_INDEX_SPEED:
            if (subindex == ZL_SUBINDEX_L_R) {
                // High 16 bits are right motor, low 16 bits are left motor
                left_speed_ = static_cast<int16_t>(data & 0xFFFF);
                right_speed_ = static_cast<int16_t>(data >> 16);
            } else if (subindex == ZL_SUBINDEX_LEFT) {
                left_speed_ = static_cast<int32_t>(data);
            } else if (subindex == ZL_SUBINDEX_RIGHT) {
                right_speed_ = static_cast<int32_t>(data);
            }
            break;
        case ZL_INDEX_CURRENT_FB:
            if (subindex == ZL_SUBINDEX_CURRENT_FB_LEFT) {
                left_current_ = static_cast<int16_t>(data);
            } else if (subindex == ZL_SUBINDEX_CURRENT_FB_RIGHT) {
                right_current_ = static_cast<int16_t>(data);
            }
            break;
        case ZL_INDEX_DIN_STATUS:
            if (subindex == ZL_SUBINDEX_DEFAULT) {
                gpio_status_ = data;
            }
            break;
        case ZL_INDEX_TEMPERATURE:
            if (subindex == ZL_SUBINDEX_TEMPERATURE_DRIVER) {
                driver_temp_ = static_cast<int16_t>(data);
            } else if (subindex == ZL_SUBINDEX_TEMPERATURE_LEFT) {
                left_temp_ = static_cast<int16_t>(data);
            } else if (subindex == ZL_SUBINDEX_TEMPERATURE_RIGHT) {
                right_temp_ = static_cast<int16_t>(data);
            }
            break;
        case ZL_INDEX_LAST_ALARM:
            last_alarm_ = static_cast<uint32_t>(data);
            if (last_alarm_ != 0) {
                RCLCPP_ERROR(this->get_logger(), "Last alarm code: 0x%08X", last_alarm_);
            }
            break;
        default:
            // Unknown index
            break;
    }
#endif

    // Call the callback if one was provided and we found a matching request
    if (request && request->callback) {
        request->callback(success, request, data);
    }
}

void ZLMotorController::enableMotors_step_1()
{
    // Send control word to enable motors step 1 with callback to track success
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) data;
        if (success) {
            control_state_ = ControlState::ENABLE_MOTORS_STEP_2;
            state_counter_ = 0;
            RCLCPP_INFO(this->get_logger(), "Motor enableMotors_step_1 command sent successfully");
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to send motor enableMotors_step_1 command= 0x%02X , index= 0x%04X, subindex= 0x%02X", 
                            request->command, request->index, request->subindex);
            control_state_ = ControlState::ERROR;
        }
    };
    sendSDO(ZL_INDEX_CTRL, ZL_SUBINDEX_DEFAULT, ZL_CTRL_RELEASE, SDO_WRITE_REQUEST_2BYTE, callback);

    // Set acceleration and deceleration times
    // sendSDO(ZL_INDEX_ACC_SPEED_TIME, ZL_SUBINDEX_LEFT, 5, SDO_WRITE_REQUEST_4BYTE);  // 5ms
    // sendSDO(ZL_INDEX_ACC_SPEED_TIME, ZL_SUBINDEX_RIGHT, 5, SDO_WRITE_REQUEST_4BYTE);  // 5ms
    // sendSDO(ZL_INDEX_DEC_SPEED_TIME, ZL_SUBINDEX_LEFT, 5, SDO_WRITE_REQUEST_4BYTE);  // 5ms
    // sendSDO(ZL_INDEX_DEC_SPEED_TIME, ZL_SUBINDEX_RIGHT, 5, SDO_WRITE_REQUEST_4BYTE);  // 5ms
}

void ZLMotorController::enableMotors_step_2()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) data;
        if (success) {
            control_state_ = ControlState::ENABLE_MOTORS_STEP_3;
            state_counter_ = 0;
            RCLCPP_INFO(this->get_logger(), "Motor enableMotors_step_2 command sent successfully");
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to send motor enableMotors_step_2 command= 0x%02X , index= 0x%04X, subindex= 0x%02X", 
                            request->command, request->index, request->subindex);
            control_state_ = ControlState::ERROR;
        }
    };
    // Send control word to enable motors step 1
    sendSDO(ZL_INDEX_CTRL, ZL_SUBINDEX_DEFAULT, ZL_CTRL_ENABLE, SDO_WRITE_REQUEST_2BYTE, callback);
}

void ZLMotorController::enableMotors_step_3()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) data;
        if (success) {
            control_state_ = ControlState::RUNNING;
            state_counter_ = 0;
            RCLCPP_INFO(this->get_logger(), "Motor enableMotors_step_3 command sent successfully");
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to send motor enableMotors_step_3 command= 0x%02X , index= 0x%04X, subindex= 0x%02X", 
                            request->command, request->index, request->subindex);
            control_state_ = ControlState::ERROR;
        }
    };
    // Send control word to enable motors step 1
    sendSDO(ZL_INDEX_CTRL, ZL_SUBINDEX_DEFAULT, ZL_CTRL_SPEED_ENABLE, SDO_WRITE_REQUEST_2BYTE, callback);
}




void ZLMotorController::disableMotors()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) data;
        if (success) {
            //control_state_ = ControlState::RUNNING;
            state_counter_ = 0;
            motors_enabled_ = false;
            RCLCPP_INFO(this->get_logger(), "Motor disableMotors command sent successfully");
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to send motor disableMotors command= 0x%02X , index= 0x%04X, subindex= 0x%02X", 
                            request->command, request->index, request->subindex);
            control_state_ = ControlState::ERROR;
        }
    };
    // Send control word to disable motors
    sendSDO(ZL_INDEX_CTRL, ZL_SUBINDEX_DEFAULT, ZL_CTRL_RELEASE, SDO_WRITE_REQUEST_2BYTE, callback);
    motors_enabled_ = false;

}

void ZLMotorController::setSpeedMode()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) data;
        if (success) {
            control_state_ = ControlState::ENABLE_MOTORS_STEP_1;
            state_counter_ = 0;
            RCLCPP_INFO(this->get_logger(), "Motor setSpeedMode command sent successfully");
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to send motor setSpeedMode command= 0x%02X , index= 0x%04X, subindex= 0x%02X", 
                            request->command, request->index, request->subindex);
            control_state_ = ControlState::ERROR;
        }
    };
    // Set motors to speed mode
    sendSDO(ZL_INDEX_RUN_MODE, ZL_SUBINDEX_DEFAULT, ZL_RUN_MODE_SPEED, SDO_WRITE_REQUEST_1BYTE, callback);

    // Enable speed control
    //sendSDO(ZL_INDEX_CTRL, ZL_SUBINDEX_DEFAULT, ZL_CTRL_SPEED_ENABLE, SDO_WRITE_REQUEST_2BYTE);
}

void ZLMotorController::setMotorSpeeds(double left_speed_rpm, double right_speed_rpm)
{
    if (control_state_ != ControlState::RUNNING) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000, "Motors not in running state");
        return;
    }

    if (!motors_enabled_) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000, "Motors not enabled");
        return;
    }

    // Convert RPM to encoder counts
    int32_t left_encoder_speed = rpmToEncoderSpeed(left_speed_rpm);
    // Right motor speed is negative when moving forward
    int32_t right_encoder_speed = rpmToEncoderSpeed(-right_speed_rpm);

    uint32_t combined_speed = (static_cast<uint32_t>(right_encoder_speed) << 16) | static_cast<uint32_t>(left_encoder_speed & 0x0000FFFF);

    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) data;
        if (success) {
            RCLCPP_DEBUG(this->get_logger(), "Motor setMotorSpeeds command sent successfully");
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to send motor setMotorSpeeds command= 0x%02X , index= 0x%04X, subindex= 0x%02X", 
                            request->command, request->index, request->subindex);
        }
    };


    sendSDO(ZL_INDEX_DEST_SPEED, ZL_SUBINDEX_L_R, combined_speed, SDO_WRITE_REQUEST_4BYTE, callback);

    RCLCPP_DEBUG(this->get_logger(), "send speed rpm: L:%04X R:%04X L_R:%08X",
        left_encoder_speed, right_encoder_speed, combined_speed);


    //sendSDO(ZL_INDEX_DEST_SPEED, ZL_SUBINDEX_L_R, combined_speed, SDO_WRITE_REQUEST_4BYTE);

    // Set left motor speed
    //sendSDO(ZL_INDEX_DEST_SPEED, ZL_SUBINDEX_LEFT, static_cast<uint32_t>(left_encoder_speed), SDO_WRITE_REQUEST_4BYTE);

    // Set right motor speed
    //sendSDO(ZL_INDEX_DEST_SPEED, ZL_SUBINDEX_RIGHT, static_cast<uint32_t>(right_encoder_speed), SDO_WRITE_REQUEST_4BYTE);
}

void ZLMotorController::emergencyStop()
{
    // Send emergency stop command
    setMotorSpeeds(0.0, 0.0);
}

void ZLMotorController::updateMotorPositions()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        bool is_left = true;
        if (success) {
            if (request->subindex == ZL_SUBINDEX_LEFT) {
                left_pos_ = static_cast<int32_t>(data);
                is_left = true;
            } else if (request->subindex == ZL_SUBINDEX_RIGHT) {
                right_pos_ = static_cast<int32_t>(data);
                is_left = false;
            }
            std::string side = is_left ? "Left " : "Right ";
            std::string str = side + "motor position updated successfully";
            RCLCPP_DEBUG(this->get_logger(), str.c_str());
        } else {

            std::string side = is_left ? "Left " : "Right ";
            std::string str = "Failed to update " + side + "motor position";
            RCLCPP_WARN(this->get_logger(), str.c_str());
        }
    };
    // Request left motor position with callback to update internal data

    sendSDO(ZL_INDEX_POS, ZL_SUBINDEX_LEFT, 0, SDO_READ_REQUEST, callback);

    // Request right motor position with callback to update internal data
    sendSDO(ZL_INDEX_POS, ZL_SUBINDEX_RIGHT, 0, SDO_READ_REQUEST, callback);
}

void ZLMotorController::updateMotorSpeeds()
{
    // Request motor speeds (both in one command) with callback to update internal data
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) request;
        if (success) {
            // High 16 bits are right motor, low 16 bits are left motor
            left_speed_ = static_cast<int16_t>(data & 0xFFFF);
            right_speed_ = static_cast<int16_t>(data >> 16);
            RCLCPP_DEBUG(this->get_logger(), "Motor speeds updated successfully: left=%d, right=%d", left_speed_, right_speed_);
        } else {
            RCLCPP_WARN(this->get_logger(), "Failed to update motor speeds");
        }
    };
    sendSDO(ZL_INDEX_SPEED, ZL_SUBINDEX_L_R, 0, SDO_READ_REQUEST, callback);
}

void ZLMotorController::updateLastAlarm()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) request;

        bool is_changed = false;

        if (success) {
            if (last_alarm_ != data)
            {
                is_changed = true;
            }
            last_alarm_ = data;
            RCLCPP_DEBUG(this->get_logger(), "Last alarm code updated successfully: 0x%08X", last_alarm_);
        } else {
            RCLCPP_WARN(this->get_logger(), "Failed to update last alarm code");
        }

        if (is_changed)
        {
            RCLCPP_INFO(this->get_logger(), "Last alarm code changed, now is 0x%08X", last_alarm_);
            is_changed = false;
        }
    };

    // Request last alarm code
    sendSDO(ZL_INDEX_LAST_ALARM, ZL_SUBINDEX_LAST_ALARM, 0, SDO_READ_REQUEST, callback);
}

void ZLMotorController::updateMotorCurrents()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        bool is_left = true;
        if (success) {
            if (request->subindex == ZL_SUBINDEX_CURRENT_FB_LEFT) {
                left_current_ = static_cast<int16_t>(data);
                is_left = true;
            } else if (request->subindex == ZL_SUBINDEX_CURRENT_FB_RIGHT) {
                right_current_ = static_cast<int16_t>(data);
                is_left = false;
            }
            std::string side = is_left ? "Left " : "Right ";
            std::string str = side + "motor current updated successfully";
            RCLCPP_DEBUG(this->get_logger(), str.c_str());
        } else {
            std::string side = is_left ? "Left " : "Right ";
            std::string str = "Failed to update " + side + "motor current";
            RCLCPP_WARN(this->get_logger(), str.c_str());
        }
    };
    // Request left motor current with callback to update internal data
    sendSDO(ZL_INDEX_CURRENT_FB, ZL_SUBINDEX_CURRENT_FB_LEFT, 0, SDO_READ_REQUEST, callback);
    sendSDO(ZL_INDEX_CURRENT_FB, ZL_SUBINDEX_CURRENT_FB_RIGHT, 0, SDO_READ_REQUEST, callback);
}

void ZLMotorController::updateMotorTemperatures()
{
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        std::string str = "";
        if (success) {
            if (request->subindex == ZL_SUBINDEX_TEMPERATURE_LEFT) {
                left_temp_ = static_cast<int16_t>(data);
                str = "Left motor temperature updated successfully";
            } else if (request->subindex == ZL_SUBINDEX_TEMPERATURE_RIGHT) {
                right_temp_ = static_cast<int16_t>(data);
                str = "Right motor temperature updated successfully";
            } else if (request->subindex == ZL_SUBINDEX_TEMPERATURE_DRIVER) {
                driver_temp_ = static_cast<int16_t>(data);
                str = "Driver motor temperature updated successfully";

                if(print_status_)
                {
                    RCLCPP_INFO(this->get_logger(), "Motor Current and T is: L: %4.2f A  %4.2f .C, R: %4.2f A  %4.2f .C, Drive T:  %4.2f .C"
                                                    , left_current_/10.0f, left_temp_/10.0f, right_current_/10.0f, right_temp_/10.0f, driver_temp_/10.0f);
                }
            }

            RCLCPP_DEBUG(this->get_logger(), str.c_str());
        } else {
            RCLCPP_WARN(this->get_logger(), "Failed to update motor temperature");
        }
    };


    // Request motor temperatures
    sendSDO(ZL_INDEX_TEMPERATURE, ZL_SUBINDEX_TEMPERATURE_LEFT, 0, SDO_READ_REQUEST, callback);
    sendSDO(ZL_INDEX_TEMPERATURE, ZL_SUBINDEX_TEMPERATURE_RIGHT, 0, SDO_READ_REQUEST, callback);
    sendSDO(ZL_INDEX_TEMPERATURE, ZL_SUBINDEX_TEMPERATURE_DRIVER, 0, SDO_READ_REQUEST, callback);
}


void ZLMotorController::updateGpioStatus()
{
    // Request GPIO status
    auto callback = [this](bool success, std::shared_ptr<SdoRequest> request, uint32_t data)
    {
        (void) request;

        if (success) {
            uint32_t old_gpio_status = gpio_status_;
            gpio_status_ = data;

            // Check for changes in specific GPIO bits
            bool emergency_bit0_changed = ((old_gpio_status & GPIO_EMERGENCY_BIT0) != (gpio_status_ & GPIO_EMERGENCY_BIT0));
            bool emergency_bit1_changed = ((old_gpio_status & GPIO_EMERGENCY_BIT1) != (gpio_status_ & GPIO_EMERGENCY_BIT1));
            bool brake_release_changed = ((old_gpio_status & GPIO_BRAKE_RELEASE) != (gpio_status_ & GPIO_BRAKE_RELEASE));

            // Log state changes for emergency signals (bit0 and bit1)
            if (emergency_bit0_changed) {
                bool is_triggered = (gpio_status_ & GPIO_EMERGENCY_BIT0) != 0;
                RCLCPP_INFO(this->get_logger(), "Emergency signal bit0 changed: %s",
                           is_triggered ? "TRIGGERED" : "RELEASED");
            }

            if (emergency_bit1_changed) {
                bool is_triggered = (gpio_status_ & GPIO_EMERGENCY_BIT1) != 0;
                RCLCPP_INFO(this->get_logger(), "Emergency signal bit1 changed: %s",
                           is_triggered ? "TRIGGERED" : "RELEASED");
            }

            // Log state changes for brake release signal (bit2)
            if (brake_release_changed) {
                bool is_triggered = (gpio_status_ & GPIO_BRAKE_RELEASE) != 0;
                RCLCPP_INFO(this->get_logger(), "Brake release signal changed: %s",
                           is_triggered ? "TRIGGERED" : "RELEASED");
            }

            // Log warnings every 1 second if emergency signals are triggered
            if (gpio_status_ & GPIO_EMERGENCY_BIT0) {
                RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                    "Emergency signal bit0 is triggered");
            }

            if (gpio_status_ & GPIO_EMERGENCY_BIT1) {
                RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                    "Emergency signal bit1 is triggered");
            }

            if (gpio_status_ & GPIO_BRAKE_RELEASE) {
                RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                    "Brake release signal is triggered");
            }

            RCLCPP_DEBUG(this->get_logger(), "GPIO status updated successfully: 0x%08X", gpio_status_);
        } else {
            RCLCPP_WARN(this->get_logger(), "Failed to update GPIO status");
        }
    };
    sendSDO(ZL_INDEX_DIN_STATUS, ZL_SUBINDEX_DEFAULT, 0, SDO_READ_REQUEST, callback);
}

void ZLMotorController::updateOdometry()
{
    //std::lock_guard<std::mutex> lock(motor_mutex_);

    // RCLCPP_DEBUG(this->get_logger(), "Odometry : x=%f, y=%f, theta=%f", x_, y_, theta_);


    rclcpp::Time current_time = this->now();
    double dt = (current_time - last_odom_time_).seconds();
    last_odom_time_ = current_time;

    if (dt == 0.0) {
        return;  // No time has passed
    }


    // Calculate position differences
    int32_t left_pos_diff = left_pos_ - left_pos_prev_;
    int32_t right_pos_diff = right_pos_ - right_pos_prev_;

    // Skip small position jumps (less than 2 encoder ticks)
    if ((std::abs(left_pos_diff) < 2) || (std::abs(right_pos_diff) < 2)) {
        // skip odometry calculation
        //left_pos_prev_ = left_pos_;
        //right_pos_prev_ = right_pos_;
        return;
    }

    // Calculate wheel rotations since last update
    double left_diff = encoderToRadians(left_pos_diff);
    // Right motor encoder position is negative when moving forward, so negate it
    double right_diff = -encoderToRadians(right_pos_diff);

    if (!is_last_left_pos_init_)
    {
        left_pos_prev_ = left_pos_;
        is_last_left_pos_init_ = true;
        return;
    }

    if (!is_last_right_pos_init_)
    {
        right_pos_prev_ = right_pos_;
        is_last_right_pos_init_ = true;
        return;
    }

    // Update previous positions
    left_pos_prev_ = left_pos_;
    right_pos_prev_ = right_pos_;

    // Calculate linear and angular displacement
    // double linear_displacement = (left_diff + right_diff) * wheel_radius_ / 2.0;
    // double angular_displacement = (right_diff - left_diff) * wheel_radius_ / wheel_separation_;

    double linear_displacement = (left_diff * wheel_radius_left_ + right_diff * wheel_radius_right_) / 2.0;
    double angular_displacement = (right_diff * wheel_radius_left_ - left_diff * wheel_radius_right_) / wheel_separation_;

    // Update pose
    // use second order runge-kutta method to update pose
    double theta_mid = theta_ + angular_displacement / 2.0;
    x_ += linear_displacement * cos(theta_mid);
    y_ += linear_displacement * sin(theta_mid);
    theta_ += angular_displacement;

    // Normalize theta to [-pi, pi]
    theta_ = normalizeAngle(theta_);
    RCLCPP_DEBUG(this->get_logger(), "Odometry : x=%f, y=%f, theta=%f", x_, y_, theta_);
}

void ZLMotorController::publishOdometry()
{
    // Create odometry message
    auto odom_msg = std::make_unique<nav_msgs::msg::Odometry>();

    // Set header
    odom_msg->header.stamp = this->now();
    odom_msg->header.frame_id = odom_frame_id_;
    odom_msg->child_frame_id = base_frame_id_;

    // Set position
    odom_msg->pose.pose.position.x = x_;
    odom_msg->pose.pose.position.y = y_;
    odom_msg->pose.pose.position.z = 0.0;

    // Set orientation using quaternion
    tf2::Quaternion q;
    q.setRPY(0.0, 0.0, theta_);
    odom_msg->pose.pose.orientation.x = q.x();
    odom_msg->pose.pose.orientation.y = q.y();
    odom_msg->pose.pose.orientation.z = q.z();
    odom_msg->pose.pose.orientation.w = q.w();

    // Set velocities
    double left_speed_rad_sec = encoderToRpm(left_speed_) * M_PI / 30.0;  // Convert RPM to rad/s
    // Right motor speed is negative when moving forward, so negate it
    double right_speed_rad_sec = -encoderToRpm(right_speed_) * M_PI / 30.0;

    double linear_velocity = (left_speed_rad_sec * wheel_radius_left_ + right_speed_rad_sec * wheel_radius_right_) / 2.0;
    double angular_velocity = (right_speed_rad_sec * wheel_radius_left_ - left_speed_rad_sec * wheel_radius_right_) / wheel_separation_;

    odom_msg->twist.twist.linear.x = linear_velocity;
    odom_msg->twist.twist.angular.z = angular_velocity;

    // Publish odometry message
    odom_pub_->publish(std::move(odom_msg));

    // Publish transform if enabled
    if (publish_tf_) {
        geometry_msgs::msg::TransformStamped transform_stamped;
        transform_stamped.header.stamp = this->now();
        transform_stamped.header.frame_id = odom_frame_id_;  // Using odom frame
        transform_stamped.child_frame_id = base_frame_id_;
        transform_stamped.transform.translation.x = x_;
        transform_stamped.transform.translation.y = y_;
        transform_stamped.transform.translation.z = 0.0;
        transform_stamped.transform.rotation.x = q.x();
        transform_stamped.transform.rotation.y = q.y();
        transform_stamped.transform.rotation.z = q.z();
        transform_stamped.transform.rotation.w = q.w();

        tf_broadcaster_->sendTransform(transform_stamped);
    }
}

// Utility methods
double ZLMotorController::rpmToRadPerSec(double rpm)
{
    return rpm * M_PI / 30.0;  // RPM to rad/s conversion
}

double ZLMotorController::radPerSecToRpm(double rad_per_sec)
{
    return rad_per_sec * 30.0 / M_PI;  // rad/s to RPM conversion
}

int32_t ZLMotorController::rpmToEncoderSpeed(double rpm)
{
    // Convert RPM to encoder counts per control cycle
    // The ZL motor driver expects speed in 0.1 RPM units
    // return static_cast<int32_t>(rpm * 10.0);
    return static_cast<int32_t>(rpm);
}

double ZLMotorController::encoderToRpm(int32_t encoder_speed)
{
    // Convert encoder speed to RPM
    // The ZL motor driver reports speed in 0.1 RPM units
    // return encoder_speed / 10.0;
    return static_cast<double>(encoder_speed/10.0);
}

double ZLMotorController::encoderToRadians(int32_t encoder_ticks)
{
    // Convert encoder ticks to radians
    return encoder_ticks * 2.0 * M_PI / (encoder_resolution_ * gear_ratio_);
}

double ZLMotorController::normalizeAngle(double angle)
{
    // Normalize angle to [-pi, pi]
    while (angle > M_PI) {
        angle -= 2.0 * M_PI;
    }
    while (angle < -M_PI) {
        angle += 2.0 * M_PI;
    }
    return angle;
}

bool ZLMotorController::initSocketCan()
{
    // Create socket
    socket_fd_ = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (socket_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error creating CAN socket: %s", strerror(errno));
        return false;
    }

    // Get interface index
    struct ifreq ifr;
    std::strcpy(ifr.ifr_name, can_interface_.c_str());
    if (ioctl(socket_fd_, SIOCGIFINDEX, &ifr) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error getting interface index for %s: %s",
                     can_interface_.c_str(), strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    // Bind socket to the CAN interface
    struct sockaddr_can addr;
    std::memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;
    if (bind(socket_fd_, reinterpret_cast<struct sockaddr*>(&addr), sizeof(addr)) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error binding socket to interface %s: %s",
                     can_interface_.c_str(), strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    // Set up filter for our RX CAN ID
    struct can_filter rfilter[1];
    rfilter[0].can_id = can_id_rx_;
    rfilter[0].can_mask = CAN_SFF_MASK;

    if (setsockopt(socket_fd_, SOL_CAN_RAW, CAN_RAW_FILTER, rfilter, sizeof(rfilter)) < 0) {
        RCLCPP_ERROR(this->get_logger(), "Error setting receive filter: %s", strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    RCLCPP_INFO(this->get_logger(), "Successfully initialized SocketCAN on interface %s with filter for ID 0x%X",
                 can_interface_.c_str(), can_id_rx_);
    return true;
}

bool ZLMotorController::sendCanFrameDirect(const sl_vcu_all::msg::CanFrame& frame)
{
    if (socket_fd_ < 0) {
        RCLCPP_ERROR(this->get_logger(), "Socket not initialized");
        return false;
    }

    // Convert ROS message to CAN frame
    struct can_frame can_frame;
    can_frame.can_id = frame.id;
    if (frame.is_extended) {
        can_frame.can_id |= CAN_EFF_FLAG;
    }
    if (frame.is_rtr) {
        can_frame.can_id |= CAN_RTR_FLAG;
    }

    can_frame.can_dlc = frame.dlc;
    for (int i = 0; i < frame.dlc && i < 8; i++) {
        can_frame.data[i] = frame.data[i];
    }

    // Send the frame
    if (write(socket_fd_, &can_frame, sizeof(struct can_frame)) != sizeof(struct can_frame)) {
        RCLCPP_ERROR(this->get_logger(), "Error sending CAN frame: %s", strerror(errno));
        return false;
    } else {
        RCLCPP_DEBUG(this->get_logger(), "Sent CAN frame with ID 0x%03X, length %d, data %02X %02X %02X %02X %02X %02X %02X %02X ",
                     frame.id, frame.dlc, frame.data[0], frame.data[1], frame.data[2], frame.data[3],
                     frame.data[4], frame.data[5], frame.data[6], frame.data[7]);
        return true;
    }
}

void ZLMotorController::receiveCanFramesThread()
{
    struct can_frame frame;
    struct timeval timeout;
    fd_set readSet;

    RCLCPP_INFO(this->get_logger(), "SocketCAN receive thread started");

    while (running_) {
        if (socket_fd_ < 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        // Set up select timeout (100 ms)
        timeout.tv_sec = 0;
        timeout.tv_usec = 100000;

        FD_ZERO(&readSet);
        FD_SET(socket_fd_, &readSet);

        // Wait for data or timeout
        int ret = select(socket_fd_ + 1, &readSet, NULL, NULL, &timeout);

        if (ret < 0) {
            if (errno != EINTR) {
                RCLCPP_ERROR(this->get_logger(), "Error in select: %s", strerror(errno));
            }
            continue;
        } else if (ret == 0) {
            // Timeout, continue
            continue;
        }

        // Check if data is available on the socket
        if (FD_ISSET(socket_fd_, &readSet)) {
            ssize_t nbytes = read(socket_fd_, &frame, sizeof(struct can_frame));

            if (nbytes < 0) {
                if (errno != EAGAIN && errno != EWOULDBLOCK) {
                    RCLCPP_ERROR(this->get_logger(), "Error reading CAN frame: %s", strerror(errno));
                }
                continue;
            }

            if (nbytes != sizeof(struct can_frame)) {
                RCLCPP_WARN(this->get_logger(), "Incomplete CAN frame read");
                continue;
            }

            // Extract CAN ID without flags
            uint32_t can_id = frame.can_id & CAN_EFF_MASK;

            // Check if this is our expected RX ID
            if (can_id == can_id_rx_) {
                // Create ROS message
                auto msg = std::make_shared<sl_vcu_all::msg::CanFrame>();
                msg->id = can_id;
                msg->is_extended = (frame.can_id & CAN_EFF_FLAG) != 0;
                msg->is_rtr = (frame.can_id & CAN_RTR_FLAG) != 0;
                msg->is_error = (frame.can_id & CAN_ERR_FLAG) != 0;
                msg->dlc = frame.can_dlc;

                for (int i = 0; i < frame.can_dlc && i < 8; i++) {
                    msg->data[i] = frame.data[i];
                }

                // Set header timestamp
                msg->header.stamp = this->now();
                msg->header.frame_id = can_interface_;

                // Process the frame directly
                canRxCallback(msg);

                RCLCPP_DEBUG(this->get_logger(), "Received and processed CAN frame with ID 0x%X %02X %02X %02X %02X %02X %02X %02X %02X ",
                             can_id, frame.data[0], frame.data[1], frame.data[2], frame.data[3],
                             frame.data[4], frame.data[5], frame.data[6], frame.data[7]);
            }
        }
    }

    RCLCPP_INFO(this->get_logger(), "SocketCAN receive thread exiting");
}

bool ZLMotorController::queueCanFrameForSend(const sl_vcu_all::msg::CanFrame& frame)
{
    size_t queue_size;
    {
        std::lock_guard<std::mutex> lock(send_queue_mutex_);
        send_queue_.push(frame);
        queue_size = send_queue_.size();
    }
    send_queue_cv_.notify_one();

    RCLCPP_DEBUG(this->get_logger(), "CAN frame queued for send: %02X %02X %02X %02X %02X %02X %02X %02X, queue size: %ld"
    , frame.data[0], frame.data[1], frame.data[2], frame.data[3], frame.data[4], frame.data[5], frame.data[6], frame.data[7], queue_size);
    return true;
}

void ZLMotorController::sendCanFramesThread()
{
    RCLCPP_INFO(this->get_logger(), "SocketCAN send thread started");

    while (running_) {
        std::unique_lock<std::mutex> lock(send_queue_mutex_);

        // Wait for frames to send or shutdown signal
        send_queue_cv_.wait(lock, [this] { return !send_queue_.empty() || !running_; });

        if (!running_) {
            break;
        }

        while (!send_queue_.empty() && running_) {
            auto frame = send_queue_.front();
            send_queue_.pop();
            lock.unlock();

            // Check timing interval
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(now - last_send_time_).count();

            if (elapsed < min_send_interval_ms_ * 1000) {
                // Too soon, wait for the remaining time
                auto wait_time = min_send_interval_ms_ * 1000 - elapsed;
                RCLCPP_DEBUG(this->get_logger(), "Send interval too small (%ld us), waiting %ld us", elapsed, wait_time);
                std::this_thread::sleep_for(std::chrono::microseconds(wait_time));
            }

            // Send the frame
            if (sendCanFrameDirect(frame)) {
                last_send_time_ = std::chrono::steady_clock::now();
                // RCLCPP_DEBUG(this->get_logger(), "CAN frame sent: ID 0x%03X", frame.id);
            } else {
                RCLCPP_ERROR(this->get_logger(), "Failed to send CAN frame: ID 0x%03X", frame.id);
            }

            lock.lock();
        }
    }

    RCLCPP_INFO(this->get_logger(), "SocketCAN send thread exiting");
}


}  // namespace sl_vcu_all

// Main entry point
int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<sl_vcu_all::ZLMotorController>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
