#include "sl_vcu_all/can_frame_dispatcher.hpp"
#include <cstring>
#include <fcntl.h>
#include <thread>
#include <algorithm>

namespace sl_vcu_all
{

CanFrameDispatcher::CanFrameDispatcher(const rclcpp::NodeOptions & options)
: Node("can_frame_dispatcher", options),
  socket_fd_(-1),
  is_socket_initialized_(false),
  running_(false)
{
  // Declare parameters
  this->declare_parameter("can_interface", "can1");
  can_interface_ = this->get_parameter("can_interface").as_string();

  RCLCPP_INFO(this->get_logger(), "Initializing CAN Frame Dispatcher on interface: %s", can_interface_.c_str());

  // Initialize socket CAN
  is_socket_initialized_ = initSocketCan();
  if (!is_socket_initialized_) {
    RCLCPP_ERROR(this->get_logger(), "Failed to initialize SocketCAN");
    return;
  }

  // Create subscriber for CAN frames to transmit
  can_tx_sub_ = this->create_subscription<sl_vcu_all::msg::CanFrame>(
    "can_tx", 100,
    std::bind(&CanFrameDispatcher::canTxCallback, this, std::placeholders::_1));

  // Create service for adding CAN filters
  add_filter_srv_ = this->create_service<sl_vcu_all::srv::AddCanFilter>(
    "add_can_filter",
    std::bind(&CanFrameDispatcher::addCanFilterCallback, this,
      std::placeholders::_1, std::placeholders::_2));

  // Create service for checking node status
  check_status_srv_ = this->create_service<sl_vcu_all::srv::CheckNodeStatus>(
    "check_node_status",
    std::bind(&CanFrameDispatcher::checkNodeStatusCallback, this,
      std::placeholders::_1, std::placeholders::_2));

  // Start receive thread
  running_ = true;
  receive_thread_ = std::thread(&CanFrameDispatcher::receiveCanFrames, this);

  RCLCPP_INFO(this->get_logger(), "CAN Frame Dispatcher initialized successfully");
}

CanFrameDispatcher::~CanFrameDispatcher()
{
  // Stop the receive thread
  running_ = false;
  if (receive_thread_.joinable()) {
    receive_thread_.join();
  }

  // Close socket
  if (socket_fd_ >= 0) {
    close(socket_fd_);
    socket_fd_ = -1;
  }
}

bool CanFrameDispatcher::initSocketCan()
{
  // Create socket
  socket_fd_ = socket(PF_CAN, SOCK_RAW, CAN_RAW);
  if (socket_fd_ < 0) {
    RCLCPP_ERROR(this->get_logger(), "Error creating CAN socket: %s", strerror(errno));
    return false;
  }

  // Set socket to non-blocking mode
  int flags = fcntl(socket_fd_, F_GETFL, 0);
  if (flags < 0) {
    RCLCPP_ERROR(this->get_logger(), "Error getting socket flags: %s", strerror(errno));
    close(socket_fd_);
    socket_fd_ = -1;
    return false;
  }
  flags |= O_NONBLOCK;
  if (fcntl(socket_fd_, F_SETFL, flags) < 0) {
    RCLCPP_ERROR(this->get_logger(), "Error setting socket to non-blocking mode: %s", strerror(errno));
    close(socket_fd_);
    socket_fd_ = -1;
    return false;
  }

  // Get interface index
  struct ifreq ifr;
  std::memset(&ifr, 0, sizeof(ifr));
  std::strncpy(ifr.ifr_name, can_interface_.c_str(), IFNAMSIZ - 1);
  if (ioctl(socket_fd_, SIOCGIFINDEX, &ifr) < 0) {
    RCLCPP_ERROR(this->get_logger(), "Error getting interface index for %s: %s",
                 can_interface_.c_str(), strerror(errno));
    close(socket_fd_);
    socket_fd_ = -1;
    return false;
  }

  // Bind socket to the CAN interface
  struct sockaddr_can addr;
  std::memset(&addr, 0, sizeof(addr));
  addr.can_family = AF_CAN;
  addr.can_ifindex = ifr.ifr_ifindex;
  if (bind(socket_fd_, reinterpret_cast<struct sockaddr*>(&addr), sizeof(addr)) < 0) {
    RCLCPP_ERROR(this->get_logger(), "Error binding socket to interface %s: %s",
                 can_interface_.c_str(), strerror(errno));
    close(socket_fd_);
    socket_fd_ = -1;
    return false;
  }

  RCLCPP_INFO(this->get_logger(), "Successfully initialized SocketCAN on interface %s",
               can_interface_.c_str());
  return true;
}

bool CanFrameDispatcher::applyCanFilters()
{
  if (socket_fd_ < 0) {
    RCLCPP_ERROR(this->get_logger(), "Socket not initialized");
    return false;
  }

  if (can_filter_ids_.empty()) {
    // If no filters, receive all frames
    if (setsockopt(socket_fd_, SOL_CAN_RAW, CAN_RAW_FILTER, NULL, 0) < 0) {
      RCLCPP_ERROR(this->get_logger(), "Error setting receive filter: %s", strerror(errno));
      return false;
    }
  } else {
    // Set up filters for specific CAN IDs
    std::vector<struct can_filter> rfilter(can_filter_ids_.size());
    for (size_t i = 0; i < can_filter_ids_.size(); i++) {
      rfilter[i].can_id = can_filter_ids_[i];
      rfilter[i].can_mask = CAN_SFF_MASK;
    }

    if (setsockopt(socket_fd_, SOL_CAN_RAW, CAN_RAW_FILTER, rfilter.data(),
                   sizeof(struct can_filter) * rfilter.size()) < 0) {
      RCLCPP_ERROR(this->get_logger(), "Error setting receive filter: %s", strerror(errno));
      return false;
    }
  }

  return true;
}

bool CanFrameDispatcher::addCanFilter(uint32_t can_id, const std::string & topic_name)
{
  // Check if this CAN ID already has a filter
  auto it = std::find(can_filter_ids_.begin(), can_filter_ids_.end(), can_id);
  if (it != can_filter_ids_.end()) {
    RCLCPP_WARN(this->get_logger(), "Filter for CAN ID 0x%X already exists", can_id);
    return false;
  }

  // Create publisher for this CAN ID
  std::string topic = topic_name.empty() ? "can_rx_" + std::to_string(can_id) : topic_name;
  auto publisher = this->create_publisher<sl_vcu_all::msg::CanFrame>(topic, 10);
  can_id_publishers_[can_id] = publisher;

  // Add CAN ID to filter list
  can_filter_ids_.push_back(can_id);

  // Apply updated filters
  if (applyCanFilters()) {
    RCLCPP_INFO(this->get_logger(), "Added filter for CAN ID 0x%X, publishing to %s",
                can_id, topic.c_str());
    return true;
  } else {
    // If applying filters failed, remove the CAN ID
    can_filter_ids_.pop_back();
    can_id_publishers_.erase(can_id);
    return false;
  }
}

void CanFrameDispatcher::canTxCallback(const sl_vcu_all::msg::CanFrame::SharedPtr msg)
{
  if (socket_fd_ < 0) {
    RCLCPP_ERROR(this->get_logger(), "Socket not initialized");
    return;
  }

  // Convert ROS message to CAN frame
  struct can_frame frame;
  frame.can_id = msg->id;
  if (msg->is_extended) {
    frame.can_id |= CAN_EFF_FLAG;
  }
  if (msg->is_rtr) {
    frame.can_id |= CAN_RTR_FLAG;
  }

  frame.can_dlc = msg->dlc;
  for (int i = 0; i < msg->dlc && i < 8; i++) {
    frame.data[i] = msg->data[i];
  }

  // Send the frame
  if (write(socket_fd_, &frame, sizeof(struct can_frame)) != sizeof(struct can_frame)) {
    RCLCPP_ERROR(this->get_logger(), "Error sending CAN frame: %s", strerror(errno));
  } else {
    RCLCPP_DEBUG(this->get_logger(), "Sent CAN frame with ID 0x%03X, length %d, data %02X %02X %02X %02X %02X %02X %02X %02X "
    , msg->id, msg->dlc, msg->data[0], msg->data[1], msg->data[2], msg->data[3], msg->data[4], msg->data[5], msg->data[6], msg->data[7]);
  }
}

void CanFrameDispatcher::addCanFilterCallback(
  const std::shared_ptr<sl_vcu_all::srv::AddCanFilter::Request> request,
  std::shared_ptr<sl_vcu_all::srv::AddCanFilter::Response> response)
{
  bool success = false;
  // Check if this CAN ID already has a filter
  auto it = std::find(can_filter_ids_.begin(), can_filter_ids_.end(), request->can_id);
  if (it != can_filter_ids_.end()) {
    RCLCPP_WARN(this->get_logger(), "Filter for CAN ID 0x%X already exists", request->can_id);
    success = true;
  }

  if (success) {
    response->success = true;
    response->message = "Successfully added filter for CAN ID " +
                         std::to_string(request->can_id) +
                         " publishing to " + request->topic_name;
  } else {
    success = addCanFilter(request->can_id, request->topic_name);

    if (success) {
        response->success = true;
        response->message = "Successfully added filter for CAN ID " +
                            std::to_string(request->can_id) +
                            " publishing to " + request->topic_name;
    } else {
        response->message = "Failed to add filter for CAN ID " +
                            std::to_string(request->can_id);
    }
  }
}

void CanFrameDispatcher::receiveCanFrames()
{
  struct can_frame frame;
  struct timeval timeout;
  fd_set readSet;

  while (running_) {
    // Set up select timeout (100 ms)
    timeout.tv_sec = 0;
    timeout.tv_usec = 100000;

    FD_ZERO(&readSet);
    FD_SET(socket_fd_, &readSet);

    // Wait for data or timeout
    int ret = select(socket_fd_ + 1, &readSet, NULL, NULL, &timeout);

    if (ret < 0) {
      if (errno != EINTR) {
        RCLCPP_ERROR(this->get_logger(), "Error in select: %s", strerror(errno));
      }
      continue;
    } else if (ret == 0) {
      // Timeout, continue
      continue;
    }

    // Check if data is available on the socket
    if (FD_ISSET(socket_fd_, &readSet)) {
      ssize_t nbytes = read(socket_fd_, &frame, sizeof(struct can_frame));

      if (nbytes < 0) {
        if (errno != EAGAIN && errno != EWOULDBLOCK) {
          RCLCPP_ERROR(this->get_logger(), "Error reading CAN frame: %s", strerror(errno));
        }
        continue;
      }

      if (nbytes != sizeof(struct can_frame)) {
        RCLCPP_WARN(this->get_logger(), "Incomplete CAN frame read");
        continue;
      }

      // Extract CAN ID without flags
      uint32_t can_id = frame.can_id & CAN_EFF_MASK;

      // Check if we have a publisher for this CAN ID
      auto it = can_id_publishers_.find(can_id);
      if (it != can_id_publishers_.end()) {
        // Create ROS message
        auto msg = std::make_unique<sl_vcu_all::msg::CanFrame>();
        msg->id = can_id;
        msg->is_extended = (frame.can_id & CAN_EFF_FLAG) != 0;
        msg->is_rtr = (frame.can_id & CAN_RTR_FLAG) != 0;
        msg->is_error = (frame.can_id & CAN_ERR_FLAG) != 0;
        msg->dlc = frame.can_dlc;

        for (int i = 0; i < frame.can_dlc && i < 8; i++) {
          msg->data[i] = frame.data[i];
        }

        // Set header timestamp
        msg->header.stamp = this->now();
        msg->header.frame_id = can_interface_;

        // Publish message
        it->second->publish(std::move(msg));
        RCLCPP_DEBUG(this->get_logger(), "Received and published CAN frame with ID 0x%X %02X %02X %02X %02X %02X %02X %02X %02X ", 
        can_id, frame.data[0], frame.data[1], frame.data[2], frame.data[3], frame.data[4], frame.data[5], frame.data[6], frame.data[7]);
      }
      else
      {
        RCLCPP_INFO(this->get_logger(), "Received CAN frame with ID 0x%X, but no publisher found. %02X %02X %02X %02X %02X %02X %02X %02X ", 
        can_id, frame.data[0], frame.data[1], frame.data[2], frame.data[3], frame.data[4], frame.data[5], frame.data[6], frame.data[7]);
      }
    }
  }
}

void CanFrameDispatcher::checkNodeStatusCallback(
  const std::shared_ptr<sl_vcu_all::srv::CheckNodeStatus::Request> request,
  std::shared_ptr<sl_vcu_all::srv::CheckNodeStatus::Response> response)
{
  (void)request; // Unused parameter

  // Check if socket is initialized and running
  response->is_ready = is_socket_initialized_ && running_ && (socket_fd_ >= 0);

  if (response->is_ready) {
    response->status = "CAN Frame Dispatcher is running normally on interface " + can_interface_;
  } else {
    if (!is_socket_initialized_) {
      response->status = "SocketCAN initialization failed";
    } else if (socket_fd_ < 0) {
      response->status = "Socket connection lost";
    } else if (!running_) {
      response->status = "Receive thread not running";
    } else {
      response->status = "Unknown error";
    }
  }

  RCLCPP_DEBUG(this->get_logger(), "Status check: ready=%d, status=%s",
               response->is_ready, response->status.c_str());
}

}  // namespace sl_vcu_all

// Main entry point
int main(int argc, char ** argv)
{
  rclcpp::init(argc, argv);
  auto node = std::make_shared<sl_vcu_all::CanFrameDispatcher>();
  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}