#!/usr/bin/env python3

"""
Test script for Jack Control Action Server
This script demonstrates how to use the jack control action to perform various operations.
"""

import rclpy
from rclpy.action import ActionClient
from rclpy.node import Node
from sl_vcu_all.action import JackControl
import sys


class JackControlTestClient(Node):
    def __init__(self):
        super().__init__('jack_control_test_client')
        self._action_client = ActionClient(self, JackControl, 'jack_control')
        self._goal_handle = None

    def send_goal(self, command, target_position=0, speed=0):
        goal_msg = JackControl.Goal()
        goal_msg.command = command
        goal_msg.target_position = target_position
        goal_msg.speed = speed

        self.get_logger().info(f'Sending goal: {command}')

        self._action_client.wait_for_server()

        self._send_goal_future = self._action_client.send_goal_async(
            goal_msg, feedback_callback=self.feedback_callback)

        self._send_goal_future.add_done_callback(self.goal_response_callback)

    def goal_response_callback(self, future):
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().info('Goal rejected :(')
            return

        self.get_logger().info('Goal accepted :)')
        self._goal_handle = goal_handle

        self._get_result_future = goal_handle.get_result_async()
        self._get_result_future.add_done_callback(self.get_result_callback)

    def get_result_callback(self, future):
        result = future.result().result
        self.get_logger().info(f'Result: success={result.success}, message="{result.message}"')
        self.get_logger().info(f'Final position: {result.final_position}')
        self.get_logger().info(f'Final status: 0x{result.final_status:04X}')
        self.get_logger().info(f'Alarm code: 0x{result.alarm_code:04X}')
        rclpy.shutdown()

    def feedback_callback(self, feedback_msg):
        feedback = feedback_msg.feedback
        self.get_logger().info(f'Feedback: stage={feedback.current_stage}, '
                              f'position={feedback.current_position}, '
                              f'progress={feedback.progress:.1f}%')

    def cancel_goal(self):
        if self._goal_handle is not None:
            self.get_logger().info('Canceling current goal...')
            cancel_future = self._goal_handle.cancel_goal_async()
            cancel_future.add_done_callback(self.cancel_done_callback)
            return cancel_future
        return None

    def cancel_done_callback(self, future):
        cancel_response = future.result()
        if cancel_response.accepted:
            self.get_logger().info('Goal cancellation accepted')
        else:
            self.get_logger().info('Goal cancellation rejected')
        rclpy.shutdown()


def main(args=None):
    if len(sys.argv) < 2:
        print("Usage: python3 test_jack_control.py <command> [target_position] [speed]")
        print("Commands: detect_base, lift_up, lift_down, stop, clear_alarm")
        print("Example: python3 test_jack_control.py detect_base")
        print("Example: python3 test_jack_control.py lift_up 1000000 1500")
        return

    rclpy.init(args=args)

    command = sys.argv[1]
    target_position = int(sys.argv[2]) if len(sys.argv) > 2 else 0
    speed = int(sys.argv[3]) if len(sys.argv) > 3 else 0

    action_client = JackControlTestClient()
    action_client.send_goal(command, target_position, speed)

    try:
        rclpy.spin(action_client)
    except KeyboardInterrupt:
        action_client.get_logger().info('Test interrupted by user - canceling goal...')
        cancel_future = action_client.cancel_goal()
        if cancel_future is not None:
            # Wait a bit for cancellation to complete
            try:
                rclpy.spin_until_future_complete(action_client, cancel_future, timeout_sec=2.0)
            except Exception as e:
                action_client.get_logger().warn(f'Error during goal cancellation: {e}')
        else:
            action_client.get_logger().info('No active goal to cancel')
    finally:
        action_client.destroy_node()


if __name__ == '__main__':
    main()
