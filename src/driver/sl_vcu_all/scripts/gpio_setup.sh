#!/bin/bash
# gpio-permission-setup.sh
# Configure GPIO group and udev rules for sysfs access

set -e

RULES_FILE="/etc/udev/rules.d/98-gpio.rules"
TARGET_USER="$SUDO_USER"

check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo "Error: This script must be run with sudo" >&2
        exit 1
    fi
}

create_gpio_group() {
    if ! getent group gpio >/dev/null; then
        groupadd gpio
        echo "[INFO] Created gpio group"
    else
        echo "[WARN] gpio group already exists"
    fi
}

add_user_to_group() {
    if ! id -nG "$TARGET_USER" | grep -qw "gpio"; then
        usermod -aG gpio "$TARGET_USER"
        echo "[INFO] Added $TARGET_USER to gpio group"
    else
        echo "[WARN] $TARGET_USER already in gpio group"
    fi
}

configure_udev() {
    cat > "$RULES_FILE" <<- EOF
    # GPIO sysfs interface permissions
    SUBSYSTEM=="gpio", GROUP="gpio", MODE="0660"
    SUBSYSTEM=="gpio", ACTION=="add", RUN+="/bin/chown root:gpio /sys%p/export /sys%p/unexport /sys%p/value"
EOF
    
    udevadm control --reload
    udevadm trigger --subsystem-match=gpio
    echo "[INFO] udev rules configured"
}

set_temp_permissions() {
    chown root:gpio /sys/class/gpio/export /sys/class/gpio/unexport
    chmod 0660 /sys/class/gpio/export /sys/class/gpio/unexport
}

main() {
    check_root
    create_gpio_group
    add_user_to_group
    configure_udev
    set_temp_permissions
    
    echo "Configuration complete. Requirements:"
    echo "1. Reboot or log out/in to apply group changes"
    echo "2. Verify with: groups | grep gpio"
    echo "3. Test with: ls -l /sys/class/gpio/export"
}

main