#!/usr/bin/env python3

"""
Test script for jack control enable parameter functionality.

This script tests that:
1. When enable=true, the jack control node initializes normally
2. When enable=false, the jack control node exits without initialization
"""

import rclpy
from rclpy.node import Node
import subprocess
import time
import sys
import os
import tempfile
import yaml

class JackControlEnableTest(Node):
    def __init__(self):
        super().__init__('jack_control_enable_test')
        self.get_logger().info('Jack Control Enable Test Node started')

    def create_test_config(self, enable_value):
        """Create a temporary config file with the specified enable value."""
        config = {
            'jack_control': {
                'ros__parameters': {
                    'enable': enable_value,
                    'can_interface': 'can0',
                    'control_cycle_ms': 50,
                    'response_timeout_ms': 1000,
                    'heartbeat_period_ms': 100,
                    'min_send_interval_ms': 5,
                    'default_speed': 1000,
                    'max_speed': 3000,
                    'max_position': 26000000,
                    'min_position': 0,
                    'position_tolerance': 1000,
                    'position_timeout_s': 60,
                    'detection_timeout_s': 30,
                    'up_stage_timeout_s': 90,
                    'down_stage_timeout_s': 90,
                    'base_stage_timeout_s': 45,
                    'movement_start_delay_s': 3,
                    'movement_check_tolerance': 100,
                    'status_update_delay_s': 2,
                    'auto_detect_base_on_start': False  # Disable auto detection for testing
                }
            }
        }
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(config, temp_file, default_flow_style=False)
        temp_file.close()
        return temp_file.name

    def test_enable_true(self):
        """Test that jack control node initializes when enable=true."""
        self.get_logger().info('Testing enable=true...')
        
        config_file = self.create_test_config(True)
        
        try:
            # Start jack control node with enable=true
            cmd = [
                'ros2', 'run', 'sl_vcu_all', 'jack_control_node',
                '--ros-args', '--params-file', config_file
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a bit for initialization
            time.sleep(3)
            
            # Check if process is still running (should be running)
            if process.poll() is None:
                self.get_logger().info('✓ PASS: Node is running with enable=true')
                process.terminate()
                process.wait(timeout=5)
                return True
            else:
                stdout, stderr = process.communicate()
                self.get_logger().error(f'✗ FAIL: Node exited unexpectedly with enable=true')
                self.get_logger().error(f'stdout: {stdout}')
                self.get_logger().error(f'stderr: {stderr}')
                return False
                
        except Exception as e:
            self.get_logger().error(f'✗ FAIL: Exception during enable=true test: {e}')
            return False
        finally:
            os.unlink(config_file)

    def test_enable_false(self):
        """Test that jack control node exits when enable=false."""
        self.get_logger().info('Testing enable=false...')
        
        config_file = self.create_test_config(False)
        
        try:
            # Start jack control node with enable=false
            cmd = [
                'ros2', 'run', 'sl_vcu_all', 'jack_control_node',
                '--ros-args', '--params-file', config_file
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for process to exit
            try:
                stdout, stderr = process.communicate(timeout=10)
                
                # Check if process exited (should exit quickly)
                if process.returncode == 0:
                    # Check if the expected message is in the output
                    if 'Jack Control Node is disabled. Exiting without initialization.' in stdout:
                        self.get_logger().info('✓ PASS: Node exited correctly with enable=false')
                        return True
                    else:
                        self.get_logger().error('✗ FAIL: Node exited but without expected message')
                        self.get_logger().error(f'stdout: {stdout}')
                        return False
                else:
                    self.get_logger().error(f'✗ FAIL: Node exited with non-zero return code: {process.returncode}')
                    self.get_logger().error(f'stdout: {stdout}')
                    self.get_logger().error(f'stderr: {stderr}')
                    return False
                    
            except subprocess.TimeoutExpired:
                self.get_logger().error('✗ FAIL: Node did not exit within timeout with enable=false')
                process.terminate()
                process.wait(timeout=5)
                return False
                
        except Exception as e:
            self.get_logger().error(f'✗ FAIL: Exception during enable=false test: {e}')
            return False
        finally:
            os.unlink(config_file)

    def run_tests(self):
        """Run all tests."""
        self.get_logger().info('Starting Jack Control Enable Parameter Tests')
        
        test_results = []
        
        # Test enable=false (should exit quickly)
        test_results.append(self.test_enable_false())
        
        # Test enable=true (should run normally)
        # Note: This test might fail if CAN interface is not available
        # test_results.append(self.test_enable_true())
        
        # Summary
        passed = sum(test_results)
        total = len(test_results)
        
        self.get_logger().info(f'Test Results: {passed}/{total} tests passed')
        
        if passed == total:
            self.get_logger().info('✓ ALL TESTS PASSED')
            return True
        else:
            self.get_logger().error('✗ SOME TESTS FAILED')
            return False

def main():
    rclpy.init()
    
    test_node = JackControlEnableTest()
    
    try:
        success = test_node.run_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        test_node.get_logger().info('Test interrupted by user')
    finally:
        test_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
