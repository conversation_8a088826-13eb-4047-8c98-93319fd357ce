#!/usr/bin/env python3

"""
Test script to verify speed conversion in Jack Control
This script tests various RPM values and shows the expected CAN values.
"""

def convert_rpm_to_can_value(rpm):
    """
    Convert RPM to CAN value using formula: rpm / 1875 * 512 * 131072
    """
    if rpm == 0:
        return 0
    
    # Calculate: rpm * 512 * 131072 / 1875
    temp = rpm * 512 * 131072
    can_value = temp // 1875
    
    return can_value

def main():
    print("Jack Control Speed Conversion Test")
    print("=" * 50)
    print("Formula: rpm / 1875 * 512 * 131072")
    print("=" * 50)
    
    # Test various RPM values
    test_rpms = [0, 100, 500, 1000, 1500, 2000, 2500, 3000]
    
    print(f"{'RPM':<8} {'CAN Value':<12} {'Hex':<10}")
    print("-" * 30)
    
    for rpm in test_rpms:
        can_value = convert_rpm_to_can_value(rpm)
        print(f"{rpm:<8} {can_value:<12} 0x{can_value:08X}")
    
    print("\nDefault configuration values:")
    print(f"Default speed (1000 RPM): {convert_rpm_to_can_value(1000)} (0x{convert_rpm_to_can_value(1000):08X})")
    print(f"Max speed (3000 RPM): {convert_rpm_to_can_value(3000)} (0x{convert_rpm_to_can_value(3000):08X})")

if __name__ == '__main__':
    main()
