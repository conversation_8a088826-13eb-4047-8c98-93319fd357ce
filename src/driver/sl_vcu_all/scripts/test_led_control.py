#!/usr/bin/env python3

"""
Test script for LED Display Control System
This script demonstrates how to use the LED control service to control SK6812 LED strips.

Features tested:
- Channel-based hardware communication
- Synchronized channel control (sync_channels)
- Optional parameters with default values
- On-time duty cycle for flashing effects
- Lenient sync validation (ignores extra data)
"""

import rclpy
from rclpy.node import Node
from sl_vcu_all.srv import LedControl
from sl_vcu_all.msg import ChannelData, PartData
import time

class LedControlTester(Node):
    def __init__(self):
        super().__init__('led_control_tester')
        
        # Create service client
        self.led_client = self.create_client(LedControl, 'led_control')
        
        # Wait for service to be available
        while not self.led_client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('LED control service not available, waiting...')
        
        self.get_logger().info('LED control service is available!')

    def send_led_command(self, channels, channel_data_list, sync_channels=False):
        """Send LED control command with new structured interface"""

        request = LedControl.Request()
        request.channels = channels
        request.sync_channels = sync_channels

        # Build channel data array
        request.channel_data = []
        for channel_data_dict in channel_data_list:
            channel_data = ChannelData()
            channel_data.parts = channel_data_dict['parts']
            channel_data.sync_parts = channel_data_dict.get('sync_parts', False)

            # Build parts data array
            channel_data.parts_data = []
            for part_data_dict in channel_data_dict['parts_data']:
                part_data = PartData()
                part_data.mode = part_data_dict['mode']  # Required field
                # Optional fields - will use defaults if not specified (0 values trigger defaults)
                part_data.green_brightness = part_data_dict.get('green_brightness', 0)
                part_data.red_brightness = part_data_dict.get('red_brightness', 0)
                part_data.blue_brightness = part_data_dict.get('blue_brightness', 0)
                part_data.frequency = part_data_dict.get('frequency', 0.0)  # 0.0 triggers default
                part_data.speed = part_data_dict.get('speed', 0.0)  # 0.0 triggers default
                part_data.marquee_direction = part_data_dict.get('marquee_direction', True)
                part_data.on_time_duty = part_data_dict.get('on_time_duty', 0.0)  # 0.0 triggers default (0.5)

                channel_data.parts_data.append(part_data)

            request.channel_data.append(channel_data)
        
        self.get_logger().info(f'Sending command: channels={channels}, sync_channels={sync_channels}')
        
        future = self.led_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        if future.result() is not None:
            response = future.result()
            if response.success:
                self.get_logger().info(f'Success: {response.message}')
                self.get_logger().info(f'Affected channels: {response.affected_channels}')
            else:
                self.get_logger().error(f'Failed: {response.message}')
            return response.success
        else:
            self.get_logger().error('Service call failed')
            return False

    def run_test_sequence(self):
        """Run a sequence of LED tests"""

        self.get_logger().info('Starting LED control test sequence...')
        self.get_logger().info('NOTE: LED node starts with all channels set to full white (startup behavior)')

        # Wait a moment to see the startup lighting
        time.sleep(2)

        # Test 1: Turn on channel 0 with red color (using explicit values)
        self.get_logger().info('\n=== Test 1: Red solid color on channel 0 ===')
        self.send_led_command(
            channels=[0],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'on',
                    'green_brightness': 0,
                    'red_brightness': 128,
                    'blue_brightness': 0
                }]
            }]
        )
        time.sleep(2)

        # Test 2: Turn on channel 1 with blue color (using default values)
        self.get_logger().info('\n=== Test 2: Blue solid color on channel 1 (using defaults) ===')
        self.send_led_command(
            channels=[1],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'on',
                    # Only specify blue brightness, others will use defaults (0)
                    'blue_brightness': 128
                }]
            }]
        )
        time.sleep(2)

        # Test 3: Breathing effect on both channels (synchronized)
        self.get_logger().info('\n=== Test 3: Green breathing effect on both channels (sync_channels=True) ===')
        self.send_led_command(
            channels=[0, 1],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'breathing',
                    'green_brightness': 200,
                    'frequency': 0.5  # Explicit frequency
                }]
            }],
            sync_channels=True  # All channels will be latched and sent to hardware simultaneously
        )
        time.sleep(6)

        # Test 4: Flashing effect with custom duty cycle
        self.get_logger().info('\n=== Test 4: Purple flashing with 25% duty cycle ===')
        self.send_led_command(
            channels=[0],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'flashing',
                    'red_brightness': 128,
                    'blue_brightness': 128,
                    'frequency': 2.0,  # 2 Hz flashing
                    'on_time_duty': 0.25  # 25% on time
                }]
            }]
        )
        time.sleep(4)

        # Test 5: Multi-part control with sync_parts using flashing mode
        self.get_logger().info('\n=== Test 5: Multi-part flashing with sync_parts=True ===')
        self.send_led_command(
            channels=[1],
            channel_data_list=[{
                'parts': [1, 2],  # First and second parts
                'sync_parts': True,  # Same data for both parts
                'parts_data': [
                    {
                        'mode': 'flashing',
                        'red_brightness': 200,
                        'blue_brightness': 200,
                        'frequency': 1.5,
                        'on_time_duty': 0.3  # 30% on time
                    }
                    # Only one parts_data entry needed since sync_parts=True
                ]
            }]
        )
        time.sleep(4)

        # Test 6: Marquee effect (forward direction)
        self.get_logger().info('\n=== Test 6: Marquee effect (forward direction) ===')
        self.send_led_command(
            channels=[0],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'marquee',
                    'green_brightness': 0,
                    'red_brightness': 255,
                    'blue_brightness': 0,
                    'speed': 5.0,  # 5 pixels per second
                    'marquee_direction': True  # Forward direction
                }]
            }]
        )
        time.sleep(5)

        # Test 7: Marquee effect (reverse direction)
        self.get_logger().info('\n=== Test 7: Marquee effect (reverse direction) ===')
        self.send_led_command(
            channels=[1],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'marquee',
                    'green_brightness': 0,
                    'red_brightness': 0,
                    'blue_brightness': 255,
                    'speed': 8.0,  # 8 pixels per second
                    'marquee_direction': False  # Reverse direction
                }]
            }]
        )
        time.sleep(5)

        # Test 8: Lenient sync validation (extra data ignored) with different data
        self.get_logger().info('\n=== Test 8: Testing lenient sync validation with different effects ===')
        self.send_led_command(
            channels=[0, 1],
            channel_data_list=[
                {
                    'parts': [0],
                    'sync_parts': False,
                    'parts_data': [{
                        'mode': 'breathing',
                        'green_brightness': 150,
                        'red_brightness': 50,
                        'frequency': 1.0
                    }]
                },
                # This extra channel data will be ignored when sync_channels=True
                {
                    'parts': [0],
                    'sync_parts': False,
                    'parts_data': [{
                        'mode': 'marquee',  # Different mode - will be ignored
                        'blue_brightness': 200,
                        'speed': 15.0
                    }]
                },
                # Even more extra data - also ignored
                {
                    'parts': [1, 2],
                    'sync_parts': True,
                    'parts_data': [{
                        'mode': 'flashing',
                        'red_brightness': 255
                    }]
                }
            ],
            sync_channels=True  # Only first channel_data will be used, others ignored
        )
        time.sleep(4)

        # Test 9: Turn off all LEDs (demonstrating channel-based hardware communication)
        self.get_logger().info('\n=== Test 9: Turn off all LEDs (sync_channels=True) ===')
        self.send_led_command(
            channels=[0, 1],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'off'  # Only mode is required, other fields optional
                }]
            }],
            sync_channels=True  # All channels latched first, then sent to hardware simultaneously
        )

        self.get_logger().info('\n=== LED control test sequence completed! ===')
        self.get_logger().info('Features demonstrated:')
        self.get_logger().info('  ✓ Channel-based hardware communication')
        self.get_logger().info('  ✓ Synchronized channel control (sync_channels)')
        self.get_logger().info('  ✓ Optional parameters with default values')
        self.get_logger().info('  ✓ Custom on-time duty cycle for flashing')
        self.get_logger().info('  ✓ Multi-part control with different sync modes')
        self.get_logger().info('  ✓ Marquee effect with forward and reverse directions')
        self.get_logger().info('  ✓ Lenient sync validation (ignores extra data)')

def main(args=None):
    rclpy.init(args=args)
    
    tester = LedControlTester()
    
    try:
        tester.run_test_sequence()
    except KeyboardInterrupt:
        tester.get_logger().info('Test interrupted by user')
    except Exception as e:
        tester.get_logger().error(f'Test failed with exception: {e}')
    finally:
        # Make sure to turn off all LEDs
        tester.send_led_command(
            channels=[0, 1],
            channel_data_list=[{
                'parts': [0],  # Whole strip
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'off'
                }]
            }],
            sync_channels=True
        )
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
