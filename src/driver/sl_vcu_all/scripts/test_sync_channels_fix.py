#!/usr/bin/env python3

"""
Test script to verify the sync_channels fix for LED control.
This script reproduces the issue described:
1. Set animated mode to both channels with channel sync
2. Set only one channel without channel sync
3. Set animated mode with channel sync again - both channels should be affected
"""

import rclpy
from rclpy.node import Node
from sl_vcu_all.srv import LedControl
from sl_vcu_all.msg import ChannelData, PartData
import time

class SyncChannelsTestNode(Node):
    def __init__(self):
        super().__init__('sync_channels_test_node')
        
        # Create service client
        self.led_client = self.create_client(LedControl, 'led_control')
        
        # Wait for service to be available
        while not self.led_client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('LED control service not available, waiting...')
        
        self.get_logger().info('LED control service is available!')

    def send_led_command(self, channels, channel_data_list, sync_channels=False, description=""):
        """Send LED control command"""
        
        request = LedControl.Request()
        request.channels = channels
        request.sync_channels = sync_channels

        # Build channel data array
        request.channel_data = []
        for channel_data_dict in channel_data_list:
            channel_data = ChannelData()
            channel_data.parts = channel_data_dict['parts']
            channel_data.sync_parts = channel_data_dict.get('sync_parts', False)

            # Build parts data array
            channel_data.parts_data = []
            for part_data_dict in channel_data_dict['parts_data']:
                part_data = PartData()
                part_data.mode = part_data_dict['mode']
                part_data.green_brightness = part_data_dict.get('green_brightness', 0)
                part_data.red_brightness = part_data_dict.get('red_brightness', 0)
                part_data.blue_brightness = part_data_dict.get('blue_brightness', 0)
                part_data.frequency = part_data_dict.get('frequency', 0.0)
                part_data.speed = part_data_dict.get('speed', 0.0)
                part_data.marquee_direction = part_data_dict.get('marquee_direction', True)
                part_data.on_time_duty = part_data_dict.get('on_time_duty', 0.0)

                channel_data.parts_data.append(part_data)

            request.channel_data.append(channel_data)
        
        self.get_logger().info(f'{description}')
        self.get_logger().info(f'  Channels: {channels}, sync_channels: {sync_channels}')
        
        future = self.led_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        if future.result() is not None:
            response = future.result()
            if response.success:
                self.get_logger().info(f'  ✓ Success: {response.message}')
                self.get_logger().info(f'  ✓ Affected channels: {response.affected_channels}')
                return True
            else:
                self.get_logger().error(f'  ✗ Failed: {response.message}')
                return False
        else:
            self.get_logger().error('  ✗ Service call failed')
            return False

    def run_sync_test(self):
        """Run the part priority test sequence"""

        self.get_logger().info('=== Testing part priority fix ===')
        self.get_logger().info('This test reproduces the reported issue and verifies the fix.')
        self.get_logger().info('Issue: When WHOLE strip is set, individual parts should be deactivated.')

        # Step 1: Set breathing mode to both channels on WHOLE strip (part 0)
        self.get_logger().info('\n--- Step 1: Set breathing on WHOLE strip (part 0) for both channels ---')
        success = self.send_led_command(
            channels=[0, 1],
            channel_data_list=[{
                'parts': [0],  # Whole strip (part 0)
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'breathing',
                    'red_brightness': 255,
                    'green_brightness': 255,
                    'blue_brightness': 0,
                    'frequency': 1.0
                }]
            }],
            sync_channels=True,
            description="Setting breathing (yellow) on WHOLE strip for both channels"
        )
        
        if not success:
            self.get_logger().error("Step 1 failed!")
            return

        time.sleep(3)  # Let it run for a bit

        # Step 2: Set marquee on parts 1,2 (first and second halves) for channel 1 only
        self.get_logger().info('\n--- Step 2: Set marquee on parts 1,2 for channel 1 only ---')
        success = self.send_led_command(
            channels=[1],  # Only channel 1
            channel_data_list=[{
                'parts': [1, 2],  # First and second halves
                'sync_parts': True,  # Same data for both parts
                'parts_data': [{
                    'mode': 'marquee',
                    'red_brightness': 255,
                    'green_brightness': 255,
                    'blue_brightness': 0,
                    'speed': 5.0,
                    'marquee_direction': False
                }]
            }],
            sync_channels=False,
            description="Setting marquee (yellow) on parts 1,2 for channel 1 only"
        )
        
        if not success:
            self.get_logger().error("Step 2 failed!")
            return

        time.sleep(3)  # Let it run for a bit

        # Step 3: Set breathing on WHOLE strip again - should override individual parts
        self.get_logger().info('\n--- Step 3: Set breathing on WHOLE strip again with sync ---')
        self.get_logger().info('CRITICAL TEST: Both channels should show breathing, NOT marquee!')
        self.get_logger().info('The WHOLE strip (part 0) should override individual parts (1,2).')
        success = self.send_led_command(
            channels=[0, 1],  # Both channels
            channel_data_list=[{
                'parts': [0],  # Whole strip (part 0)
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'breathing',
                    'red_brightness': 255,
                    'green_brightness': 255,
                    'blue_brightness': 0,
                    'frequency': 1.0
                }]
            }],
            sync_channels=True,
            description="Setting breathing on WHOLE strip - should override parts 1,2"
        )
        
        if not success:
            self.get_logger().error("Step 3 failed!")
            return
        
        time.sleep(4)
        
        # Step 4: Turn off all LEDs
        self.get_logger().info('\n--- Step 4: Turn off all LEDs ---')
        self.send_led_command(
            channels=[0, 1],
            channel_data_list=[{
                'parts': [0],
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'off'
                }]
            }],
            sync_channels=True,
            description="Turning off all LEDs"
        )
        
        self.get_logger().info('\n=== Test completed! ===')
        self.get_logger().info('Expected behavior:')
        self.get_logger().info('  Step 1: Both channels show yellow breathing (WHOLE strip)')
        self.get_logger().info('  Step 2: Channel 0 still breathing, Channel 1 shows yellow marquee (parts 1,2)')
        self.get_logger().info('  Step 3: BOTH channels show yellow breathing (WHOLE strip overrides parts) - THIS IS THE FIX!')
        self.get_logger().info('  Step 4: Both channels off')
        self.get_logger().info('')
        self.get_logger().info('If Step 3 shows breathing on BOTH channels, the fix works!')
        self.get_logger().info('If Step 3 shows breathing on channel 0 but marquee on channel 1, the bug still exists.')
        self.get_logger().info('')
        self.get_logger().info('The fix ensures that when WHOLE strip (part 0) is set, it deactivates individual parts (1,2).')

def main(args=None):
    rclpy.init(args=args)
    
    tester = SyncChannelsTestNode()
    
    try:
        tester.run_sync_test()
    except KeyboardInterrupt:
        tester.get_logger().info('Test interrupted by user')
    except Exception as e:
        tester.get_logger().error(f'Test failed with exception: {e}')
    finally:
        # Make sure to turn off all LEDs
        tester.send_led_command(
            channels=[0, 1],
            channel_data_list=[{
                'parts': [0],
                'sync_parts': False,
                'parts_data': [{
                    'mode': 'off'
                }]
            }],
            sync_channels=True,
            description="Final cleanup - turning off all LEDs"
        )
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
