#!/usr/bin/env python3

"""
Test script specifically for testing the marquee effect fix
This script tests the marquee function with various speeds and directions
to verify that the fix for smooth movement works correctly.
"""

import rclpy
from rclpy.node import Node
from sl_vcu_all.srv import LedControl
from sl_vcu_all.msg import ChannelData, PartData
import time

class MarqueeTestNode(Node):
    def __init__(self):
        super().__init__('marquee_test_node')
        
        # Create service client
        self.led_client = self.create_client(LedControl, 'led_control')
        
        # Wait for service to be available
        while not self.led_client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('LED control service not available, waiting...')
        
        self.get_logger().info('LED control service is available!')

    def send_marquee_command(self, channel, speed, direction, color_r=255, color_g=0, color_b=0):
        """Send marquee command with specified parameters"""
        
        request = LedControl.Request()
        request.channels = [channel]
        request.sync_channels = False

        # Build channel data
        channel_data = ChannelData()
        channel_data.parts = [0]  # Whole strip
        channel_data.sync_parts = False

        # Build part data
        part_data = PartData()
        part_data.mode = 'marquee'
        part_data.green_brightness = color_g
        part_data.red_brightness = color_r
        part_data.blue_brightness = color_b
        part_data.speed = speed
        part_data.marquee_direction = direction

        channel_data.parts_data = [part_data]
        request.channel_data = [channel_data]
        
        direction_str = "forward" if direction else "reverse"
        self.get_logger().info(f'Sending marquee command: channel={channel}, speed={speed}, direction={direction_str}, color=({color_r},{color_g},{color_b})')
        
        future = self.led_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        if future.result() is not None:
            response = future.result()
            if response.success:
                self.get_logger().info(f'Success: {response.message}')
                return True
            else:
                self.get_logger().error(f'Failed: {response.message}')
                return False
        else:
            self.get_logger().error('Service call failed')
            return False

    def turn_off_all(self):
        """Turn off all LEDs"""
        request = LedControl.Request()
        request.channels = [0, 1]
        request.sync_channels = True

        channel_data = ChannelData()
        channel_data.parts = [0]  # Whole strip
        channel_data.sync_parts = False

        part_data = PartData()
        part_data.mode = 'off'

        channel_data.parts_data = [part_data]
        request.channel_data = [channel_data]
        
        future = self.led_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)

    def run_marquee_tests(self):
        """Run comprehensive marquee tests"""
        
        self.get_logger().info('Starting marquee effect tests...')
        
        # Test 1: Very slow speed (should now work with the fix)
        self.get_logger().info('\n=== Test 1: Very slow marquee (0.5 pixels/sec) - Red ===')
        self.send_marquee_command(channel=0, speed=0.5, direction=True, color_r=255, color_g=0, color_b=0)
        time.sleep(8)  # Long enough to see movement
        
        # Test 2: Normal speed forward
        self.get_logger().info('\n=== Test 2: Normal speed forward (5 pixels/sec) - Green ===')
        self.send_marquee_command(channel=0, speed=5.0, direction=True, color_r=0, color_g=255, color_b=0)
        time.sleep(4)
        
        # Test 3: Normal speed reverse
        self.get_logger().info('\n=== Test 3: Normal speed reverse (5 pixels/sec) - Blue ===')
        self.send_marquee_command(channel=0, speed=5.0, direction=False, color_r=0, color_g=0, color_b=255)
        time.sleep(4)
        
        # Test 4: Fast speed
        self.get_logger().info('\n=== Test 4: Fast speed (15 pixels/sec) - Purple ===')
        self.send_marquee_command(channel=0, speed=15.0, direction=True, color_r=255, color_g=0, color_b=255)
        time.sleep(3)
        
        # Test 5: Very fast speed
        self.get_logger().info('\n=== Test 5: Very fast speed (30 pixels/sec) - Cyan ===')
        self.send_marquee_command(channel=0, speed=30.0, direction=False, color_r=0, color_g=255, color_b=255)
        time.sleep(2)
        
        # Test 6: Fractional speed (should work smoothly now)
        self.get_logger().info('\n=== Test 6: Fractional speed (2.5 pixels/sec) - Yellow ===')
        self.send_marquee_command(channel=0, speed=2.5, direction=True, color_r=255, color_g=255, color_b=0)
        time.sleep(5)
        
        # Test 7: Two channels with different speeds and directions
        self.get_logger().info('\n=== Test 7: Two channels - different speeds and directions ===')
        self.send_marquee_command(channel=0, speed=3.0, direction=True, color_r=255, color_g=0, color_b=0)
        time.sleep(0.5)  # Small delay between commands
        self.send_marquee_command(channel=1, speed=7.0, direction=False, color_r=0, color_g=0, color_b=255)
        time.sleep(6)
        
        # Turn off all LEDs
        self.get_logger().info('\n=== Turning off all LEDs ===')
        self.turn_off_all()
        
        self.get_logger().info('\n=== Marquee tests completed! ===')
        self.get_logger().info('Tests performed:')
        self.get_logger().info('  ✓ Very slow speed (0.5 pixels/sec) - tests fractional movement fix')
        self.get_logger().info('  ✓ Normal speeds (5 pixels/sec) - forward and reverse')
        self.get_logger().info('  ✓ Fast speeds (15-30 pixels/sec) - tests trail effect')
        self.get_logger().info('  ✓ Fractional speed (2.5 pixels/sec) - tests smooth movement')
        self.get_logger().info('  ✓ Dual channel operation with different parameters')
        self.get_logger().info('')
        self.get_logger().info('New marquee features:')
        self.get_logger().info('  ✓ Fixed smooth movement for slow speeds (no more truncation)')
        self.get_logger().info('  ✓ Trail effect - LEDs light up based on movement distance')
        self.get_logger().info('  ✓ Single LED marquee (simplified from 3-LED pattern)')
        self.get_logger().info('  ✓ Proper direction handling for both forward and reverse')

def main(args=None):
    rclpy.init(args=args)
    
    tester = MarqueeTestNode()
    
    try:
        tester.run_marquee_tests()
    except KeyboardInterrupt:
        tester.get_logger().info('Test interrupted by user')
    except Exception as e:
        tester.get_logger().error(f'Test failed with exception: {e}')
    finally:
        # Make sure to turn off all LEDs
        tester.turn_off_all()
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
