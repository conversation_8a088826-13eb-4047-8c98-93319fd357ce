# IMU Device Setup Script

## Overview

The `setup_imu_devices.sh` script configures IMU devices as root, allowing the IMU sensor node to run without root privileges. This separation of concerns improves security and follows best practices.

## Key Benefits

- **Security**: IMU node runs as regular user, not root
- **Separation of Concerns**: Device configuration separate from data processing
- **Flexibility**: Configurable parameters and selective device setup
- **Safety**: Dry-run mode and comprehensive error checking
- **User-Friendly**: Colored output and detailed help

## Script Features

### Device Configuration
- Configures gyroscope (device1) and accelerometer (device2) separately
- Sets sampling frequency, scales, scan elements, and buffer parameters
- Enables/disables device buffers
- Sets appropriate device permissions for non-root access

### Safety Features
- **Dry-run mode**: See what would be done without making changes
- **Error checking**: Validates device existence and write operations
- **Root check**: Ensures script runs with appropriate privileges
- **Rollback**: Can disable buffers if needed

### Flexibility
- **Selective setup**: Configure only gyro, only accel, or both
- **Custom parameters**: Override default sampling rates, scales, etc.
- **Quiet mode**: Reduce output verbosity
- **Help system**: Comprehensive usage information

## Usage Examples

### Basic Usage
```bash
# Setup both devices with defaults (most common)
sudo ./scripts/setup_imu_devices.sh

# Check what would be done first
./scripts/setup_imu_devices.sh --dry-run

# Setup with custom sampling rate
sudo ./scripts/setup_imu_devices.sh --sampling-freq 100
```

### Selective Setup
```bash
# Setup only gyroscope
sudo ./scripts/setup_imu_devices.sh --gyro-only

# Setup only accelerometer
sudo ./scripts/setup_imu_devices.sh --accel-only
```

### Maintenance
```bash
# Disable all buffers
sudo ./scripts/setup_imu_devices.sh --disable

# Quiet operation
sudo ./scripts/setup_imu_devices.sh --quiet
```

## Configuration Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `sampling-freq` | 200 | Sampling frequency in Hz |
| `acc-scale` | 0.001196 | Accelerometer scale factor |
| `gyro-scale` | 0.001064 | Gyroscope scale factor |
| `buffer-length` | 480 | Buffer length in samples |

## Device Paths

### Gyroscope (device1)
- Device: `/dev/iio:device1`
- Sysfs: `/sys/bus/iio/devices/iio:device1/`

### Accelerometer (device2)
- Device: `/dev/iio:device2`
- Sysfs: `/sys/bus/iio/devices/iio:device2/`

## Workflow

1. **Run Setup Script as Root**:
   ```bash
   sudo ./scripts/setup_imu_devices.sh
   ```

2. **Launch IMU Node as Regular User**:
   ```bash
   ros2 launch sl_vcu_all imu_sensor.launch.py
   ```

3. **Monitor IMU Data**:
   ```bash
   ros2 topic echo /imu/data
   ```

## Error Handling

The script provides clear error messages for common issues:

- **Device not found**: Check if IMU hardware is connected
- **Permission denied**: Ensure script runs as root
- **Write failures**: Check sysfs file permissions and device state

## Integration with ROS2

The script is designed to work seamlessly with the ROS2 IMU sensor node:

1. Script configures devices and sets permissions
2. IMU node opens devices without needing root
3. Node reads data and publishes to ROS2 topics
4. Clean separation between system configuration and application logic

## Troubleshooting

### Common Issues

1. **"Device not found"**
   - Check hardware connections
   - Verify device enumeration: `ls /dev/iio:device*`

2. **"Permission denied" when not root**
   - Use `sudo` to run the script
   - Script must configure system files as root

3. **IMU node can't open devices**
   - Run setup script first
   - Check device permissions: `ls -la /dev/iio:device*`

### Debug Mode

Use dry-run mode to debug configuration:
```bash
./scripts/setup_imu_devices.sh --dry-run --verbose
```

## Security Considerations

- Script requires root for device configuration only
- IMU node runs as regular user for data processing
- Device permissions set to allow non-root access
- No persistent root processes needed

This approach follows the principle of least privilege while maintaining functionality.
