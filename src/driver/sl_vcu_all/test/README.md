# IMU Test Program

This standalone C++ program tests the IMU sensors by reading data from both accelerometer and gyroscope devices.

## Overview

The program tests two IMU devices:
- **Device 1** (`/dev/iio:device1`): Gyroscope
- **Device 2** (`/dev/iio:device2`): Accelerometer

Both devices are configured with the same settings:
- Sampling frequency: 200 Hz
- Accelerometer scale: 0.001196
- Gyroscope scale: 0.001064
- Buffer length: 480 bytes
- Timestamp clock: realtime

## Device Paths

### Gyroscope (device1)
- Device: `/dev/iio:device1`
- Sysfs path: `/sys/bus/iio/devices/iio:device1/`

### Accelerometer (device2)
- Device: `/dev/iio:device2`
- Sysfs path: `/sys/bus/iio/devices/iio:device2/`

## Building

### Using Make (Recommended)
```bash
cd src/sl_vcu_all/test
make
```

### Using GCC directly
```bash
cd src/sl_vcu_all/test
g++ -std=c++17 -Wall -Wextra -O2 -pthread -o imu_test imu_test.cpp
```

## Running

### Basic execution (both devices enabled)
```bash
./imu_test
```

### Enable only accelerometer
```bash
./imu_test --accel-only
```

### Enable only gyroscope
```bash
./imu_test --gyro-only
```

### Enable both devices (explicit)
```bash
./imu_test --both
```

### Show help
```bash
./imu_test --help
```

### With sudo (if device permissions require it)
```bash
sudo ./imu_test [options]
```

## Output Format

The program prints real-time data from enabled sensors:

```
ACCEL: timestamp=1234567890123456789 x=0.123456 y=0.234567 z=9.876543 m/s^2
  Time diff (accel-prev): 5000000 ns
  Time diff (accel-gyro): 1234567 ns
GYRO:  timestamp=1234567890123456790 x=0.001234 y=0.002345 z=0.003456 rad/s
  Time diff (gyro-prev): 5000000 ns
  Time diff (gyro-accel): -1234567 ns
```

### Data Fields

**Accelerometer data:**
- `timestamp`: Device timestamp in nanoseconds
- `x`, `y`, `z`: Linear acceleration in m/s²

**Gyroscope data:**
- `timestamp`: Device timestamp in nanoseconds
- `x`, `y`, `z`: Angular velocity in rad/s

**Timestamp comparisons:**
- `Time diff (accel-prev)`: Time difference between current and previous accelerometer readings
- `Time diff (gyro-prev)`: Time difference between current and previous gyroscope readings
- `Time diff (accel-gyro)`: Time difference between accelerometer and gyroscope readings
- `Time diff (gyro-accel)`: Time difference between gyroscope and accelerometer readings
- Positive value: current sensor timestamp is later
- Negative value: current sensor timestamp is earlier

## Command Line Options

The program supports the following command line options:

| Option | Description |
|--------|-------------|
| `--accel-only` | Enable only accelerometer (device2) |
| `--gyro-only` | Enable only gyroscope (device1) |
| `--both` | Enable both devices (default) |
| `--quiet`, `-q` | Reduce output for faster operation |
| `--help`, `-h` | Show help message |

### Examples:
```bash
# Test only accelerometer
./imu_test --accel-only

# Test only gyroscope
./imu_test --gyro-only

# Test both devices (default behavior)
./imu_test
./imu_test --both

# Fast operation with reduced output
./imu_test --quiet
./imu_test --gyro-only --quiet
```

## Configuration

The program uses the same configuration as the ImuSensor ROS2 node:

| Parameter | Value | Description |
|-----------|-------|-------------|
| Sampling frequency | 200 Hz | Data acquisition rate |
| Accelerometer sensitivity | 4g | Full-scale range |
| Gyroscope sensitivity | 2000 dps | Full-scale range |
| Accelerometer scale | 0.001196 | Conversion factor |
| Gyroscope scale | 0.001064 | Conversion factor |
| Buffer length | 1920 bytes | Device buffer size (increased for faster reading) |
| Poll timeout | 10 ms | Maximum wait time for data (reduced for faster response) |

## Performance Optimizations

The program includes several optimizations for fast data acquisition:

### Fast Reading Mode
- **Continuous Reading**: Uses non-blocking reads in a loop to drain all available data
- **Reduced Poll Timeout**: 10ms timeout instead of 1000ms for faster response
- **Larger Buffers**: 1920-byte buffers to handle multiple frames per read
- **Efficient Frame Processing**: Processes multiple frames (16 bytes each) in a single read operation

### Quiet Mode
Use `--quiet` or `-q` for maximum performance:
- Reduces console output overhead
- Eliminates "read X bytes" messages
- Maintains data output and timestamp comparisons
- Significantly improves data acquisition speed

### Typical Performance
- **Normal Mode**: ~200Hz with full output
- **Quiet Mode**: Higher throughput with minimal output overhead
- **Multiple Frames**: Handles 2-3 frames per read (32-48 bytes typical)

## Troubleshooting

### Permission Issues
If you get permission errors accessing `/dev/iio:device*`, try:
```bash
sudo ./imu_test
```

### Device Not Found
If devices `/dev/iio:device1` or `/dev/iio:device2` don't exist:
1. Check if IIO devices are present: `ls /dev/iio:device*`
2. Check sysfs: `ls /sys/bus/iio/devices/`
3. Verify IMU hardware is connected and drivers are loaded

### No Data Output
If the program runs but shows no sensor data:
1. Check device configuration files exist in sysfs
2. Verify buffer is enabled: `cat /sys/bus/iio/devices/iio:device*/buffer/enable`
3. Check sampling frequency: `cat /sys/bus/iio/devices/iio:device*/sampling_frequency`

### Build Issues
If compilation fails:
1. Ensure g++ supports C++17: `g++ --version`
2. Install build essentials: `sudo apt install build-essential`
3. Check for missing headers

## Stopping the Program

The program runs for 30 seconds by default, then automatically stops. You can also stop it early with:
- `Ctrl+C` (SIGINT)
- `Ctrl+Z` then `kill %1` (background and kill)

## Cleaning Up

To remove build artifacts:
```bash
make clean
```

## Data Structures

The program uses separate buffer frame structures for each device type:

### Accelerometer Frame Structure
```cpp
struct accel_device_buffer_frame {
    uint8_t  acc_x_h, acc_x_l;    // X-axis acceleration (high/low bytes)
    uint8_t  acc_y_h, acc_y_l;    // Y-axis acceleration (high/low bytes)
    uint8_t  acc_z_h, acc_z_l;    // Z-axis acceleration (high/low bytes)
    uint8_t  padding[10];          // Padding to match expected frame size
    uint8_t  timestamp[8];         // Device timestamp
};
```

### Gyroscope Frame Structure
```cpp
struct gyro_device_buffer_frame {
    uint8_t  gyro_x_h, gyro_x_l;  // X-axis angular velocity (high/low bytes)
    uint8_t  gyro_y_h, gyro_y_l;  // Y-axis angular velocity (high/low bytes)
    uint8_t  gyro_z_h, gyro_z_l;  // Z-axis angular velocity (high/low bytes)
    uint8_t  padding[10];          // Padding to match expected frame size
    uint8_t  timestamp[8];         // Device timestamp
};
```

## Notes

- This is a standalone program with no ROS2 dependencies
- Uses separate data structures for accelerometer and gyroscope devices
- Supports selective enabling/disabling of individual devices via command line options
- Designed for testing and debugging IMU hardware
- Timestamps are compared both with previous readings from the same device and between devices
- Enabled devices are monitored in a single thread using poll() for efficient concurrent data acquisition
- Provides detailed timestamp analysis for synchronization testing
- Optimized for fast data acquisition with continuous reading and reduced latency
- Supports quiet mode for maximum performance with minimal output overhead
