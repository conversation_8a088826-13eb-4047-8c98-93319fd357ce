#include <iostream>
#include <chrono>
#include <cstring>
#include <cmath>
#include <thread>
#include <atomic>
#include <fcntl.h>
#include <poll.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/time.h>
#include <vector>
#include <iomanip>

// Accelerometer device buffer frame structure
struct accel_device_buffer_frame
{
    uint8_t  acc_x_h;
    uint8_t  acc_x_l;
    uint8_t  acc_y_h;
    uint8_t  acc_y_l;
    uint8_t  acc_z_h;
    uint8_t  acc_z_l;
    uint8_t  padding[2];  // Padding to match expected frame size
    uint8_t  timestamp[8];
};

// Gyroscope device buffer frame structure
struct gyro_device_buffer_frame
{
    uint8_t  gyro_x_h;
    uint8_t  gyro_x_l;
    uint8_t  gyro_y_h;
    uint8_t  gyro_y_l;
    uint8_t  gyro_z_h;
    uint8_t  gyro_z_l;
    uint8_t  padding[2];  // Padding to match expected frame size
    uint8_t  timestamp[8];
};

class ImuTester
{
public:
    ImuTester(bool enable_accel = true, bool enable_gyro = true, bool verbose = true) :
        accel_device_fd_(-1),
        gyro_device_fd_(-1),
        keep_monitoring_(true),
        accel_last_timestamp_(0),
        gyro_last_timestamp_(0),
        accel_prev_timestamp_(0),
        gyro_prev_timestamp_(0),
        enable_accel_(enable_accel),
        enable_gyro_(enable_gyro),
        accel_partial_size_(0),
        gyro_partial_size_(0),
        verbose_output_(verbose)
    {
        // Configuration values (optimized for fast reading)
        poll_timeout_ = 10;  // Reduced timeout for faster response
        imu_sensor_acc_sensitivity_ = 2;
        imu_sensor_gyro_sensitivity_ = 2000;

        // Device paths (swapped: device1=gyro, device2=accel)
        gyro_device_path_ = "/dev/iio:device1";
        accel_device_path_ = "/dev/iio:device2";

        // Accel device configuration paths (now device2)
        accel_buff_enable_file_ = "/sys/bus/iio/devices/iio:device2/buffer/enable";
        accel_sampling_freq_file_ = "/sys/bus/iio/devices/iio:device2/sampling_frequency";
        accel_acc_scale_file_ = "/sys/bus/iio/devices/iio:device2/in_accel_scale";
        accel_scan_element_acc_x_file_ = "/sys/bus/iio/devices/iio:device2/scan_elements/in_accel_x_en";
        accel_scan_element_acc_y_file_ = "/sys/bus/iio/devices/iio:device2/scan_elements/in_accel_y_en";
        accel_scan_element_acc_z_file_ = "/sys/bus/iio/devices/iio:device2/scan_elements/in_accel_z_en";
        accel_scan_element_timestamp_file_ = "/sys/bus/iio/devices/iio:device2/scan_elements/in_timestamp_en";
        accel_timestamp_clock_file_ = "/sys/bus/iio/devices/iio:device2/current_timestamp_clock";
        accel_buffer_length_file_ = "/sys/bus/iio/devices/iio:device2/buffer/length";

        // Gyro device configuration paths (now device1)
        gyro_buff_enable_file_ = "/sys/bus/iio/devices/iio:device1/buffer/enable";
        gyro_sampling_freq_file_ = "/sys/bus/iio/devices/iio:device1/sampling_frequency";
        gyro_gyro_scale_file_ = "/sys/bus/iio/devices/iio:device1/in_anglvel_scale";
        gyro_scan_element_gyro_x_file_ = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_x_en";
        gyro_scan_element_gyro_y_file_ = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_y_en";
        gyro_scan_element_gyro_z_file_ = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_z_en";
        gyro_scan_element_timestamp_file_ = "/sys/bus/iio/devices/iio:device1/scan_elements/in_timestamp_en";
        gyro_timestamp_clock_file_ = "/sys/bus/iio/devices/iio:device1/current_timestamp_clock";
        gyro_buffer_length_file_ = "/sys/bus/iio/devices/iio:device1/buffer/length";

        // Configuration values
        buff_enable_on_ = "1";
        buff_enable_off_ = "0";
        sampling_freq_value_ = "200.0";
        acc_scale_value_ = "0.000598550";
        gyro_scale_value_ = "0.001065264";
        timestamp_clock_value_ = "realtime";
        buffer_length_value_ = "1920";  // Increased buffer size for faster reading
    }

    ~ImuTester()
    {
        stop();
    }

    bool initialize()
    {
        std::cout << "Initializing IMU Tester..." << std::endl;
        std::cout << "Accelerometer: " << (enable_accel_ ? "ENABLED" : "DISABLED") << std::endl;
        std::cout << "Gyroscope: " << (enable_gyro_ ? "ENABLED" : "DISABLED") << std::endl;

        // Setup accelerometer device if enabled
        if (enable_accel_) {
            if (!setupAccelDevice()) {
                std::cerr << "Failed to setup accelerometer device" << std::endl;
                return false;
            }
        }

        // Setup gyroscope device if enabled
        if (enable_gyro_) {
            if (!setupGyroDevice()) {
                std::cerr << "Failed to setup gyroscope device" << std::endl;
                return false;
            }
        }

        // Allocate data buffers
        int buffer_size = std::stoi(buffer_length_value_);
        accel_data_buf_.resize(buffer_size);
        gyro_data_buf_.resize(buffer_size);

        // Initialize partial frame buffers
        accel_partial_buf_.resize(sizeof(accel_device_buffer_frame));
        gyro_partial_buf_.resize(sizeof(gyro_device_buffer_frame));
        accel_partial_size_ = 0;
        gyro_partial_size_ = 0;

        std::cout << "IMU Tester initialized successfully" << std::endl;
        return true;
    }

    void start()
    {
        std::cout << "Starting IMU monitoring..." << std::endl;

        // Enable both buffers
        // if (!start_both_buffers())
        // {
        //     std::cerr << "Failed to start both buffers" << std::endl;
        //     return;
        // }

        // Start single monitoring thread for both devices
        imu_thread_ = std::thread(&ImuTester::monitorImuDevices, this);
    }

    void stop()
    {
        keep_monitoring_ = false;

        if (imu_thread_.joinable()) {
            imu_thread_.join();
        }

        // Disable buffers
        enableAccelBuffer(false);
        enableGyroBuffer(false);

        // Close devices
        if (accel_device_fd_ >= 0) {
            close(accel_device_fd_);
            accel_device_fd_ = -1;
        }

        if (gyro_device_fd_ >= 0) {
            close(gyro_device_fd_);
            gyro_device_fd_ = -1;
        }

        std::cout << "IMU Tester stopped" << std::endl;
    }

private:
    // Device file descriptors
    int accel_device_fd_;
    int gyro_device_fd_;

    // Device paths
    std::string accel_device_path_;
    std::string gyro_device_path_;

    // Configuration parameters
    int poll_timeout_;
    int imu_sensor_acc_sensitivity_;
    int imu_sensor_gyro_sensitivity_;

    // Accelerometer configuration file paths
    std::string accel_buff_enable_file_;
    std::string accel_sampling_freq_file_;
    std::string accel_acc_scale_file_;
    std::string accel_scan_element_acc_x_file_;
    std::string accel_scan_element_acc_y_file_;
    std::string accel_scan_element_acc_z_file_;
    std::string accel_scan_element_timestamp_file_;
    std::string accel_timestamp_clock_file_;
    std::string accel_buffer_length_file_;

    // Gyroscope configuration file paths
    std::string gyro_buff_enable_file_;
    std::string gyro_sampling_freq_file_;
    std::string gyro_gyro_scale_file_;
    std::string gyro_scan_element_gyro_x_file_;
    std::string gyro_scan_element_gyro_y_file_;
    std::string gyro_scan_element_gyro_z_file_;
    std::string gyro_scan_element_timestamp_file_;
    std::string gyro_timestamp_clock_file_;
    std::string gyro_buffer_length_file_;

    // Configuration values
    std::string buff_enable_on_;
    std::string buff_enable_off_;
    std::string sampling_freq_value_;
    std::string acc_scale_value_;
    std::string gyro_scale_value_;
    std::string timestamp_clock_value_;
    std::string buffer_length_value_;

    // Data buffers with partial frame management
    std::vector<uint8_t> accel_data_buf_;
    std::vector<uint8_t> gyro_data_buf_;
    std::vector<uint8_t> accel_partial_buf_;
    std::vector<uint8_t> gyro_partial_buf_;
    size_t accel_partial_size_;
    size_t gyro_partial_size_;

    // Single monitoring thread for both devices
    std::thread imu_thread_;
    std::atomic<bool> keep_monitoring_;

    // Last timestamps for comparison
    uint64_t accel_last_timestamp_;
    uint64_t gyro_last_timestamp_;

    // Previous timestamps for time difference calculation
    uint64_t accel_prev_timestamp_;
    uint64_t gyro_prev_timestamp_;

    // Enable/disable flags for each device
    bool enable_accel_;
    bool enable_gyro_;

    // Verbosity control
    bool verbose_output_;

    bool writeToFile(const std::string& file_path, const std::string& value)
    {
        int fd = ::open(file_path.c_str(), O_WRONLY);
        if (fd < 0) {
            std::cerr << "Error opening " << file_path << ": " << strerror(errno) << std::endl;
            return false;
        }

        int err = ::write(fd, value.c_str(), value.size() + 1);
        if (err < 0) {
            std::cerr << "Error writing to " << file_path << ": " << strerror(errno) << std::endl;
            ::close(fd);
            return false;
        }

        err = ::close(fd);
        if (err < 0) {
            std::cerr << "Error closing " << file_path << ": " << strerror(errno) << std::endl;
            return false;
        }

        return true;
    }

    bool enableAccelBuffer(bool enable)
    {
        const std::string& value = enable ? buff_enable_on_ : buff_enable_off_;
        return writeToFile(accel_buff_enable_file_, value);
    }

    bool enableGyroBuffer(bool enable)
    {
        const std::string& value = enable ? buff_enable_on_ : buff_enable_off_;
        return writeToFile(gyro_buff_enable_file_, value);
    }

    bool setupAccelDevice()
    {
        std::cout << "Setting up accelerometer device..." << std::endl;

        // Disable buffer first
        if (!enableAccelBuffer(false)) {
            std::cerr << "Failed to disable accel buffer" << std::endl;
            return false;
        }

        // Set sampling frequency
        if (!writeToFile(accel_sampling_freq_file_, sampling_freq_value_)) {
            std::cerr << "Failed to set accel sampling frequency" << std::endl;
            return false;
        }

        // Set accelerometer scale
        if (!writeToFile(accel_acc_scale_file_, acc_scale_value_)) {
            std::cerr << "Failed to set accel scale" << std::endl;
            return false;
        }

        // Enable scan elements
        if (!writeToFile(accel_scan_element_acc_x_file_, buff_enable_on_) ||
            !writeToFile(accel_scan_element_acc_y_file_, buff_enable_on_) ||
            !writeToFile(accel_scan_element_acc_z_file_, buff_enable_on_) ||
            !writeToFile(accel_scan_element_timestamp_file_, buff_enable_on_)) {
            std::cerr << "Failed to enable accel scan elements" << std::endl;
            return false;
        }

        // Set timestamp clock
        if (!writeToFile(accel_timestamp_clock_file_, timestamp_clock_value_)) {
            std::cerr << "Failed to set accel timestamp clock" << std::endl;
            return false;
        }

        // Set buffer length
        if (!writeToFile(accel_buffer_length_file_, buffer_length_value_)) {
            std::cerr << "Failed to set accel buffer length" << std::endl;
            return false;
        }

        // Open accelerometer device
        accel_device_fd_ = ::open(accel_device_path_.c_str(), O_RDONLY | O_NONBLOCK);
        if (accel_device_fd_ < 0) {
            std::cerr << "Failed to open accel device " << accel_device_path_
                      << ": " << strerror(errno) << std::endl;
            return false;
        }

        std::cout << "Accel device opened successfully, fd = " << accel_device_fd_ << std::endl;

        // Enable buffer
        if (!enableAccelBuffer(true)) {
            std::cerr << "Failed to enable accel buffer" << std::endl;
            close(accel_device_fd_);
            accel_device_fd_ = -1;
            return false;
        }

        return true;
    }

    bool setupGyroDevice()
    {
        std::cout << "Setting up gyroscope device..." << std::endl;

        // Disable buffer first
        if (!enableGyroBuffer(false)) {
            std::cerr << "Failed to disable gyro buffer" << std::endl;
            return false;
        }

        // Set sampling frequency
        if (!writeToFile(gyro_sampling_freq_file_, sampling_freq_value_)) {
            std::cerr << "Failed to set gyro sampling frequency" << std::endl;
            return false;
        }

        // Set gyroscope scale
        if (!writeToFile(gyro_gyro_scale_file_, gyro_scale_value_)) {
            std::cerr << "Failed to set gyro scale" << std::endl;
            return false;
        }

        // Enable scan elements
        if (!writeToFile(gyro_scan_element_gyro_x_file_, buff_enable_on_) ||
            !writeToFile(gyro_scan_element_gyro_y_file_, buff_enable_on_) ||
            !writeToFile(gyro_scan_element_gyro_z_file_, buff_enable_on_) ||
            !writeToFile(gyro_scan_element_timestamp_file_, buff_enable_on_)) {
            std::cerr << "Failed to enable gyro scan elements" << std::endl;
            return false;
        }

        // Set timestamp clock
        if (!writeToFile(gyro_timestamp_clock_file_, timestamp_clock_value_)) {
            std::cerr << "Failed to set gyro timestamp clock" << std::endl;
            return false;
        }

        // Set buffer length
        if (!writeToFile(gyro_buffer_length_file_, buffer_length_value_)) {
            std::cerr << "Failed to set gyro buffer length" << std::endl;
            return false;
        }

        // Open gyroscope device
        gyro_device_fd_ = ::open(gyro_device_path_.c_str(), O_RDONLY | O_NONBLOCK);
        if (gyro_device_fd_ < 0) {
            std::cerr << "Failed to open gyro device " << gyro_device_path_
                      << ": " << strerror(errno) << std::endl;
            return false;
        }

        std::cout << "Gyro device opened successfully, fd = " << gyro_device_fd_ << std::endl;

        // Enable buffer
        if (!enableGyroBuffer(true)) {
            std::cerr << "Failed to enable gyro buffer" << std::endl;
            close(gyro_device_fd_);
            gyro_device_fd_ = -1;
            return false;
        }

        return true;
    }

    bool start_both_buffers()
    {
        // Enable both buffers
        if (!enableAccelBuffer(true) || !enableGyroBuffer(true)) {
            std::cerr << "Failed to enable both buffers" << std::endl;
            if (accel_device_fd_ >= 0) {
                close(accel_device_fd_);
                accel_device_fd_ = -1;
            }

            if (gyro_device_fd_ >= 0) {
                close(gyro_device_fd_);
                gyro_device_fd_ = -1;
            }

            return false;
        }

        return true;
    }


    void monitorImuDevices()
    {
        std::cout << "Starting IMU monitoring thread" << std::endl;

        // Setup poll file descriptors for enabled devices
        struct pollfd fds[2];
        int num_fds = 0;
        int accel_fd_index = -1;
        int gyro_fd_index = -1;

        if (enable_accel_) {
            fds[num_fds].fd = accel_device_fd_;
            fds[num_fds].events = POLLIN;
            fds[num_fds].revents = 0;
            accel_fd_index = num_fds;
            num_fds++;
        }

        if (enable_gyro_) {
            fds[num_fds].fd = gyro_device_fd_;
            fds[num_fds].events = POLLIN;
            fds[num_fds].revents = 0;
            gyro_fd_index = num_fds;
            num_fds++;
        }

        // Calculate conversion factors
        double acc_q16_factor = (imu_sensor_acc_sensitivity_ * (1<<16) / (1<<15));
        double gyro_q16_factor = (imu_sensor_gyro_sensitivity_ * (1<<16) / (1<<15));

        while (keep_monitoring_) {
            //int ret = poll(fds, num_fds, poll_timeout_);
            int ret = poll(fds, num_fds, 2);

            if (ret < 0) {
                std::cerr << "Poll error: " << strerror(errno) << std::endl;
                continue;
            } else if (ret == 0) {
                // No timeout message for faster operation
                continue;
            }

            // Check accelerometer data
            if (enable_accel_ && accel_fd_index >= 0 && (fds[accel_fd_index].revents & POLLIN)) {
                // Read all available data continuously
                while (true) {
                    ret = ::read(fds[accel_fd_index].fd, accel_data_buf_.data(), accel_data_buf_.size());

                    if (ret < 0) {
                        if (errno == EAGAIN || errno == EWOULDBLOCK) {
                            // No more data available
                            break;
                        }
                        std::cerr << "Accel read error: " << strerror(errno) << std::endl;
                        break;
                    } else if (ret == 0) {
                        // No data
                        break;
                    } else {
                        if (verbose_output_) {
                            std::cout << "Accel read: " << ret << " bytes" << std::endl;
                        }
                        processAccelData(ret, acc_q16_factor);
                    }
                }
                fds[accel_fd_index].revents = 0;
            }

            // Check gyroscope data
            if (enable_gyro_ && gyro_fd_index >= 0 && (fds[gyro_fd_index].revents & POLLIN)) {
                // Read all available data continuously
                while (true) {
                    ret = ::read(fds[gyro_fd_index].fd, gyro_data_buf_.data(), gyro_data_buf_.size());

                    if (ret < 0) {
                        if (errno == EAGAIN || errno == EWOULDBLOCK) {
                            // No more data available
                            break;
                        }
                        std::cerr << "Gyro read error: " << strerror(errno) << std::endl;
                        break;
                    } else if (ret == 0) {
                        // No data
                        break;
                    } else {
                        if (verbose_output_) {
                            std::cout << "Gyro read: " << ret << " bytes" << std::endl;
                        }
                        processGyroData(ret, gyro_q16_factor);
                    }
                }
                fds[gyro_fd_index].revents = 0;
            }
        }

        std::cout << "IMU monitoring thread stopped" << std::endl;
    }

    void processAccelData(int bytes_read, double acc_q16_factor)
    {
        // Process accelerometer data
        int num_frames = bytes_read / sizeof(accel_device_buffer_frame);

        for (int i = 0; i < num_frames; ++i) {
            accel_device_buffer_frame* frame = reinterpret_cast<accel_device_buffer_frame*>(
                accel_data_buf_.data() + i * sizeof(accel_device_buffer_frame));

            // Extract timestamp
            uint64_t timestamp = *reinterpret_cast<uint64_t*>(frame->timestamp);

            // Skip if same timestamp as last frame
            if (timestamp == accel_last_timestamp_) {
                std::cout << "Accel duplicate timestamp: " << timestamp << std::endl;
                continue;
            }

            // Store previous timestamp for comparison
            accel_prev_timestamp_ = accel_last_timestamp_;
            accel_last_timestamp_ = timestamp;

            // Extract accelerometer data (only accel data in this frame)
            int16_t acc_x = (frame->acc_x_h << 8) | frame->acc_x_l;
            int16_t acc_y = (frame->acc_y_h << 8) | frame->acc_y_l;
            int16_t acc_z = (frame->acc_z_h << 8) | frame->acc_z_l;

            // Convert to proper units (m/s^2)
            double acc_x_ms2 = static_cast<double>(acc_x) * acc_q16_factor / 65536.0 * 9.81;
            double acc_y_ms2 = static_cast<double>(acc_y) * acc_q16_factor / 65536.0 * 9.81;
            double acc_z_ms2 = static_cast<double>(acc_z) * acc_q16_factor / 65536.0 * 9.81;

            // Print accelerometer data
            std::cout << std::fixed << std::setprecision(6);
            std::cout << "ACCEL: timestamp=" << timestamp
                      << " x=" << acc_x_ms2
                      << " y=" << acc_y_ms2
                      << " z=" << acc_z_ms2 << " m/s^2" << std::endl;

            // Compare timestamp with previous accel data
            if (accel_prev_timestamp_ > 0) {
                int64_t time_diff_prev = static_cast<int64_t>(timestamp) - static_cast<int64_t>(accel_prev_timestamp_);
                std::cout << "  Time diff (accel-prev): " << time_diff_prev << " ns" << std::endl;
            }

            // Compare timestamps if gyro data is available
            if (enable_gyro_ && gyro_last_timestamp_ > 0) {
                int64_t time_diff = static_cast<int64_t>(timestamp) - static_cast<int64_t>(gyro_last_timestamp_);
                std::cout << "  Time diff (accel-gyro): " << time_diff << " ns" << std::endl;
            }
        }
    }

    void processGyroData(int bytes_read, double gyro_q16_factor)
    {
        // Process gyroscope data
        int num_frames = bytes_read / sizeof(gyro_device_buffer_frame);

        for (int i = 0; i < num_frames; ++i) {
            gyro_device_buffer_frame* frame = reinterpret_cast<gyro_device_buffer_frame*>(
                gyro_data_buf_.data() + i * sizeof(gyro_device_buffer_frame));

            // Extract timestamp
            uint64_t timestamp = *reinterpret_cast<uint64_t*>(frame->timestamp);

            // Skip if same timestamp as last frame
            if (timestamp == gyro_last_timestamp_) {
                std::cout << "Gyro duplicate timestamp: " << timestamp << std::endl;
                continue;
            }

            // Store previous timestamp for comparison
            gyro_prev_timestamp_ = gyro_last_timestamp_;
            gyro_last_timestamp_ = timestamp;

            // Extract gyroscope data (only gyro data in this frame)
            int16_t gyro_x = (frame->gyro_x_h << 8) | frame->gyro_x_l;
            int16_t gyro_y = (frame->gyro_y_h << 8) | frame->gyro_y_l;
            int16_t gyro_z = (frame->gyro_z_h << 8) | frame->gyro_z_l;

            // Convert to proper units (rad/s)
            double gyro_x_rads = static_cast<double>(gyro_x) * gyro_q16_factor / 65536.0 * M_PI / 180.0;
            double gyro_y_rads = static_cast<double>(gyro_y) * gyro_q16_factor / 65536.0 * M_PI / 180.0;
            double gyro_z_rads = static_cast<double>(gyro_z) * gyro_q16_factor / 65536.0 * M_PI / 180.0;

            // Print gyroscope data
            std::cout << std::fixed << std::setprecision(6);
            std::cout << "GYRO:  timestamp=" << timestamp
                      << " x=" << gyro_x_rads
                      << " y=" << gyro_y_rads
                      << " z=" << gyro_z_rads << " rad/s" << std::endl;

            // Compare timestamp with previous gyro data
            if (gyro_prev_timestamp_ > 0) {
                int64_t time_diff_prev = static_cast<int64_t>(timestamp) - static_cast<int64_t>(gyro_prev_timestamp_);
                std::cout << "  Time diff (gyro-prev): " << time_diff_prev << " ns" << std::endl;
            }

            // Compare timestamps if accel data is available
            if (enable_accel_ && accel_last_timestamp_ > 0) {
                int64_t time_diff = static_cast<int64_t>(timestamp) - static_cast<int64_t>(accel_last_timestamp_);
                std::cout << "  Time diff (gyro-accel): " << time_diff << " ns" << std::endl;
            }
        }
    }
};

void printUsage(const char* program_name)
{
    std::cout << "Usage: " << program_name << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  --accel-only    Enable only accelerometer (device2)" << std::endl;
    std::cout << "  --gyro-only     Enable only gyroscope (device1)" << std::endl;
    std::cout << "  --both          Enable both devices (default)" << std::endl;
    std::cout << "  --quiet, -q     Reduce output for faster operation" << std::endl;
    std::cout << "  --help, -h      Show this help message" << std::endl;
    std::cout << std::endl;
    std::cout << "Device mapping:" << std::endl;
    std::cout << "  device1 (/dev/iio:device1) = Gyroscope" << std::endl;
    std::cout << "  device2 (/dev/iio:device2) = Accelerometer" << std::endl;
}

int main(int argc, char* argv[])
{
    bool enable_accel = true;
    bool enable_gyro = true;
    bool verbose = true;

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--accel-only") {
            enable_accel = true;
            enable_gyro = false;
        } else if (arg == "--gyro-only") {
            enable_accel = false;
            enable_gyro = true;
        } else if (arg == "--both") {
            enable_accel = true;
            enable_gyro = true;
        } else if (arg == "--quiet" || arg == "-q") {
            verbose = false;
        } else if (arg == "--help" || arg == "-h") {
            printUsage(argv[0]);
            return 0;
        } else {
            std::cerr << "Unknown option: " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }

    std::cout << "IMU Test Program" << std::endl;
    std::cout << "Testing gyroscope (/dev/iio:device1) and accelerometer (/dev/iio:device2)" << std::endl;

    ImuTester tester(enable_accel, enable_gyro, verbose);

    if (!tester.initialize()) {
        std::cerr << "Failed to initialize IMU tester" << std::endl;
        return 1;
    }

    tester.start();

    std::cout << "Press Ctrl+C to stop..." << std::endl;

    // Run for a while or until interrupted
    std::this_thread::sleep_for(std::chrono::seconds(30));

    tester.stop();

    std::cout << "IMU test completed" << std::endl;
    return 0;
}