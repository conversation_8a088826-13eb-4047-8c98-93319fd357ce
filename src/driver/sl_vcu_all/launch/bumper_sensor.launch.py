#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # Get the package directory
    package_dir = get_package_share_directory('sl_vcu_all')
    
    # Path to the parameter file
    params_file = os.path.join(package_dir, 'config', 'bumper_sensor.yaml')
    
    # Create the bumper sensor node
    bumper_sensor_node = Node(
        package='sl_vcu_all',
        executable='bumper_sensor_node',
        name='bumper_sensor',
        parameters=[params_file],
        output='screen'
    )
    
    return LaunchDescription([
        bumper_sensor_node
    ])
