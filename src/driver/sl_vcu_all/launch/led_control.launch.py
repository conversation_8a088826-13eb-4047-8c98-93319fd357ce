#!/usr/bin/env python3

"""
Launch file for LED Display Control Node.
This launch file starts the LED display control node with configurable parameters.
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    # Declare launch arguments
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('sl_vcu_all'),
            'config',
            'led_control.yaml'
        ]),
        description='Path to LED display control configuration file'
    )

    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level for the LED display control node'
    )

    # LED Display Control Node
    led_display_control_node = Node(
        package='sl_vcu_all',
        executable='led_display_control_node',
        name='led_display_control',
        parameters=[LaunchConfiguration('config_file')],
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')],
        output='screen',
        # emulate_tty=True
    )
    
    return LaunchDescription([
        # Launch arguments
        config_file_arg,
        log_level_arg,

        # Nodes
        led_display_control_node,
    ])

if __name__ == '__main__':
    generate_launch_description()
