#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # Declare launch arguments
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('sl_vcu_all'),
            'config',
            'imu_sensor.yaml'
        ]),
        description='Path to the IMU sensor configuration file'
    )

    publish_tf_arg = DeclareLaunchArgument(
        'publish_tf',
        default_value='false',
        description='Enable IMU TF publishing (true/false)'
    )

    parent_frame_arg = DeclareLaunchArgument(
        'parent_frame',
        default_value='base_link',
        description='Parent frame for IMU TF'
    )

    child_frame_arg = DeclareLaunchArgument(
        'child_frame',
        default_value='imu_link',
        description='Child frame for IMU TF'
    )

    # IMU sensor node
    imu_sensor_node = Node(
        package='sl_vcu_all',
        executable='imu_sensor_node',
        name='imu_sensor',
        parameters=[
            LaunchConfiguration('config_file'),
            {
                'publish_tf': LaunchConfiguration('publish_tf'),
                'parent_frame_id': LaunchConfiguration('parent_frame'),
                'child_frame_id': LaunchConfiguration('child_frame')
            }
        ],
        output='screen',
        # emulate_tty=True,
        # respawn=True,
        # respawn_delay=2.0
    )

    return LaunchDescription([
        config_file_arg,
        publish_tf_arg,
        parent_frame_arg,
        child_frame_arg,
        imu_sensor_node
    ])
