# SL VCU All Package

This package provides a comprehensive Vehicle Control Unit (VCU) system for robotic platforms with CAN bus communication, IMU sensor fusion, and robot localization.

## Features

### Core Components
1. **CAN Frame Dispatcher** - Handles CAN bus communication
2. **ZL Motor Controller** - Controls motors via CAN bus with odometry
3. **IMU Sensor Node** - Advanced IMU processing with bias correction and yaw integration
4. **Bumper Sensor Node** - Handles bumper/collision detection
5. **LED Display Control** - SK6812 LED strip control with effects and synchronization
6. **Battery Monitor** - Battery status monitoring and reporting
7. **Jack Control** - Hydraulic jack control system
8. **Robot Localization EKF** - Extended Kalman Filter for sensor fusion

### IMU Sensor Advanced Features
- **Initial Bias Filtering**: Uses configurable large offset for initial filtering
- **Dynamic Bias Calculation**: Calculates actual bias during first 10 seconds of operation
- **Yaw Integration**: Integrates filtered gyro Z data for accurate yaw estimation
- **Motion-Aware Bias Update**: Updates bias when robot is stationary and gyro variance is low
- **Dual IMU Output**: Publishes both raw and filtered IMU data
- **TF Publishing**: Optional TF transform publishing with integrated yaw orientation

### LED Display Control Features
- **Multi-Channel Support**: Control multiple SK6812 LED strips simultaneously
- **Channel-Based Hardware Communication**: Data sent by channel with combined parts data
- **Synchronized Control**: `sync_channels` mode for simultaneous hardware updates
- **Flexible Part Control**: Individual control of strip sections (whole, first half, second half)
- **Advanced Effects**: Breathing, flashing (with custom duty cycle), marquee, solid colors
- **Optional Parameters**: Only mode required, all other parameters use configurable defaults
- **Lenient Sync Validation**: Accepts minimum required data, ignores extras

## Topics

### Published Topics
- `sl_vcu_all/imu_data_raw` - Raw IMU data
- `sl_vcu_all/imu_data_filtered` - Bias-corrected IMU data with integrated yaw
- `sl_vcu_all/odom` - Motor controller odometry
- `odom` - Filtered odometry from EKF (when enabled)
- `motor_info` - Motor status information (optional)
- `bumper_state` - Bumper sensor state
- `battery_status` - Battery monitoring information
- `jack_status` - Jack control system status

### Subscribed Topics
- `cmd_vel` - Velocity commands for the robot
- CAN bus topics (when not using direct SocketCAN)

## Services

### LED Control Service
- `/led_control` - Control SK6812 LED strips with advanced features:
  - **Channel-based control**: Send data by channel, combining parts data
  - **Synchronized operation**: `sync_channels=true` latches all data first, then sends simultaneously
  - **Flexible parameters**: Only `mode` required, others optional with defaults
  - **Custom duty cycle**: `on_time_duty` parameter for flashing effects (0.0-1.0)
  - **Lenient validation**: Accepts minimum data, ignores extras

### Other Services
- `/jack_control` - Hydraulic jack control actions
- `/add_can_filter` - Add CAN message filters
- `/check_node_status` - Node health monitoring

## Launch Files

### Main Launch File
```bash
ros2 launch sl_vcu_all sl_vcu_all.launch.py
```

**Parameters:**
- `motor_config_file`: Path to motor controller config (default: config/zl_motor_controller.yaml)
- `imu_config_file`: Path to IMU sensor config (default: config/imu_sensor.yaml)
- `ekf_config_file`: Path to EKF config (default: config/robot_localization_ekf.yaml)
- `log_level`: Log level for all nodes (default: info)
- `odom_topic`: Output odometry topic name (default: odom)
- `enable_ekf`: Enable robot localization EKF (default: true)
- `imu_publish_tf`: Enable IMU TF publishing (default: false)
- `imu_parent_frame`: Parent frame for IMU TF (default: base_link)
- `imu_child_frame`: Child frame for IMU TF (default: imu_link)

### Individual Launch Files
```bash
# Launch only motor controller
ros2 launch sl_vcu_all zl_motor_controller.launch.py

# Launch only IMU sensor
ros2 launch sl_vcu_all imu_sensor.launch.py

# Launch IMU sensor with TF publishing
ros2 launch sl_vcu_all imu_sensor.launch.py publish_tf:=true parent_frame:=base_link child_frame:=imu_link

# Launch only EKF
ros2 launch sl_vcu_all robot_localization_ekf.launch.py

# Launch all VCU nodes (without EKF)
ros2 launch sl_vcu_all vcu_nodes.launch.py

# Launch LED control system
ros2 launch sl_vcu_all led_control.launch.py

# Launch battery monitor
ros2 launch sl_vcu_all battery_monitor.launch.py

# Launch jack control system
ros2 launch sl_vcu_all jack_control.launch.py
```

## Configuration

### IMU Sensor Configuration
Key parameters in `config/imu_sensor.yaml`:
- `initial_bias_offset`: Initial large offset for gyro bias filtering (default: 0.1 rad/s)
- `bias_calculation_time`: Time to calculate initial bias (default: 10.0 s)
- `bias_update_time`: Time window for bias update analysis (default: 5.0 s)
- `bias_update_threshold`: Standard deviation threshold for bias update (default: 0.01 rad/s)
- `cmd_vel_timeout`: Timeout for cmd_vel messages (default: 2.0 s)
- `publish_tf`: Enable TF transform publishing (default: false)
- `parent_frame_id`: Parent frame for TF transforms (default: base_link)
- `child_frame_id`: Child frame for TF transforms (default: imu_link)

### Motor Controller Configuration
Key parameters in `config/zl_motor_controller.yaml`:
- `wheel_diameter`: Wheel diameter in meters
- `wheel_separation`: Distance between wheels in meters
- `can_interface`: SocketCAN interface name (default: can0)
- `use_sockcan_direct`: Use direct SocketCAN vs ROS topics

### EKF Configuration
The EKF is configured in `config/robot_localization_ekf.yaml` to fuse:
- Motor controller odometry (position and velocity)
- Filtered IMU data (yaw orientation and angular velocity)
- Linear acceleration from IMU

## Hardware Setup

### CAN Bus Setup
1. Configure CAN interface:
```bash
sudo ip link set can0 type can bitrate 500000
sudo ip link set up can0
```

2. For testing, use virtual CAN:
```bash
sudo modprobe vcan
sudo ip link add dev vcan0 type vcan
sudo ip link set up vcan0
```

### IMU Setup
Run the IMU device setup script as root:
```bash
sudo ./scripts/setup_imu_devices.sh
```

## Usage Examples

### Basic Operation
```bash
# Launch the complete system
ros2 launch sl_vcu_all sl_vcu_all.launch.py

# Launch with IMU TF publishing enabled
ros2 launch sl_vcu_all sl_vcu_all.launch.py imu_publish_tf:=true

# Send velocity commands
ros2 topic pub /cmd_vel geometry_msgs/msg/Twist "{linear: {x: 0.5}, angular: {z: 0.0}}"

# Monitor IMU data
ros2 topic echo /sl_vcu_all/imu_data_filtered

# Monitor odometry
ros2 topic echo /odom

# Monitor TF transforms (if TF publishing is enabled)
ros2 run tf2_tools view_frames.py
ros2 topic echo /tf
```

### LED Control Examples
```bash
# Turn on red LEDs on channel 0
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0], channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'on', red_brightness: 255}]}]}"

# Synchronized breathing effect on both channels (green)
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0, 1], sync_channels: true, channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'breathing', green_brightness: 200, frequency: 0.5}]}]}"

# Flashing with custom duty cycle (25% on time)
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0], channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'flashing', blue_brightness: 128, frequency: 2.0, on_time_duty: 0.25}]}]}"

# Turn off all LEDs
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0, 1], sync_channels: true, channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'off'}]}]}"

# Run comprehensive test sequence
python3 src/sl_vcu_all/scripts/test_led_control.py
```

### Testing IMU Bias Correction
```bash
# Launch with debug logging
ros2 launch sl_vcu_all sl_vcu_all.launch.py log_level:=debug

# Monitor bias correction in logs
# Initial bias will be calculated in first 10 seconds
# Dynamic bias updates occur when robot is stationary
```

### Advanced Usage Examples
```bash
# Disable EKF for testing
ros2 launch sl_vcu_all sl_vcu_all.launch.py enable_ekf:=false

# Enable IMU TF publishing with custom frames
ros2 launch sl_vcu_all sl_vcu_all.launch.py \
    imu_publish_tf:=true \
    imu_parent_frame:=odom \
    imu_child_frame:=imu_base

# Launch with custom configuration files
ros2 launch sl_vcu_all sl_vcu_all.launch.py \
    imu_config_file:=/path/to/custom_imu.yaml \
    motor_config_file:=/path/to/custom_motor.yaml
```

## Troubleshooting

### Common Issues
1. **CAN Interface Not Found**: Ensure CAN interface is up and configured
2. **IMU Device Access**: Run IMU setup script with sudo permissions
3. **EKF Not Starting**: Check that IMU and odometry topics are publishing
4. **High IMU Drift**: Verify bias correction parameters and ensure robot starts stationary

### Diagnostic Tools
```bash
# Check node status
ros2 node list

# Monitor diagnostics (if diagnostic_aggregator is enabled)
ros2 topic echo /diagnostics_agg

# Check TF tree
ros2 run tf2_tools view_frames.py
```

## Dependencies
- ROS 2 Humble
- robot_localization package
- tf2_ros
- diagnostic_aggregator (optional)

Install missing dependencies:
```bash
sudo apt install ros-humble-robot-localization ros-humble-diagnostic-aggregator
```
