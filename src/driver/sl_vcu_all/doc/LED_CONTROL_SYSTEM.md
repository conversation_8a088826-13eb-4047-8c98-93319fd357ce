# SK6812 LED Control System

## Overview

The LED Control System provides comprehensive control over SK6812 LED strips using SPI communication. The system supports multiple channels, various lighting effects, and flexible configuration options with advanced synchronization capabilities.

## Features

- **Multiple Channels**: Support for up to 2 SPI LED strip channels (configurable)
- **Strip Partitioning**: Each strip can be divided into 2 controllable parts
- **Lighting Modes**:
  - Static on/off
  - Breathing light effect
  - Flashing patterns with configurable duty cycle
  - Marquee scrolling effects
- **Advanced Synchronization**:
  - Channel-based hardware communication
  - Synchronized channel control (`sync_channels`)
  - Part synchronization within channels (`sync_parts`)
- **Flexible Parameters**:
  - Only `mode` required, all others optional with defaults
  - Configurable on-time duty cycle for flashing (0.0-1.0)
  - Lenient validation (ignores extra data)
- **ROS2 Service Interface**: Easy integration with ROS2 systems
- **Real-time Effects**: 50Hz update rate for smooth animations

## Architecture

### Components

1. **SK6812SpiDriver**: Low-level SPI communication with LED strips
2. **LedStripController**: Individual strip management and effects
3. **LedControlNode**: ROS2 node providing service interface
4. **LedControl Service**: ROS2 service for LED control commands

### Hardware Requirements

- SK6812 LED strips (GRB format)
- SPI interface (e.g., `/dev/spidev0.0`, `/dev/spidev1.0`)
- Appropriate power supply for LED strips

## Configuration

### Parameters

The system uses the following ROS2 parameters (defined in `config/led_control.yaml`):

```yaml
led_control_node:
  ros__parameters:
    # Basic settings
    num_channels: 2                    # Number of LED strip channels
    auto_start_effects: true           # Auto-start effect threads
    
    # Default effect parameters
    default_frequency: 1.0             # Hz for breathing/flashing
    default_speed: 10.0                # pixels/second for marquee
    default_marquee_direction: true    # true=forward, false=reverse
    default_green_brightness: 255      # Default green brightness (0-255)
    default_red_brightness: 255        # Default red brightness (0-255)
    default_blue_brightness: 255       # Default blue brightness (0-255)
    default_on_time_duty: 0.5           # Default duty cycle for flashing (0.0-1.0)
    
    # Channel-specific configuration
    channel_0:
      spi_device: "/dev/spidev0.0"     # SPI device path
      spi_speed: 6400000               # SPI clock speed (6.4MHz)
      num_leds: 50                     # Number of LEDs
      first_part_leds: 25              # LEDs in first part
      second_part_leds: 25             # LEDs in second part
      enabled: true                    # Enable this channel

    channel_1:
      spi_device: "/dev/spidev1.0"     # SPI device path
      spi_speed: 6400000               # SPI clock speed
      num_leds: 30                     # Number of LEDs
      first_part_leds: 15              # LEDs in first part
      second_part_leds: 15             # LEDs in second part
      enabled: true                    # Enable this channel
```

## Usage

### Starting the LED Control Node

```bash
# Launch LED control node only
ros2 launch sl_vcu_all led_control.launch.py

# Launch with custom config
ros2 launch sl_vcu_all led_control.launch.py config_file:=/path/to/custom_config.yaml

# Launch as part of complete VCU system
ros2 launch sl_vcu_all sl_vcu_all.launch.py
```

### Service Interface

The LED control system provides a ROS2 service `/led_control` with the following interface:

#### Request Parameters

**Top-level parameters:**
- `channels` (uint8[]): Array of channel IDs to control
- `sync_channels` (bool): Whether to synchronize multiple channels
- `channel_data` (ChannelData[]): Array of channel data structures

**ChannelData structure:**
- `parts` (uint8[]): Array of part IDs (0=whole, 1=first half, 2=second half)
- `sync_parts` (bool): Whether to synchronize parts within this channel
- `parts_data` (PartData[]): Array of part data structures

**PartData structure:**
- `mode` (string): **Required** - Lighting mode ("off", "on", "breathing", "flashing", "marquee")
- `green_brightness` (uint8): **Optional** - Green brightness (0-255, default from config)
- `red_brightness` (uint8): **Optional** - Red brightness (0-255, default from config)
- `blue_brightness` (uint8): **Optional** - Blue brightness (0-255, default from config)
- `frequency` (float32): **Optional** - Frequency for breathing/flashing effects (Hz, default from config)
- `speed` (float32): **Optional** - Speed for marquee effect (pixels/second, default from config)
- `marquee_direction` (bool): **Optional** - Marquee direction (true=forward, false=reverse)
- `on_time_duty` (float32): **Optional** - Duty cycle for flashing (0.0-1.0, default 0.5)

**Key Features:**
- Only `mode` is required; all other fields are optional
- Zero values (0, 0.0) trigger default parameter usage
- `sync_channels=true`: Latches all channel data first, then sends to hardware simultaneously
- `sync_parts=true`: Uses first parts_data entry for all parts in the channel
- Lenient validation: Accepts minimum required data, ignores extras

#### Response

- `success` (bool): Whether the command succeeded
- `message` (string): Success or error message
- `affected_channels` (uint8[]): Channels that were actually affected

### Command Examples

```bash
# Turn on channel 0 with red color (using new structured interface)
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0], channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'on', red_brightness: 255}]}]}"

# Synchronized breathing effect on both channels (green)
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0, 1], sync_channels: true, channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'breathing', green_brightness: 200, frequency: 0.5}]}]}"

# Flashing with custom duty cycle (25% on time)
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0], channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'flashing', blue_brightness: 128, frequency: 2.0, on_time_duty: 0.25}]}]}"

# Multi-part control with different colors
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0], channel_data: [{parts: [1, 2], sync_parts: false, parts_data: [{mode: 'on', red_brightness: 255}, {mode: 'on', blue_brightness: 255}]}]}"

# Turn off all LEDs (only mode required)
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0, 1], sync_channels: true, channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'off'}]}]}"

# Using defaults (only specify what you need)
ros2 service call /led_control sl_vcu_all/srv/LedControl \
  "{channels: [0], channel_data: [{parts: [0], sync_parts: false, parts_data: [{mode: 'breathing', green_brightness: 100}]}]}"
```

### Python API Example

```python
import rclpy
from rclpy.node import Node
from sl_vcu_all.srv import LedControl
from sl_vcu_all.msg import ChannelData, PartData

class LedController(Node):
    def __init__(self):
        super().__init__('led_controller')
        self.client = self.create_client(LedControl, 'led_control')

    def set_breathing_effect(self, channels, green=0, red=0, blue=0, frequency=0.0, sync_channels=False):
        """Set breathing effect with optional parameters"""
        request = LedControl.Request()
        request.channels = channels
        request.sync_channels = sync_channels

        # Create channel data
        channel_data = ChannelData()
        channel_data.parts = [0]  # Whole strip
        channel_data.sync_parts = False

        # Create part data (only specify non-zero values)
        part_data = PartData()
        part_data.mode = 'breathing'  # Required
        if green > 0:
            part_data.green_brightness = green
        if red > 0:
            part_data.red_brightness = red
        if blue > 0:
            part_data.blue_brightness = blue
        if frequency > 0.0:
            part_data.frequency = frequency
        # on_time_duty will use default (0.5) since we don't set it

        channel_data.parts_data = [part_data]
        request.channel_data = [channel_data]

        future = self.client.call_async(request)
        return future

    def set_flashing_with_duty(self, channels, green=0, red=0, blue=0, frequency=2.0, duty_cycle=0.5):
        """Set flashing effect with custom duty cycle"""
        request = LedControl.Request()
        request.channels = channels
        request.sync_channels = False

        channel_data = ChannelData()
        channel_data.parts = [0]
        channel_data.sync_parts = False

        part_data = PartData()
        part_data.mode = 'flashing'
        part_data.green_brightness = green
        part_data.red_brightness = red
        part_data.blue_brightness = blue
        part_data.frequency = frequency
        part_data.on_time_duty = duty_cycle  # Custom duty cycle

        channel_data.parts_data = [part_data]
        request.channel_data = [channel_data]

        return self.client.call_async(request)
```

## Testing

Use the provided test script to verify the LED control system:

```bash
# Run test sequence
python3 src/sl_vcu_all/scripts/test_led_control.py
```

The test script demonstrates:
- Solid colors on individual channels
- Breathing effects with synchronization
- Flashing patterns on strip parts
- Marquee effects with different parameters

## Hardware Setup

### SPI Configuration

1. Enable SPI interface on your system
2. Ensure SPI devices are accessible (e.g., `/dev/spidev0.0`)
3. Set appropriate permissions for SPI devices
4. Connect SK6812 strips to SPI MOSI pins

### LED Strip Wiring

- **VCC**: Connect to appropriate power supply (5V for SK6812)
- **GND**: Connect to common ground
- **DIN**: Connect to SPI MOSI pin
- **DOUT**: Chain to next strip if needed

### Power Considerations

- Calculate power requirements: ~60mA per LED at full brightness
- Use appropriate power supply capacity
- Consider voltage drop for long strips
- Add decoupling capacitors near LED strips

## Troubleshooting

### Common Issues

1. **SPI Permission Denied**
   - Add user to `spi` group: `sudo usermod -a -G spi $USER`
   - Set SPI device permissions: `sudo chmod 666 /dev/spidev*`

2. **LEDs Not Responding**
   - Check SPI device path in configuration
   - Verify SPI clock speed (6.4MHz recommended for SK6812)
   - Check LED strip power supply
   - Verify wiring connections

3. **Flickering or Incorrect Colors**
   - Adjust SPI timing parameters
   - Check for electromagnetic interference
   - Verify LED strip type (SK6812 vs WS2812)

4. **Service Not Available**
   - Check if LED control node is running
   - Verify ROS2 environment setup
   - Check for configuration file errors

### Debug Commands

```bash
# Check if service is available
ros2 service list | grep led_control

# Monitor service calls
ros2 service echo /led_control

# Check node status
ros2 node info /led_control_node

# View parameters
ros2 param list /led_control_node
```

## Performance Notes

- Effect update rate: 50Hz for smooth animations
- SPI communication is non-blocking
- Multiple channels can be controlled simultaneously
- Effect threads are automatically managed

## Advanced Features

### Channel-Based Hardware Communication
The system now sends data to hardware by channel rather than by individual parts. This means:
- All parts data for a channel is combined before sending to hardware
- More efficient communication with LED strips
- Better synchronization between parts within a channel

### Synchronized Channel Control
When `sync_channels=true`:
1. **Latch Phase**: All channel data is processed and stored in internal buffers
2. **Send Phase**: All channels send their data to hardware simultaneously
3. This ensures perfect synchronization across multiple LED strips

### Flexible Parameter System
- **Required**: Only `mode` field is mandatory
- **Optional**: All other parameters use configurable defaults when not specified or set to 0
- **Defaults**: Configurable via ROS parameters (frequency, speed, brightness, duty cycle)
- **Validation**: Lenient - accepts minimum required data, ignores extra entries

### Custom Duty Cycle for Flashing
The `on_time_duty` parameter allows precise control of flashing patterns:
- Range: 0.0 to 1.0 (0% to 100% on time)
- Default: 0.5 (50% on time)
- Examples: 0.25 = 25% on, 75% off; 0.8 = 80% on, 20% off

## Safety Considerations

- Always turn off LEDs when shutting down
- Monitor power consumption
- Use appropriate current limiting
- Ensure proper heat dissipation for high-power applications
