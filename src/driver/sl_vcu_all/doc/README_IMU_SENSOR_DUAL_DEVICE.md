# IMU Sensor Dual Device Implementation

## Overview

The IMU sensor node has been updated to support dual device configuration with separate gyroscope and accelerometer devices, timestamp-based synchronization, and configurable parameters through YAML files.

## Key Features

### 1. Dual Device Support
- **Device1 (/dev/iio:device1)**: Gyroscope data at `/sys/bus/iio/devices/iio:device1`
- **Device2 (/dev/iio:device2)**: Accelerometer data at `/sys/bus/iio/devices/iio:device2`

### 2. Separate Buffer Frames
- **Gyroscope Frame**: 16 bytes total (6 bytes data + 2 bytes padding + 8 bytes timestamp)
- **Accelerometer Frame**: 16 bytes total (6 bytes data + 2 bytes padding + 8 bytes timestamp)

### 3. Timestamp Synchronization
- Accelerometer timestamp is used as the primary timestamp
- Gyroscope data is matched to the closest accelerometer timestamp
- Configurable tolerance for timestamp matching (default: 10ms)
- Data queues maintain recent samples for synchronization

### 4. Single Thread Reading
- One monitoring thread reads from both devices using poll()
- Efficient polling of both file descriptors simultaneously
- Thread-safe data queues for each device

## Configuration

### YAML Configuration File
Location: `src/sl_vcu_all/config/imu_sensor.yaml`

Key parameters:
- `gyro_device_path`: "/dev/iio:device1"
- `accel_device_path`: "/dev/iio:device2"
- `timestamp_sync_tolerance_ns`: 10000000 (10ms)
- `imu_sensor_acc_sensitivity`: 4 (g)
- `imu_sensor_gyro_sensitivity`: 2000 (dps)
- `poll_timeout`: 1000 (ms)

Note: Device configuration (sampling rates, scales, buffer settings) is handled by the setup script.

## File Structure

### Modified Files
1. **Header File**: `src/sl_vcu_all/include/sl_vcu_all/imu_sensor.hpp`
   - Added dual device support
   - Separate buffer frame structures
   - Data queues for timestamp synchronization
   - Removed unused configuration parameters

2. **Implementation**: `src/sl_vcu_all/src/imu_sensor_node.cpp`
   - Simplified device opening (no configuration)
   - Timestamp-based data synchronization
   - Single thread for both devices
   - Cleaned up parameter handling

3. **CMakeLists.txt**: Added geometry_msgs dependency and config install

### New Files
1. **Configuration**: `src/sl_vcu_all/config/imu_sensor.yaml` (simplified)
2. **Launch File**: `src/sl_vcu_all/launch/imu_sensor.launch.py`
3. **Setup Script**: `src/sl_vcu_all/scripts/setup_imu_devices.sh` (device configuration)
4. **Documentation**: This file and setup script README

### Updated Files
1. **Main Launch**: `src/sl_vcu_all/launch/vcu_nodes.launch.py` - Added IMU sensor node

## Buffer Frame Structures

### Gyroscope Frame (16 bytes)
```cpp
struct gyro_device_buffer_frame {
    uint8_t  gyro_x_h;      // Gyro X high byte
    uint8_t  gyro_x_l;      // Gyro X low byte
    uint8_t  gyro_y_h;      // Gyro Y high byte
    uint8_t  gyro_y_l;      // Gyro Y low byte
    uint8_t  gyro_z_h;      // Gyro Z high byte
    uint8_t  gyro_z_l;      // Gyro Z low byte
    uint8_t  padding[2];    // 2 bytes padding
    uint8_t  timestamp[8];  // 8 bytes timestamp
};
```

### Accelerometer Frame (16 bytes)
```cpp
struct accel_device_buffer_frame {
    uint8_t  acc_x_h;       // Accel X high byte
    uint8_t  acc_x_l;       // Accel X low byte
    uint8_t  acc_y_h;       // Accel Y high byte
    uint8_t  acc_y_l;       // Accel Y low byte
    uint8_t  acc_z_h;       // Accel Z high byte
    uint8_t  acc_z_l;       // Accel Z low byte
    uint8_t  padding[2];    // 2 bytes padding
    uint8_t  timestamp[8];  // 8 bytes timestamp
};
```

## Usage

### Setup IMU Devices (Run as Root First)
```bash
# Setup both devices with default configuration
sudo ./scripts/setup_imu_devices.sh

# Setup only gyroscope
sudo ./scripts/setup_imu_devices.sh --gyro-only

# Setup only accelerometer
sudo ./scripts/setup_imu_devices.sh --accel-only

# Disable device buffers
sudo ./scripts/setup_imu_devices.sh --disable

# Dry run to see what would be done
sudo ./scripts/setup_imu_devices.sh --dry-run

# Custom configuration
sudo ./scripts/setup_imu_devices.sh --sampling-freq 100 --buffer-length 960

# Get help
./scripts/setup_imu_devices.sh --help
```

### Launch IMU Sensor Node (Run as Regular User)
```bash
# Launch with default configuration
ros2 launch sl_vcu_all imu_sensor.launch.py

# Launch with custom config
ros2 launch sl_vcu_all imu_sensor.launch.py config_file:=/path/to/custom/imu_sensor.yaml

# Launch all VCU nodes (includes IMU sensor)
ros2 launch sl_vcu_all vcu_nodes.launch.py
```

### Testing
```bash
# Build and run the test
cd src/sl_vcu_all/test
make
./imu_test

# Test options
./imu_test --accel-only    # Test only accelerometer
./imu_test --gyro-only     # Test only gyroscope
./imu_test --both          # Test both devices (default)
./imu_test --quiet         # Reduce output
```

## Data Flow

1. **Device Setup**: Both devices are configured with separate file paths
2. **Monitoring**: Single thread polls both devices simultaneously
3. **Data Processing**:
   - Gyroscope data is stored in a queue with timestamps
   - Accelerometer data triggers synchronization lookup
   - Closest gyroscope timestamp within tolerance is matched
4. **Publishing**: Synchronized IMU message is published with accelerometer timestamp

## Performance Considerations

- Data queues are limited to 100 samples each to manage memory
- Configurable timestamp tolerance for synchronization accuracy
- Non-blocking reads with efficient polling
- Thread-safe operations with minimal locking

## Compatibility

This implementation maintains compatibility with the existing ROS2 sensor_msgs/Imu message format while providing improved accuracy through dual-device timestamp synchronization.
