# Jack Control Node

## Overview

The Jack Control Node provides ROS2 action-based control for the lifting jack system. It uses SocketCAN for direct communication with the jack motor controller and implements a state machine for safe operation.

## Features

- **ROS2 Action Interface**: Provides asynchronous control with feedback and cancellation support
- **SocketCAN Communication**: Direct CAN bus communication without dispatcher dependency
- **APS Motor Only**: Designed for APS motor control (no BK motor support)
- **State Machine**: Implements safe operation states and transitions
- **Real-time Feedback**: Provides position, status, and progress updates during operations

## Supported Commands

1. **detect_base**: Detect the base position of the jack
2. **lift_up**: Lift the jack to a specified position
3. **lift_down**: Lower the jack to the base position
4. **stop**: Emergency stop the jack movement
5. **clear_alarm**: Clear any alarm conditions

## Action Interface

### Goal
```
string command          # Command to execute
uint32 target_position  # Target position (for lift operations)
uint32 speed           # Speed in RPM (optional)
```

### Result
```
bool success           # Operation success status
string message         # Result message
uint32 final_position  # Final position after operation
uint16 final_status    # Final status word
uint16 alarm_code      # Any alarm codes
```

### Feedback
```
string current_stage   # Current operation stage
uint32 current_position # Current position
uint16 current_status  # Current status word
uint16 current_alarm   # Current alarm code
float32 progress       # Progress percentage (0-100)
```

## Configuration

The node is configured via `config/jack_control.yaml`:

```yaml
jack_control:
  ros__parameters:
    can_interface: "can0"        # CAN interface name
    control_cycle_ms: 20         # Control loop period
    response_timeout_ms: 1000    # CAN response timeout
    heartbeat_period_ms: 20      # Status update period
    default_speed: 1000          # Default speed (RPM)
    max_position: 2240000        # Maximum position
    min_position: 0              # Minimum position
```

## Usage

### Launch the Node

```bash
# Launch jack control node only
ros2 launch sl_vcu_all jack_control.launch.py

# Launch with custom config
ros2 launch sl_vcu_all jack_control.launch.py config_file:=/path/to/config.yaml

# Launch as part of full system
ros2 launch sl_vcu_all sl_vcu_all.launch.py
```

### Send Action Goals

#### Using Command Line
```bash
# Detect base position
ros2 action send_goal /jack_control sl_vcu_all/action/JackControl "{command: 'detect_base'}"

# Lift up to position 1000000 at 1500 RPM
ros2 action send_goal /jack_control sl_vcu_all/action/JackControl "{command: 'lift_up', target_position: 1000000, speed: 1500}"

# Lift down to base
ros2 action send_goal /jack_control sl_vcu_all/action/JackControl "{command: 'lift_down'}"

# Emergency stop
ros2 action send_goal /jack_control sl_vcu_all/action/JackControl "{command: 'stop'}"

# Clear alarms
ros2 action send_goal /jack_control sl_vcu_all/action/JackControl "{command: 'clear_alarm'}"
```

#### Using Test Script
```bash
# Navigate to workspace and source
cd /path/to/workspace
source install/setup.bash

# Run test script
python3 src/sl_vcu_all/scripts/test_jack_control.py detect_base
python3 src/sl_vcu_all/scripts/test_jack_control.py lift_up 1000000 1500
python3 src/sl_vcu_all/scripts/test_jack_control.py lift_down
python3 src/sl_vcu_all/scripts/test_jack_control.py stop
python3 src/sl_vcu_all/scripts/test_jack_control.py clear_alarm
```

### Monitor Status
```bash
# Monitor action feedback
ros2 topic echo /jack_control/_action/feedback

# Check action status
ros2 action list
ros2 action info /jack_control
```

## Safety Features

- **Timeout Protection**: Operations timeout if they take too long
- **Position Limits**: Enforces minimum and maximum position limits
- **Emergency Stop**: Immediate stop capability via action cancellation
- **Alarm Monitoring**: Continuous monitoring of alarm conditions
- **State Machine**: Prevents invalid state transitions

## CAN Communication

The node communicates directly with the jack controller using:
- **Request ID**: 0x607
- **Response ID**: 0x587
- **Protocol**: CANopen-like SDO (Service Data Object)

## Troubleshooting

### Common Issues

1. **CAN Interface Not Found**
   - Ensure CAN interface is up: `sudo ip link set can0 up type can bitrate 500000`
   - Check interface exists: `ip link show can0`

2. **No Response from Jack Controller**
   - Verify CAN wiring and termination
   - Check controller power and configuration
   - Monitor CAN traffic: `candump can0`

3. **Action Goals Rejected**
   - Check if another goal is already active
   - Verify command syntax and parameters
   - Check node logs for error messages

4. **Position Not Reached**
   - Check for mechanical obstructions
   - Verify target position is within limits
   - Check for alarm conditions

### Debug Commands
```bash
# Check node status
ros2 node info /jack_control

# Monitor CAN traffic
candump can0

# Check action server status
ros2 action list -t
ros2 service list | grep jack_control
```

## Integration

The jack control node can be integrated with higher-level systems:

- **Navigation Stack**: For automatic lifting during docking
- **Mission Planning**: For coordinated lifting operations
- **Safety Systems**: For emergency stop integration
- **Monitoring**: For status reporting and diagnostics

## Dependencies

- ROS2 (Jazzy or later)
- rclcpp_action
- SocketCAN (Linux kernel module)
- CAN utilities (can-utils package)
