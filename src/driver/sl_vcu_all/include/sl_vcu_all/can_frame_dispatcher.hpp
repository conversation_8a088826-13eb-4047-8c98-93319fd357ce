#ifndef CAN_FRAME_DISPATCHER_HPP_
#define CAN_FRAME_DISPATCHER_HPP_

#include <rclcpp/rclcpp.hpp>
#include <sl_vcu_all/msg/can_frame.hpp>
#include <sl_vcu_all/srv/add_can_filter.hpp>
#include <sl_vcu_all/srv/check_node_status.hpp>
#include <string>
#include <unordered_map>
#include <vector>
#include <thread>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <unistd.h>

namespace sl_vcu_all
{

class CanFrameDispatcher : public rclcpp::Node
{
public:
  explicit CanFrameDispatcher(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
  virtual ~CanFrameDispatcher();

private:
  // Socket CAN related
  int socket_fd_;
  std::string can_interface_;
  std::vector<uint32_t> can_filter_ids_;
  bool is_socket_initialized_;

  // ROS subscribers and publishers
  rclcpp::Subscription<sl_vcu_all::msg::CanFrame>::SharedPtr can_tx_sub_;
  std::unordered_map<uint32_t, rclcpp::Publisher<sl_vcu_all::msg::CanFrame>::SharedPtr> can_id_publishers_;

  // ROS services
  rclcpp::Service<sl_vcu_all::srv::AddCanFilter>::SharedPtr add_filter_srv_;
  rclcpp::Service<sl_vcu_all::srv::CheckNodeStatus>::SharedPtr check_status_srv_;

  // Thread for receiving CAN frames
  std::thread receive_thread_;
  bool running_;

  // Initialize socket CAN
  bool initSocketCan();

  // Add filter for a specific CAN ID
  bool addCanFilter(uint32_t can_id, const std::string & topic_name);

  // Apply CAN filters to socket
  bool applyCanFilters();

  // Callback for receiving CAN frames to transmit
  void canTxCallback(const sl_vcu_all::msg::CanFrame::SharedPtr msg);

  // Service callback for adding a CAN filter
  void addCanFilterCallback(
    const std::shared_ptr<sl_vcu_all::srv::AddCanFilter::Request> request,
    std::shared_ptr<sl_vcu_all::srv::AddCanFilter::Response> response);

  // Service callback for checking node status
  void checkNodeStatusCallback(
    const std::shared_ptr<sl_vcu_all::srv::CheckNodeStatus::Request> request,
    std::shared_ptr<sl_vcu_all::srv::CheckNodeStatus::Response> response);

  // Thread function for receiving CAN frames
  void receiveCanFrames();
};

}  // namespace sl_vcu_all

#endif  // CAN_FRAME_DISPATCHER_HPP_