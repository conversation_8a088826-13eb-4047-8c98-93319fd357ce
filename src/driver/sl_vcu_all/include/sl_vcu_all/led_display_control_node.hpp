#ifndef SL_VCU_ALL__LED_DISPLAY_CONTROL_NODE_HPP_
#define SL_VCU_ALL__LED_DISPLAY_CONTROL_NODE_HPP_

#include <rclcpp/rclcpp.hpp>
#include "sl_vcu_all/srv/led_control.hpp"
#include <memory>
#include <vector>
#include <map>
#include <mutex>
#include <string>
#include <chrono>
#include <atomic>
#include <thread>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <cstring>

namespace sl_vcu_all
{

/**
 * @brief Color structure for SK6812 LEDs (GRB format) with individual brightness
 */
struct SK6812Color
{
    uint8_t green;
    uint8_t red;
    uint8_t blue;
    uint8_t green_brightness;
    uint8_t red_brightness;
    uint8_t blue_brightness;

    SK6812Color() : green(255), red(255), blue(255), green_brightness(0), red_brightness(0), blue_brightness(0) {}
    SK6812Color(uint8_t g, uint8_t r, uint8_t b, uint8_t gb = 0, uint8_t rb = 0, uint8_t bb = 0)
        : green(g), red(r), blue(b), green_brightness(gb), red_brightness(rb), blue_brightness(bb) {}

    // Apply brightness to get final GRB values
    void getScaledGRB(uint8_t& scaled_g, uint8_t& scaled_r, uint8_t& scaled_b) const;
};

/**
 * @brief Lighting modes for LED strips
 */
enum class LightingMode
{
    OFF,
    ON,
    BREATHING,
    FLASHING,
    MARQUEE
};

/**
 * @brief LED strip part selection
 */
enum class StripPart
{
    WHOLE = 0,
    FIRST_HALF = 1,
    SECOND_HALF = 2
};

/**
 * @brief Effect parameters for different lighting modes
 */
struct EffectParams
{
    SK6812Color color;
    float frequency;           // Hz for breathing/flashing
    float speed;              // pixels/second for marquee
    bool marquee_direction;   // true=forward, false=reverse
    float on_time_duty;       // Duty cycle for flashing (0.0-1.0)

    EffectParams()
        : color(), frequency(1.0f), speed(10.0f), marquee_direction(true), on_time_duty(0.5f) {}
};

/**
 * @brief Low-level SPI driver for SK6812 LED strips
 */
class SK6812SpiDriver
{
public:
    explicit SK6812SpiDriver(const std::string& spi_device, uint32_t spi_speed = 6400000);
    ~SK6812SpiDriver();

    bool initialize();
    void close();
    bool isInitialized() const { return spi_fd_ >= 0; }
    bool sendColors(const std::vector<SK6812Color>& colors);
    bool sendRawGRB(const uint8_t* grb_data, size_t num_leds);
    bool turnOffAll(size_t num_leds);

private:
    std::string spi_device_;
    uint32_t spi_speed_;
    int spi_fd_;
    std::mutex spi_mutex_;

    static constexpr uint8_t BIT_0_PATTERN = 0xC0;
    static constexpr uint8_t BIT_1_PATTERN = 0xF0;
    static constexpr size_t RESET_BYTES = 80;

    void byteToSpiPattern(uint8_t byte, uint8_t* output);
    size_t convertToSpiData(const uint8_t* grb_data, size_t num_leds, std::vector<uint8_t>& spi_data);
};

/**
 * @brief Controller for a single LED strip with effect management
 */
class LedStripController
{
public:
    LedStripController(uint8_t channel_id, size_t num_leds, size_t first_part_leds, size_t second_part_leds,
                      std::shared_ptr<SK6812SpiDriver> spi_driver);
    ~LedStripController();

    bool setLightingMode(StripPart part, LightingMode mode, const EffectParams& params, bool send_to_hardware = true);
    bool turnOff(StripPart part, bool send_to_hardware = true);
    bool turnOffAll();
    void update();
    bool isActive() const;
    void startEffectThread();
    void stopEffectThread();
    bool sendChannelToHardware();  // Send entire channel data to hardware

    uint8_t getChannelId() const { return channel_id_; }
    size_t getNumLeds() const { return num_leds_; }
    size_t getFirstPartLeds() const { return first_part_leds_; }
    size_t getSecondPartLeds() const { return second_part_leds_; }

private:
    uint8_t channel_id_;
    size_t num_leds_;
    size_t first_part_leds_;
    size_t second_part_leds_;
    std::shared_ptr<SK6812SpiDriver> spi_driver_;

    std::vector<SK6812Color> led_buffer_;
    std::mutex buffer_mutex_;

    struct PartState
    {
        LightingMode mode;
        EffectParams params;
        std::chrono::steady_clock::time_point start_time;
        std::chrono::steady_clock::time_point last_update;
        float phase;
        size_t marquee_position;
        bool active;

        PartState() : mode(LightingMode::OFF), phase(0.0f), marquee_position(0), active(false) {}
    };

    PartState part_states_[3];
    mutable std::mutex state_mutex_;

    std::atomic<bool> effect_thread_running_;
    std::thread effect_thread_;
    static constexpr int UPDATE_RATE_HZ = 50;

    void getPartRange(StripPart part, size_t& start_led, size_t& end_led) const;
    void updateBreathingEffect(PartState& state, size_t start_led, size_t end_led);
    void updateFlashingEffect(PartState& state, size_t start_led, size_t end_led);
    void updateMarqueeEffect(PartState& state, size_t start_led, size_t end_led);
    void setSolidColor(size_t start_led, size_t end_led, const SK6812Color& color);
    void clearRange(size_t start_led, size_t end_led);
    void effectThreadFunction();
    bool sendToHardware();
};

/**
 * @brief ROS2 node for controlling SK6812 LED strips
 */
class LedDisplayControlNode : public rclcpp::Node
{
public:
    explicit LedDisplayControlNode(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~LedDisplayControlNode();

    void shutdownLedStrips();

private:
    void ledControlServiceCallback(
        const std::shared_ptr<srv::LedControl::Request> request,
        std::shared_ptr<srv::LedControl::Response> response);

    void initializeParameters();
    void declareParameters();
    rcl_interfaces::msg::SetParametersResult parametersCallback(
        const std::vector<rclcpp::Parameter> & parameters);

    bool initializeLedStrips();
    void setStartupLighting();

    LightingMode stringToLightingMode(const std::string& mode_str);
    std::string lightingModeToString(LightingMode mode);
    bool validateChannels(const std::vector<uint8_t>& channels, std::string& error_msg);

    rclcpp::Service<srv::LedControl>::SharedPtr led_control_service_;
    rclcpp::node_interfaces::OnSetParametersCallbackHandle::SharedPtr param_callback_handle_;

    std::map<uint8_t, std::shared_ptr<SK6812SpiDriver>> spi_drivers_;
    std::map<uint8_t, std::shared_ptr<LedStripController>> led_controllers_;
    std::mutex controllers_mutex_;

    struct ChannelConfig
    {
        std::string spi_device;
        uint32_t spi_speed;
        size_t num_leds;
        size_t first_part_leds;
        size_t second_part_leds;
        bool enabled;

        ChannelConfig() : spi_device("/dev/spidev0.0"), spi_speed(6400000), 
                         num_leds(50), first_part_leds(25), second_part_leds(25), enabled(true) {}
    };

    std::map<uint8_t, ChannelConfig> channel_configs_;
    uint8_t num_channels_;
    bool auto_start_effects_;
    
    struct DefaultEffectParams
    {
        float default_frequency;
        float default_speed;
        bool default_marquee_direction;
        uint8_t default_green_brightness;
        uint8_t default_red_brightness;
        uint8_t default_blue_brightness;
        float default_on_time_duty;

        DefaultEffectParams()
            : default_frequency(1.0f), default_speed(10.0f), default_marquee_direction(true),
              default_green_brightness(255), default_red_brightness(255), default_blue_brightness(255),
              default_on_time_duty(0.5f) {}
    };

    DefaultEffectParams default_params_;
    bool initialized_;
    bool shutdown_called_;
    std::chrono::steady_clock::time_point last_command_time_;
};

} // namespace sl_vcu_all

#endif // SL_VCU_ALL__LED_DISPLAY_CONTROL_NODE_HPP_
