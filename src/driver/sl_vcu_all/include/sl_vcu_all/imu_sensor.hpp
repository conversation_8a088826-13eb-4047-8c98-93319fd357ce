#ifndef SL_VCU_ALL_IMU_SENSOR_HPP
#define SL_VCU_ALL_IMU_SENSOR_HPP

#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/imu.hpp"
#include "geometry_msgs/msg/vector3.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "geometry_msgs/msg/transform_stamped.hpp"
#include "tf2_ros/transform_broadcaster.h"
#include "tf2/LinearMath/Quaternion.h"
#include <chrono>
#include <functional>
#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <fcntl.h>
#include <poll.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/time.h>
#include <deque>
#include <algorithm>
#include <numeric>
#include <cmath>

namespace sl_vcu_all
{

class ImuSensor : public rclcpp::Node
{
public:
    explicit ImuSensor(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~ImuSensor();

private:
    // Methods
    void initParameters();
    bool openImuDevices();
    void monitorImuDevices();
    void publishImuData();
    void cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg);
    void updateBiasEstimation(double gyro_z, uint64_t timestamp);
    void calculateInitialBias();
    bool shouldUpdateBias();
    geometry_msgs::msg::Vector3 applyBiasCorrection(const geometry_msgs::msg::Vector3& gyro_data);
    void integrateYaw(const geometry_msgs::msg::Vector3& filtered_gyro, uint64_t timestamp);
    void publishTransform(const builtin_interfaces::msg::Time& timestamp);

    uint64_t findClosestTimestamp(uint64_t target_timestamp, const std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>>& data_queue);

    // Gyroscope device buffer frame structure (16 bytes total)
    struct gyro_device_buffer_frame
    {
        uint8_t  gyro_x_h;
        uint8_t  gyro_x_l;
        uint8_t  gyro_y_h;
        uint8_t  gyro_y_l;
        uint8_t  gyro_z_h;
        uint8_t  gyro_z_l;
        uint8_t  padding[2];  // 2 bytes padding
        uint8_t  timestamp[8];
    };

    // Accelerometer device buffer frame structure (16 bytes total)
    struct accel_device_buffer_frame
    {
        uint8_t  acc_x_h;
        uint8_t  acc_x_l;
        uint8_t  acc_y_h;
        uint8_t  acc_y_l;
        uint8_t  acc_z_h;
        uint8_t  acc_z_l;
        uint8_t  padding[2];  // 2 bytes padding
        uint8_t  timestamp[8];
    };

    // Parameters with default values
    int poll_timeout_;
    int imu_sensor_acc_sensitivity_;  // g
    int imu_sensor_gyro_sensitivity_;  // dps
    int timestamp_sync_tolerance_ns_;  // nanoseconds tolerance for timestamp matching

    // Bias estimation parameters
    double initial_bias_offset_;      // Initial large offset for filtering
    double bias_calculation_time_;    // Time to calculate initial bias (seconds)
    double bias_update_time_;         // Time window for bias update analysis (seconds)
    double bias_update_threshold_;    // Standard deviation threshold for bias update
    double cmd_vel_timeout_;          // Timeout for cmd_vel messages (seconds)

    // TF publishing parameters
    bool publish_tf_;                 // Whether to publish TF transforms
    std::string parent_frame_id_;     // Parent frame for TF (e.g., "base_link")
    std::string child_frame_id_;      // Child frame for TF (same as imu_frame_id_)

    // Device paths (only paths needed for opening devices)
    std::string gyro_device_path_;
    std::string accel_device_path_;

    // Publisher and Subscriber
    rclcpp::Publisher<sensor_msgs::msg::Imu>::SharedPtr imu_pub_;
    rclcpp::Publisher<sensor_msgs::msg::Imu>::SharedPtr imu_filtered_pub_;
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_sub_;

    // TF broadcaster
    std::unique_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;

    // IMU device parameters
    std::string imu_frame_id_;
    int gyro_device_fd_;
    int accel_device_fd_;
    uint64_t last_gyro_timestamp_;
    uint64_t last_accel_timestamp_;

    // IMU monitoring thread
    std::thread imu_thread_;
    std::atomic<bool> keep_monitoring_;

    // Timer for publishing
    rclcpp::TimerBase::SharedPtr publish_timer_;
    int publish_period_ms_; // Publishing period in milliseconds
    std::string imu_topic_;

    // Data buffers for each device
    std::vector<uint8_t> gyro_data_buf_;
    std::vector<uint8_t> accel_data_buf_;

    // Data queues for timestamp synchronization
    std::mutex gyro_data_mutex_;
    std::mutex accel_data_mutex_;
    std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>> gyro_data_queue_;
    std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>> accel_data_queue_;

    // Latest synchronized IMU data
    std::mutex imu_data_mutex_;
    sensor_msgs::msg::Imu latest_imu_data_;
    sensor_msgs::msg::Imu latest_filtered_imu_data_;
    bool new_data_available_;

    // Bias estimation and filtering
    std::mutex bias_mutex_;
    geometry_msgs::msg::Vector3 current_bias_;
    geometry_msgs::msg::Vector3 initial_bias_;
    bool bias_initialized_;
    std::chrono::steady_clock::time_point node_start_time_;
    std::chrono::steady_clock::time_point last_cmd_vel_time_;

    // Yaw integration
    double current_yaw_;
    uint64_t last_yaw_timestamp_;

    // Bias calculation data
    std::deque<std::pair<uint64_t, double>> gyro_z_history_;  // For bias calculation
    std::deque<double> gyro_z_samples_;  // For standard deviation calculation
    std::chrono::steady_clock::time_point last_bias_update_time_;

    // Motion detection
    bool robot_moving_;
    std::string cmd_vel_topic_;
};

} // namespace sl_vcu_all

#endif // SL_VCU_ALL_IMU_SENSOR_HPP
