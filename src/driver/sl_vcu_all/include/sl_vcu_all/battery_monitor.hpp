#ifndef SL_VCU_ALL_BATTERY_MONITOR_HPP_
#define SL_VCU_ALL_BATTERY_MONITOR_HPP_

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/battery_state.hpp>
#include <sl_vcu_all/msg/battery_status.hpp>
#include <string>
#include <thread>
#include <atomic>
#include <memory>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <linux/input.h>
#include <chrono>

namespace sl_vcu_all
{

class BatteryMonitor : public rclcpp::Node
{
public:
    explicit BatteryMonitor(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~BatteryMonitor();

private:
    // Socket CAN related
    int socket_fd_;
    std::string can_interface_;
    bool is_socket_initialized_;
    bool running_;
    std::thread can_thread_;
    std::chrono::steady_clock::time_point last_send_time_;
    int min_send_interval_ms_;
    int request_interval_ms_;

    // GPIO input device related
    int input_device_fd_;
    std::string input_device_path_;
    int charge_event_code_;
    int manual_charge_event_code_;  // New event code for manual charging
    int poll_timeout_ms_;
    bool is_charging_;
    std::thread input_thread_;
    std::atomic<bool> keep_monitoring_;
    std::atomic<bool> is_manual_charging_;  // New state for manual charging

    // Battery state
    std::mutex battery_mutex_;
    double voltage_;           // Total voltage in Volts
    double current_;           // Current in Amperes (positive = charging)
    //double charge_;           // Remaining charge in Ah
    double capacity_;         // Full capacity in Ah
    int32_t cycle_count_;     // Charge cycle count
    double percentage_;       // Remaining capacity percentage
    
    // Parameters
    int publish_rate_ms_;
    int battery_status_publish_rate_ms_;  // New parameter for battery status publish rate
    std::string battery_topic_;
    std::string battery_status_topic_;  // New topic for battery status
    uint32_t can_id_battery_info_;    // 0x100
    uint32_t can_id_battery_status_;  // 0x101

    // Publishers
    rclcpp::Publisher<sensor_msgs::msg::BatteryState>::SharedPtr battery_state_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::BatteryStatus>::SharedPtr battery_status_pub_;  // New publisher
    rclcpp::TimerBase::SharedPtr publish_timer_;
    rclcpp::TimerBase::SharedPtr battery_status_timer_;  // New timer for battery status
    rclcpp::TimerBase::SharedPtr request_timer_;

    // Methods
    void initParameters();
    bool setupInputDevice();
    bool initSocketCan();
    void monitorInputEvents();
    void monitorCanFrames();
    void publishBatteryState();
    void publishBatteryStatus();  // New function to publish battery status
    void requestBatteryStatus();
    void processCanFrame(const struct can_frame& frame);
    double convertToDouble(uint8_t high_byte, uint8_t low_byte, double scale, bool is_signed = false);
};

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL_BATTERY_MONITOR_HPP_ 