#ifndef JACK_CONTROL_NODE_HPP_
#define JACK_CONTROL_NODE_HPP_

#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <memory>
#include <string>
#include <thread>
#include <mutex>
#include <queue>
#include <chrono>
#include <atomic>
#include <condition_variable>

// Linux SocketCAN includes
#include <linux/can.h>
#include <linux/can/raw.h>
#include <sys/socket.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <signal.h>

// ROS2 Action
#include "sl_vcu_all/action/jack_control.hpp"

namespace sl_vcu_all
{

// Jack BK driver constants (from jack_bk_driver.h)
#define JACK_DATA_REQUEST_ID         (0x607)
#define JACK_DATA_RESPONSE_ID        (0x587)

#define JACK_CMD_READ_REQUEST                   (0x40)
#define JACK_CMD_READ_RESPONSE_1BYTE            (0x4F)
#define JACK_CMD_READ_RESPONSE_2BYTE            (0x4B)
#define JACK_CMD_READ_RESPONSE_4BYTE            (0x43)
#define JACK_CMD_READ_RESPONSE_ERROR            (0x80)

#define JACK_CMD_WRITE_REQUEST_1BYTE            (0x2F)
#define JACK_CMD_WRITE_REQUEST_2BYTE            (0x2B)
#define JACK_CMD_WRITE_REQUEST_4BYTE            (0x23)
#define JACK_CMD_WRITE_RESPONSE_OK              (0x60)
#define JACK_CMD_WRITE_RESPONSE_ERROR           (0x80)

// Control registers
#define JACK_INDEX_CTRL                         (0x6040)
#define JACK_SUB_INDEX_CTRL                     (0x00)
#define JACK_CTRL_RELEASE                       (0x06)
#define JACK_CTRL_BRAKE                         (0x0F)
#define JACK_CTRL_DETECT_BASE                   (0x1F)
#define JACK_CTRL_AUTO_POS_CHG                  (0x103F)
#define JACK_CTRL_ALARM_CLS                     (0x86)

// Mode registers
#define JACK_INDEX_MODE                         (0x6060)
#define JACK_SUB_INDEX_MODE                     (0x00)
#define JACK_MODE_POS                           (0x01)
#define JACK_MODE_DETECT_BASE                   (0x06)

// Position and speed registers
#define JACK_INDEX_TARGET_POS                   (0x607A)
#define JACK_SUB_INDEX_TARGET_POS               (0x00)
#define JACK_INDEX_TARGET_SPEED                 (0x6081)
#define JACK_SUB_INDEX_TARGET_SPEED             (0x00)
#define JACK_INDEX_ACTUAL_POS                   (0x6063)
#define JACK_SUB_INDEX_ACTUAL_POS               (0x00)

// Status registers
#define JACK_INDEX_STATUS                       (0x6041)
#define JACK_SUB_INDEX_STATUS                   (0x00)
#define JACK_STATUS_GET_BASE                    (1<<15)
#define JACK_STATUS_REACH_TRAGET                (1<<10)

#define JACK_INDEX_ALARM                        (0x2601)
#define JACK_SUB_INDEX_ALARM                    (0x00)

// Jack stages
typedef enum
{
    JACK_STAGE_INIT = 0,
    JACK_STAGE_DETECTING_BASE,
    JACK_STAGE_BASE_STOP,
    JACK_STAGE_LIFTING_UP,
    JACK_STAGE_LIFTING_DOWN,
    JACK_STAGE_TOP_STOP,
    JACK_STAGE_MIDDLE_STOP,
} jack_stage_t;

// Request stages
typedef enum
{
    JACK_REQUEST_STAGE_WAIT_TO_SEND = 0,
    JACK_REQUEST_STAGE_WAIT_RESPONSE,
    JACK_REQUEST_STAGE_RESPONSE_OK,
    JACK_REQUEST_STAGE_RESPONSE_ERROR,
} jack_request_stage_t;

// Protocol structure
#pragma pack(1)
typedef struct
{
    uint8_t     cmd;
    uint16_t    index;
    uint8_t     sub_index;
    uint32_t    data;
} __attribute__((packed)) jack_bk_protocol_t;
#pragma pack()

class JackControlNode : public rclcpp::Node
{
public:
    using JackControlAction = sl_vcu_all::action::JackControl;
    using GoalHandleJackControl = rclcpp_action::ServerGoalHandle<JackControlAction>;

    explicit JackControlNode(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    virtual ~JackControlNode();

    // Shutdown handling
    void shutdown();

    // Check if node is enabled and properly initialized
    bool isEnabled() const;

private:
    // ROS2 Action Server
    rclcpp_action::Server<JackControlAction>::SharedPtr action_server_;
    
    // Action callbacks
    rclcpp_action::GoalResponse handle_goal(
        const rclcpp_action::GoalUUID & uuid,
        std::shared_ptr<const JackControlAction::Goal> goal);
    
    rclcpp_action::CancelResponse handle_cancel(
        const std::shared_ptr<GoalHandleJackControl> goal_handle);
    
    void handle_accepted(const std::shared_ptr<GoalHandleJackControl> goal_handle);
    
    // Action execution
    void execute_goal(const std::shared_ptr<GoalHandleJackControl> goal_handle);

    // Command execution methods
    bool executeDetectBase();
    bool executeLiftUp();
    bool executeLiftDown();
    bool executeStop();
    bool executeClearAlarm();
    bool waitForPositionReached();

    // Automatic initialization
    void startAutomaticBaseDetection();
    
    // Parameter initialization
    void initParameters();
    
    // SocketCAN methods
    bool initSocketCan();
    void closeSocketCan();
    bool sendCanFrame(const jack_bk_protocol_t& protocol);
    bool queueCanFrameForSend(const jack_bk_protocol_t& protocol);
    void receiveCanFramesThread();
    void sendCanFramesThread();
    void processCanFrame(const struct can_frame& frame);
    
    // Jack control methods
    bool sendJackCommand(uint16_t index, uint8_t sub_index, uint32_t data, uint8_t cmd);
    bool sendJackCommandUnlocked(uint16_t index, uint8_t sub_index, uint32_t data, uint8_t cmd);
    bool waitForResponse(uint32_t timeout_ms = 1000);
    void updateJackStatus();
    void controlTimerCallback();
    
    // State machine methods
    void processStageInit();
    void processStageDetectingBase();
    void processStageBaseStop();
    void processStageLiftingUp();
    void processStageLiftingDown();
    void processStageTopStop();
    void processStageMiddleStop();
    
    // Utility methods
    std::string stageToString(jack_stage_t stage);
    float calculateProgress();
    bool validateSpeed(uint32_t speed);
    bool checkPositionTolerance(int32_t target_pos, int32_t current_pos);
    bool checkStageTimeout();
    bool checkMovementStarted(int32_t initial_position);
    uint32_t convertRpmToCanValue(uint32_t rpm);
    
    // Parameters
    bool enable_;
    std::string can_interface_;
    uint32_t control_cycle_ms_;
    uint32_t response_timeout_ms_;
    uint32_t heartbeat_period_ms_;
    uint32_t min_send_interval_ms_;
    uint32_t default_speed_;
    uint32_t max_speed_;
    int32_t max_position_;
    int32_t min_position_;
    int32_t position_tolerance_;
    uint32_t up_stage_timeout_s_;
    uint32_t down_stage_timeout_s_;
    uint32_t base_stage_timeout_s_;
    uint32_t movement_start_delay_s_;
    int32_t movement_check_tolerance_;
    uint32_t status_update_delay_s_;
    bool auto_detect_base_on_start_;
    
    // SocketCAN
    int socket_fd_;
    std::atomic<bool> running_;
    std::thread receive_thread_;
    std::thread send_thread_;
    std::queue<jack_bk_protocol_t> send_queue_;
    std::mutex send_queue_mutex_;
    std::condition_variable send_queue_cv_;
    std::chrono::steady_clock::time_point last_send_time_;
    
    // Jack state
    jack_stage_t current_stage_;
    jack_request_stage_t request_stage_;
    int32_t current_position_;
    uint16_t current_status_;
    uint16_t current_alarm_;
    int32_t target_position_;
    uint32_t target_speed_;
    
    // Action state
    std::shared_ptr<GoalHandleJackControl> current_goal_handle_;
    std::string current_command_;
    
    // Timing
    rclcpp::Time last_heartbeat_time_;
    rclcpp::Time last_request_time_;
    rclcpp::Time action_start_time_;
    rclcpp::Time stage_start_time_;
    
    // Synchronization
    std::mutex jack_mutex_;
    std::mutex action_mutex_;
    
    // Timers
    rclcpp::TimerBase::SharedPtr control_timer_;
    rclcpp::TimerBase::SharedPtr auto_detect_timer_;
    
    // Response tracking
    bool awaiting_response_;
    uint16_t expected_response_index_;
    uint8_t expected_response_subindex_;
    
    // Last received response
    jack_bk_protocol_t last_response_;
    bool response_received_;
};

}  // namespace sl_vcu_all

#endif  // JACK_CONTROL_NODE_HPP_
