cmake_minimum_required(VERSION 3.8)
project(sl_vcu_all)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(rclcpp_action REQUIRED)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

# Generate service and action interfaces
rosidl_generate_interfaces(${PROJECT_NAME}
  "srv/AddCanFilter.srv"
  "srv/CheckNodeStatus.srv"
  "srv/LedControl.srv"
  "msg/MotorInfo.msg"
  "msg/MotorState.msg"
  "msg/BumperState.msg"
  "msg/CanFrame.msg"
  "msg/BatteryStatus.msg"
  "msg/PartData.msg"
  "msg/ChannelData.msg"
  "action/JackControl.action"
  DEPENDENCIES std_msgs
)

# Get the include directories for the generated interfaces
rosidl_get_typesupport_target(cpp_typesupport_target "${PROJECT_NAME}" "rosidl_typesupport_cpp")

# Declare a C++ executable for CAN frame dispatcher
add_executable(can_frame_dispatcher_node src/can_frame_dispatcher_node.cpp)
target_include_directories(can_frame_dispatcher_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(can_frame_dispatcher_node PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  can_frame_dispatcher_node
  "rclcpp"
  "std_msgs"
)

# Add dependency for service messages to can_frame_dispatcher_node
target_link_libraries(can_frame_dispatcher_node "${cpp_typesupport_target}")

# Declare a C++ executable for ZL motor controller
add_executable(zl_motor_controller_node src/zl_motor_controller_node.cpp)
target_include_directories(zl_motor_controller_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(zl_motor_controller_node PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  zl_motor_controller_node
  "rclcpp"
  "std_msgs"
  "geometry_msgs"
  "nav_msgs"
  "tf2"
  "tf2_ros"
  "tf2_geometry_msgs"
  "sensor_msgs"
)

# Add dependency for service messages to zl_motor_controller_node
target_link_libraries(zl_motor_controller_node "${cpp_typesupport_target}")

# Declare a C++ executable for Bumper sensor
add_executable(bumper_sensor_node src/bumper_sensor_node.cpp)
target_include_directories(bumper_sensor_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(bumper_sensor_node PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  bumper_sensor_node
  "rclcpp"
  "std_msgs"
)

# Add dependency for service messages to bumper_sensor_node
target_link_libraries(bumper_sensor_node "${cpp_typesupport_target}")

# Declare a C++ executable for IMU sensor
add_executable(imu_sensor_node src/imu_sensor_node.cpp)
target_include_directories(imu_sensor_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(imu_sensor_node PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  imu_sensor_node
  "rclcpp"
  "std_msgs"
  "sensor_msgs"
  "geometry_msgs"
  "tf2"
  "tf2_ros"
)

# Add dependency for service messages to imu_sensor_node
target_link_libraries(imu_sensor_node "${cpp_typesupport_target}")

# Declare a C++ executable for Bumper sensor
add_executable(teleop_key src/teleop_key.cpp)
target_include_directories(teleop_key PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(teleop_key PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  teleop_key
  "rclcpp"
  "std_msgs"
  "geometry_msgs"
)

# Add dependency for service messages to bumper_sensor_node
target_link_libraries(teleop_key "${cpp_typesupport_target}")

# Declare a C++ executable for Battery Monitor
add_executable(battery_monitor_node src/battery_monitor_node.cpp)
target_include_directories(battery_monitor_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(battery_monitor_node PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  battery_monitor_node
  "rclcpp"
  "std_msgs"
  "sensor_msgs"
)

# Add dependency for service messages to battery_monitor_node
target_link_libraries(battery_monitor_node "${cpp_typesupport_target}")

# Declare a C++ executable for Jack Control
add_executable(jack_control_node src/jack_control_node.cpp)
target_include_directories(jack_control_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(jack_control_node PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  jack_control_node
  "rclcpp"
  "rclcpp_action"
  "std_msgs"
)

# Add dependency for action messages to jack_control_node
target_link_libraries(jack_control_node "${cpp_typesupport_target}")

# Declare a C++ executable for LED Display Control
add_executable(led_display_control_node
    src/led_display_control_node.cpp
)
target_include_directories(led_display_control_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(led_display_control_node PUBLIC c_std_99 cxx_std_17)
ament_target_dependencies(
  led_display_control_node
  "rclcpp"
  "std_msgs"
)

# Add dependency for service messages to led_display_control_node
target_link_libraries(led_display_control_node "${cpp_typesupport_target}")

# Install executables
install(TARGETS
  can_frame_dispatcher_node
  zl_motor_controller_node
  bumper_sensor_node
  imu_sensor_node
  teleop_key
  battery_monitor_node
  jack_control_node
  led_display_control_node
  DESTINATION lib/${PROJECT_NAME})

# Add install for launch files and config files
install(DIRECTORY
  launch
  config
  DESTINATION share/${PROJECT_NAME}
)

ament_package()
