<!ELEMENT BENCH (AVAILSOLVER+,LINEARSYSTEM+)>
  <!ELEMENT AVAILSOLVER (SOLVER+)>
    <!ELEMENT SOLVER (TYPE,PACKAGE)>
      <!ELEMENT TYPE (#PCDATA)>  <!-- One of LU, LLT, LDLT, ITER -->
      <!ELEMENT PACKAGE (#PCDATA)>  <!-- Derived from a library -->
  <!ELEMENT LINEARSYSTEM (MATRIX,SOLVER_STAT+,BEST_SOLVER,GLOBAL_PARAMS*)>
    <!ELEMENT MATRIX (NAME,SIZE,ENTRIES,PATTERN?,SYMMETRY,POSDEF?,ARITHMETIC,RHS*)>
      <!ELEMENT NAME (#PCDATA)>
      <!ELEMENT SIZE (#PCDATA)>
      <!ELEMENT ENTRIES (#PCDATA)> <!-- The number of nonzeros elements -->
      <!ELEMENT PATTERN (#PCDATA)>  <!-- Is structural pattern symmetric or not -->
      <!ELEMENT SYMMETRY (#PCDATA)> <!-- symmmetry with numerical values -->
      <!ELEMENT POSDEF (#PCDATA)> <!-- Is the matrix positive definite or not -->
      <!ELEMENT ARITHMETIC (#PCDATA)> 
      <!ELEMENT RHS (SOURCE)>  <!-- A matrix can have one or more right hand side associated. -->
        <!ELEMENT SOURCE (#PCDATA)> <!-- Source of the right hand side, either generated or provided -->
    <!ELEMENT SOLVER_STAT (PARAMS*,TIME,ERROR,ITER?)>
      <!ELEMENT PARAMS (#PCDATA)>
      <!ELEMENT TIME (COMPUTE,SOLVE,TOTAL)>
        <!ELEMENT COMPUTE (#PCDATA)> <!-- Time to analyze,to factorize, or to setup the preconditioner-->
        <!ELEMENT SOLVE (#PCDATA)> <!-- Time to solve with all the available rhs -->
        <!ELEMENT TOTAL (#PCDATA)>
      <!ELEMENT ERROR (#PCDATA)> <!-- Either the relative error or the relative residual norm -->
      <!ELEMENT ITER (#PCDATA)> <!-- Number of iterations -->
    <!ELEMENT BEST_SOLVER CDATA> <!-- Id of the best solver -->
    <!ELEMENT GLOBAL_PARAMS (#PCDATA)> <!-- Parameters shared by all solvers -->

<!ATTLIST SOLVER ID CDATA #REQUIRED>
<!ATTLIST SOLVER_STAT ID CDATA #REQUIRED>
<!ATTLIST BEST_SOLVER ID CDATA #REQUIRED>
<!ATTLIST RHS ID CDATA #IMPLIED>