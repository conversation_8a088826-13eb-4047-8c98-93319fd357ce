
find_package(Blitz)

if (<PERSON><PERSON><PERSON>Z_FOUND)
  include_directories(${BL<PERSON>Z_INCLUDES})

  btl_add_bench(btl_blitz btl_blitz.cpp)
  if (BUILD_btl_blitz)
    target_link_libraries(btl_blitz ${BLITZ_LIBRARIES})
  endif ()

  btl_add_bench(btl_tiny_blitz btl_tiny_blitz.cpp OFF)
  if (BUILD_btl_tiny_blitz)
    target_link_libraries(btl_tiny_blitz ${BLITZ_LIBRARIES})
  endif ()

endif ()
