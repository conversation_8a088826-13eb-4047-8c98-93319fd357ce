# library eigen

EIGEN_FILES = [
	"Eigen/**",
	"unsupported/Eigen/CXX11/**",
	"unsupported/Eigen/FFT",
	"unsupported/Eigen/KroneckerProduct",
	"unsupported/Eigen/src/FFT/**",
	"unsupported/Eigen/src/KroneckerProduct/**",
	"unsupported/Eigen/MatrixFunctions",
	"unsupported/Eigen/SpecialFunctions",
	"unsupported/Eigen/src/MatrixFunctions/**",
	"unsupported/Eigen/src/SpecialFunctions/**",
]

# List of files picked up by glob but actually part of another target.
EIGEN_EXCLUDE_FILES = [
	"Eigen/src/Core/arch/AVX/PacketMathGoogleTest.cc",
]

EIGEN_MPL2_HEADER_FILES = glob(
	EIGEN_FILES,
	exclude = EIGEN_EXCLUDE_FILES,
)

cc_library(
	name = "eigen",
	hdrs = EIGEN_MPL2_HEADER_FILES,
	includes = ["."],
	visibility = ["//visibility:public"],
)
