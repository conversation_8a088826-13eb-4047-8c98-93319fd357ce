'dblat2.summ'     NAME OF <PERSON>UMMARY OUTPUT FILE
6                 UNIT NUMBER OF SUMMARY FILE
'dblat2.snap'     NAME OF SNAPSHOT OUTPUT FILE
-1                UNIT NUMBER OF SNAPSHOT FILE (NOT USED IF .LT. 0)
F        LOGICAL FLAG, T TO REWIND SNAPSHOT FILE AFTER EACH RECORD.
F        LOGICAL FLAG, T TO STOP ON FAILURES.
T        LOGICAL FLAG, T TO TEST ERROR EXITS.
16.0     THRESHOLD VALUE OF TEST RATIO
6                 NUMBER OF VALUES OF N
0 1 2 3 5 9       VALUES OF N
4                 NUMBER OF VALUES OF K
0 1 2 4           VALUES OF K
4                 NUMBER OF VALUES OF INCX AND INCY
1 2 -1 -2         VALUES OF INCX AND INCY
3                 NUMBER OF VALUES OF ALPHA
0.0 1.0 0.7       VALUES OF ALPHA
3                 NUMBER OF VALUES OF BETA
0.0 1.0 0.9       VALUES OF BETA
DGEMV  T PUT F FOR NO TEST. SAME COLUMNS.
DGBMV  T PUT F FOR NO TEST. SAME COLUMNS.
DSYMV  T PUT F FOR NO TEST. SAME COLUMNS.
DSBMV  T PUT F FOR NO TEST. SAME COLUMNS.
DSPMV  T PUT F FOR NO TEST. SAME COLUMNS.
DTRMV  T PUT F FOR NO TEST. SAME COLUMNS.
DTBMV  T PUT F FOR NO TEST. SAME COLUMNS.
DTPMV  T PUT F FOR NO TEST. SAME COLUMNS.
DTRSV  T PUT F FOR NO TEST. SAME COLUMNS.
DTBSV  T PUT F FOR NO TEST. SAME COLUMNS.
DTPSV  T PUT F FOR NO TEST. SAME COLUMNS.
DGER   T PUT F FOR NO TEST. SAME COLUMNS.
DSYR   T PUT F FOR NO TEST. SAME COLUMNS.
DSPR   T PUT F FOR NO TEST. SAME COLUMNS.
DSYR2  T PUT F FOR NO TEST. SAME COLUMNS.
DSPR2  T PUT F FOR NO TEST. SAME COLUMNS.
