'zblat2.summ'     NAME OF <PERSON>UMMARY OUTPUT FILE
6                 UNIT NUMBER OF SUMMARY FILE
'cbla2t.snap'     NAME OF SNAPSHOT OUTPUT FILE
-1                UNIT NUMBER OF SNAPSHOT FILE (NOT USED IF .LT. 0)
F        LOGICAL FLAG, T TO REWIND SNAPSHOT FILE AFTER EACH RECORD.
F        LOGICAL FLAG, T TO STOP ON FAILURES.
T        LOGICAL FLAG, T TO TEST ERROR EXITS.
16.0     THRESHOLD VALUE OF TEST RATIO
6                 NUMBER OF VALUES OF N
0 1 2 3 5 9       VALUES OF N
4                 NUMBER OF VALUES OF K
0 1 2 4           VALUES OF K
4                 NUMBER OF VALUES OF INCX AND INCY
1 2 -1 -2         VALUES OF INCX AND INCY
3                 NUMBER OF VALUES OF ALPHA
(0.0,0.0) (1.0,0.0) (0.7,-0.9)       VALUES OF ALPHA
3                 NUMBER OF VALUES OF BETA
(0.0,0.0) (1.0,0.0) (1.3,-1.1)       VALUES OF BETA
ZGEMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZGBMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZHEMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZHBMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZHPMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZTRMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZTBMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZTPMV  T PUT F FOR NO TEST. SAME COLUMNS.
ZTRSV  T PUT F FOR NO TEST. SAME COLUMNS.
ZTBSV  T PUT F FOR NO TEST. SAME COLUMNS.
ZTPSV  T PUT F FOR NO TEST. SAME COLUMNS.
ZGERC  T PUT F FOR NO TEST. SAME COLUMNS.
ZGERU  T PUT F FOR NO TEST. SAME COLUMNS.
ZHER   T PUT F FOR NO TEST. SAME COLUMNS.
ZHPR   T PUT F FOR NO TEST. SAME COLUMNS.
ZHER2  T PUT F FOR NO TEST. SAME COLUMNS.
ZHPR2  T PUT F FOR NO TEST. SAME COLUMNS.
