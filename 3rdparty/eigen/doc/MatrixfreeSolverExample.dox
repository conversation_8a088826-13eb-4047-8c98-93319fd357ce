
namespace Eigen {

/**

\eigenManualPage MatrixfreeSolverExample Matrix-free solvers

Iterative solvers such as ConjugateGradient and BiCGSTAB can be used in a matrix free context. To this end, user must provide a wrapper class inheriting EigenBase<> and implementing the following methods:
 - \c Index \c rows() and \c Index \c cols(): returns number of rows and columns respectively
 - \c operator* with your type and an %Eigen dense column vector (its actual implementation goes in a specialization of the internal::generic_product_impl class)

\c Eigen::internal::traits<> must also be specialized for the wrapper type.

Here is a complete example wrapping an Eigen::SparseMatrix:
\include matrixfree_cg.cpp
Output: \verbinclude matrixfree_cg.out

*/

}