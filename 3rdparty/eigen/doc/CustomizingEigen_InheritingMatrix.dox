namespace Eigen {

/** \page TopicCustomizing_InheritingMatrix Inheriting from Matrix

Before inheriting from Matrix, be really, I mean REALL<PERSON>, sure that using
EIGEN_MATRIX_PLUGIN is not what you really want (see previous section).
If you just need to add few members to Matrix, this is the way to go.

An example of when you actually need to inherit Matrix, is when you
have several layers of heritage such as 
MyVerySpecificVector1, MyVerySpecificVector2 -> MyVector1 -> Matrix and
MyVerySpecificVector3, MyVerySpecificVector4 -> MyVector2 -> Matrix.

In order for your object to work within the %Eigen framework, you need to
define a few members in your inherited class.

Here is a minimalistic example:

\include CustomizingEigen_Inheritance.cpp

Output: \verbinclude CustomizingEigen_Inheritance.out

This is the kind of error you can get if you don't provide those methods
\verbatim
error: no match for ‘operator=’ in ‘v = Eigen::operator*(
const Eigen::MatrixBase<Eigen::Matrix<double, -0x000000001, 1, 0, -0x000000001, 1> >::<PERSON>ala<PERSON>&, 
const Eigen::MatrixBase<Eigen::Matrix<double, -0x000000001, 1> >::StorageBaseType&)
(((const Eigen::MatrixBase<Eigen::Matrix<double, -0x000000001, 1> >::StorageBaseType&)
((const Eigen::MatrixBase<Eigen::Matrix<double, -0x000000001, 1> >::StorageBaseType*)(& v))))’
\endverbatim

*/

}
