namespace Eigen {

/** \eigenManualPage TutorialReshape Reshape

Since the version 3.4, %Eigen exposes convenient methods to reshape a matrix to another matrix of different sizes or vector.
All cases are handled via the DenseBase::reshaped(NRowsType,NColsType) and DenseBase::reshaped() functions.
Those functions do not perform in-place reshaping, but instead return a <i> view </i> on the input expression.

\eigenAutoToc

\section TutorialReshapeMat2Mat Reshaped 2D views

The more general reshaping transformation is handled via: `reshaped(nrows,ncols)`.
Here is an example reshaping a 4x4 matrix to a 2x8 one:

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include MatrixBase_reshaped_int_int.cpp
</td>
<td>
\verbinclude MatrixBase_reshaped_int_int.out
</td></tr></table>

By default, the input coefficients are always interpreted in column-major order regardless of the storage order of the input expression.
For more control on ordering, compile-time sizes, and automatic size deduction, please see de documentation of DenseBase::reshaped(NRowsType,NColsType) that contains all the details with many examples.


\section TutorialReshapeMat2Vec 1D linear views

A very common usage of reshaping is to create a 1D linear view over a given 2D matrix or expression.
In this case, sizes can be deduced and thus omitted as in the following example:

<table class="example">
<tr><th>Example:</th></tr>
<tr><td>
\include MatrixBase_reshaped_to_vector.cpp
</td></tr>
<tr><th>Output:</th></tr>
<tr><td>
\verbinclude MatrixBase_reshaped_to_vector.out
</td></tr></table>

This shortcut always returns a column vector and by default input coefficients are always interpreted in column-major order.
Again, see the documentation of DenseBase::reshaped() for more control on the ordering.

\section TutorialReshapeInPlace

The above examples create reshaped views, but what about reshaping inplace a given matrix?
Of course this task in only conceivable for matrix and arrays having runtime dimensions.
In many cases, this can be accomplished via PlainObjectBase::resize(Index,Index):

<table class="example">
<tr><th>Example:</th></tr>
<tr><td>
\include Tutorial_reshaped_vs_resize_1.cpp
</td></tr>
<tr><th>Output:</th></tr>
<tr><td>
\verbinclude Tutorial_reshaped_vs_resize_1.out
</td></tr></table>

However beware that unlike \c reshaped, the result of \c resize depends on the input storage order.
It thus behaves similarly to `reshaped<AutoOrder>`:

<table class="example">
<tr><th>Example:</th></tr>
<tr><td>
\include Tutorial_reshaped_vs_resize_2.cpp
</td></tr>
<tr><th>Output:</th></tr>
<tr><td>
\verbinclude Tutorial_reshaped_vs_resize_2.out
</td></tr></table>

Finally, assigning a reshaped matrix to itself is currently not supported and will result to undefined-behavior because of \link TopicAliasing aliasing \endlink.
The following is forbidden: \code A = A.reshaped(2,8); \endcode
This is OK: \code A = A.reshaped(2,8).eval(); \endcode

*/

}
