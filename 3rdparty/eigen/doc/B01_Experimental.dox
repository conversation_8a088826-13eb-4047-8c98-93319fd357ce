namespace Eigen {

/** \page Experimental Experimental parts of Eigen

\eigenAutoToc

\section Experimental_summary Summary

With the 2.0 release, Eigen's API is, to a large extent, stable. However, we wish to retain the freedom to make API incompatible changes. To that effect, we call many parts of Eigen "experimental" which means that they are not subject to API stability guarantee.

Our goal is that for the 2.1 release (expected in July 2009) most of these parts become API-stable too.

We are aware that API stability is a major concern for our users. That's why it's a priority for us to reach it, but at the same time we're being serious about not calling Eigen API-stable too early.

Experimental features may at any time:
\li be removed;
\li be subject to an API incompatible change;
\li introduce API or ABI incompatible changes in your own code if you let them affect your API or ABI.

\section Experimental_modules Experimental modules

The following modules are considered entirely experimental, and we make no firm API stability guarantee about them for the time being:
\li SVD
\li QR
\li Cholesky
\li Sparse
\li Geometry (this one should be mostly stable, but it's a little too early to make a formal guarantee)

\section Experimental_core Experimental parts of the Core module

In the Core module, the only classes subject to ABI stability guarantee (meaning that you can use it for data members in your public ABI) is:
\li Matrix
\li Map

All other classes offer no ABI guarantee, e.g. the layout of their data can be changed.

The only classes subject to (even partial) API stability guarantee (meaning that you can safely construct and use objects) are:
\li MatrixBase : partial API stability (see below)
\li Matrix : full API stability (except for experimental stuff inherited from MatrixBase)
\li Map : full API stability (except for experimental stuff inherited from MatrixBase)

All other classes offer no direct API guarantee, e.g. their methods can be changed; however notice that most classes inherit MatrixBase and that this is where most of their API comes from -- so in practice most of the API is stable.

A few MatrixBase methods are considered experimental, hence not part of any API stability guarantee:
\li all methods documented as internal
\li all methods hidden in the Doxygen documentation
\li all methods marked as experimental
\li all methods defined in experimental modules

*/

}
