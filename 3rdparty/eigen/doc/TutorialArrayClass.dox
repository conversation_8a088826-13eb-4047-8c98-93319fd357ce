namespace Eigen {

/** \eigenManualPage TutorialArrayClass The Array class and coefficient-wise operations

This page aims to provide an overview and explanations on how to use
<PERSON><PERSON><PERSON>'s Array class.

\eigenAutoToc
  
\section TutorialArrayClassIntro What is the Array class?

The Array class provides general-purpose arrays, as opposed to the Matrix class which
is intended for linear algebra. Furthermore, the Array class provides an easy way to
perform coefficient-wise operations, which might not have a linear algebraic meaning,
such as adding a constant to every coefficient in the array or multiplying two arrays coefficient-wise.


\section TutorialArrayClassTypes Array types
Array is a class template taking the same template parameters as Matrix.
As with Matrix, the first three template parameters are mandatory:
\code
Array<typename Scalar, int RowsAtCompileTime, int ColsAtCompileTime>
\endcode
The last three template parameters are optional. Since this is exactly the same as for Matrix,
we won't explain it again here and just refer to \ref TutorialMatrixClass.

Eigen also provides typedefs for some common cases, in a way that is similar to the Matrix typedefs
but with some slight differences, as the word "array" is used for both 1-dimensional and 2-dimensional arrays.
We adopt the convention that typedefs of the form ArrayNt stand for 1-dimensional arrays, where N and t are
the size and the scalar type, as in the Matrix typedefs explained on \ref TutorialMatrixClass "this page". For 2-dimensional arrays, we
use typedefs of the form ArrayNNt. Some examples are shown in the following table:

<table class="manual">
  <tr>
    <th>Type </th>
    <th>Typedef </th>
  </tr>
  <tr>
    <td> \code Array<float,Dynamic,1> \endcode </td>
    <td> \code ArrayXf \endcode </td>
  </tr>
  <tr>
    <td> \code Array<float,3,1> \endcode </td>
    <td> \code Array3f \endcode </td>
  </tr>
  <tr>
    <td> \code Array<double,Dynamic,Dynamic> \endcode </td>
    <td> \code ArrayXXd \endcode </td>
  </tr>
  <tr>
    <td> \code Array<double,3,3> \endcode </td>
    <td> \code Array33d \endcode </td>
  </tr>
</table>


\section TutorialArrayClassAccess Accessing values inside an Array

The parenthesis operator is overloaded to provide write and read access to the coefficients of an array, just as with matrices.
Furthermore, the \c << operator can be used to initialize arrays (via the comma initializer) or to print them.

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ArrayClass_accessors.cpp
</td>
<td>
\verbinclude Tutorial_ArrayClass_accessors.out
</td></tr></table>

For more information about the comma initializer, see \ref TutorialAdvancedInitialization.


\section TutorialArrayClassAddSub Addition and subtraction

Adding and subtracting two arrays is the same as for matrices.
The operation is valid if both arrays have the same size, and the addition or subtraction is done coefficient-wise.

Arrays also support expressions of the form <tt>array + scalar</tt> which add a scalar to each coefficient in the array.
This provides a functionality that is not directly available for Matrix objects.

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ArrayClass_addition.cpp
</td>
<td>
\verbinclude Tutorial_ArrayClass_addition.out
</td></tr></table>


\section TutorialArrayClassMult Array multiplication

First of all, of course you can multiply an array by a scalar, this works in the same way as matrices. Where arrays
are fundamentally different from matrices, is when you multiply two together. Matrices interpret
multiplication as matrix product and arrays interpret multiplication as coefficient-wise product. Thus, two 
arrays can be multiplied if and only if they have the same dimensions.

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ArrayClass_mult.cpp
</td>
<td>
\verbinclude Tutorial_ArrayClass_mult.out
</td></tr></table>


\section TutorialArrayClassCwiseOther Other coefficient-wise operations

The Array class defines other coefficient-wise operations besides the addition, subtraction and multiplication
operators described above. For example, the \link ArrayBase::abs() .abs() \endlink method takes the absolute
value of each coefficient, while \link ArrayBase::sqrt() .sqrt() \endlink computes the square root of the
coefficients. If you have two arrays of the same size, you can call \link ArrayBase::min(const Eigen::ArrayBase<OtherDerived>&) const .min(.) \endlink to
construct the array whose coefficients are the minimum of the corresponding coefficients of the two given
arrays. These operations are illustrated in the following example.

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ArrayClass_cwise_other.cpp
</td>
<td>
\verbinclude Tutorial_ArrayClass_cwise_other.out
</td></tr></table>

More coefficient-wise operations can be found in the \ref QuickRefPage.


\section TutorialArrayClassConvert Converting between array and matrix expressions

When should you use objects of the Matrix class and when should you use objects of the Array class? You cannot
apply Matrix operations on arrays, or Array operations on matrices. Thus, if you need to do linear algebraic
operations such as matrix multiplication, then you should use matrices; if you need to do coefficient-wise
operations, then you should use arrays. However, sometimes it is not that simple, but you need to use both
Matrix and Array operations. In that case, you need to convert a matrix to an array or reversely. This gives
access to all operations regardless of the choice of declaring objects as arrays or as matrices.

\link MatrixBase Matrix expressions \endlink have an \link MatrixBase::array() .array() \endlink method that
'converts' them into \link ArrayBase array expressions\endlink, so that coefficient-wise operations
can be applied easily. Conversely, \link ArrayBase array expressions \endlink
have a \link ArrayBase::matrix() .matrix() \endlink method. As with all Eigen expression abstractions,
this doesn't have any runtime cost (provided that you let your compiler optimize).
Both \link MatrixBase::array() .array() \endlink and \link ArrayBase::matrix() .matrix() \endlink 
can be used as rvalues and as lvalues.

Mixing matrices and arrays in an expression is forbidden with Eigen. For instance, you cannot add a matrix and
array directly; the operands of a \c + operator should either both be matrices or both be arrays. However,
it is easy to convert from one to the other with \link MatrixBase::array() .array() \endlink and 
\link ArrayBase::matrix() .matrix()\endlink. The exception to this rule is the assignment operator: it is
allowed to assign a matrix expression to an array variable, or to assign an array expression to a matrix
variable.

The following example shows how to use array operations on a Matrix object by employing the 
\link MatrixBase::array() .array() \endlink method. For example, the statement 
<tt>result = m.array() * n.array()</tt> takes two matrices \c m and \c n, converts them both to an array, uses
* to multiply them coefficient-wise and assigns the result to the matrix variable \c result (this is legal
because Eigen allows assigning array expressions to matrix variables). 

As a matter of fact, this usage case is so common that Eigen provides a \link MatrixBase::cwiseProduct const
.cwiseProduct(.) \endlink method for matrices to compute the coefficient-wise product. This is also shown in
the example program.

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ArrayClass_interop_matrix.cpp
</td>
<td>
\verbinclude Tutorial_ArrayClass_interop_matrix.out
</td></tr></table>

Similarly, if \c array1 and \c array2 are arrays, then the expression <tt>array1.matrix() * array2.matrix()</tt>
computes their matrix product.

Here is a more advanced example. The expression <tt>(m.array() + 4).matrix() * m</tt> adds 4 to every
coefficient in the matrix \c m and then computes the matrix product of the result with \c m. Similarly, the
expression <tt>(m.array() * n.array()).matrix() * m</tt> computes the coefficient-wise product of the matrices
\c m and \c n and then the matrix product of the result with \c m.

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ArrayClass_interop.cpp
</td>
<td>
\verbinclude Tutorial_ArrayClass_interop.out
</td></tr></table>

*/

}
