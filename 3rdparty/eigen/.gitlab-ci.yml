# This file is part of Eigen, a lightweight C++ template library
# for linear algebra.
#
# Copyright (C) 2020 Arm Ltd. and Contributors
#
# This Source Code Form is subject to the terms of the Mozilla
# Public License v. 2.0. If a copy of the MPL was not distributed
# with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

stages:
  - buildsmoketests
  - smoketests
  - build
  - test

variables:
  BUILDDIR: builddir
  EIGEN_CI_CMAKE_GENEATOR: "Ninja"

include:
  - "/ci/smoketests.gitlab-ci.yml"
  - "/ci/build.gitlab-ci.yml"
  - "/ci/test.gitlab-ci.yml"
