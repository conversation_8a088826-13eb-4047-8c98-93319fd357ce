# The file split_test_helper.h was generated at first run,
# it is now included in test/
if(EXISTS ${CMAKE_CURRENT_BINARY_DIR}/split_test_helper.h)
  file(REMOVE ${CMAKE_CURRENT_BINARY_DIR}/split_test_helper.h)
endif()

# check if we have a Fortran compiler
include(CheckLanguage)
check_language(Fortran)
if(CMAKE_Fortran_COMPILER)
  enable_language(Fortran)
  set(EIGEN_Fortran_COMPILER_WORKS ON)
else()
  set(EIGEN_Fortran_COMPILER_WORKS OFF)
  # search for a default Lapack library to complete <PERSON>ige<PERSON>'s one
  find_package(LAPACK QUIET)
endif()

# TODO do the same for EXTERNAL_LAPACK
option(EIGEN_TEST_EXTERNAL_BLAS "Use external BLAS library for testsuite" OFF)
if(EIGEN_TEST_EXTERNAL_BLAS)
  find_package(BLAS REQUIRED)
  message(STATUS "BLAS_COMPILER_FLAGS: ${BLAS_COMPILER_FLAGS}")
  add_definitions("-DE<PERSON>EN_USE_BLAS") # is adding  ${BLAS_COMPILER_FLAGS} necessary?
  list(APPEND EXTERNAL_LIBS "${BLAS_LIBRARIES}")
endif()

# configure blas/lapack (use Eigen's ones)
set(EIGEN_BLAS_LIBRARIES eigen_blas)
set(EIGEN_LAPACK_LIBRARIES eigen_lapack)

set(EIGEN_TEST_MATRIX_DIR "" CACHE STRING "Enable testing of realword sparse matrices contained in the specified path")
if(EIGEN_TEST_MATRIX_DIR)
  if(NOT WIN32)
    message(STATUS "Test realworld sparse matrices: ${EIGEN_TEST_MATRIX_DIR}")
    add_definitions( -DTEST_REAL_CASES="${EIGEN_TEST_MATRIX_DIR}" )
  else()
    message(STATUS "REAL CASES CAN NOT BE CURRENTLY TESTED ON WIN32")
  endif()
endif()

set(SPARSE_LIBS " ")

find_package(CHOLMOD)
if(CHOLMOD_FOUND)
  add_definitions("-DEIGEN_CHOLMOD_SUPPORT")
  include_directories(${CHOLMOD_INCLUDES})
  set(SPARSE_LIBS ${SPARSE_LIBS} ${CHOLMOD_LIBRARIES} ${EIGEN_BLAS_LIBRARIES} ${EIGEN_LAPACK_LIBRARIES})
  set(CHOLMOD_ALL_LIBS  ${CHOLMOD_LIBRARIES} ${EIGEN_BLAS_LIBRARIES} ${EIGEN_LAPACK_LIBRARIES})
  ei_add_property(EIGEN_TESTED_BACKENDS "CHOLMOD, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS "CHOLMOD, ")
endif()

find_package(UMFPACK)
if(UMFPACK_FOUND)
  add_definitions("-DEIGEN_UMFPACK_SUPPORT")
  include_directories(${UMFPACK_INCLUDES})
  set(SPARSE_LIBS ${SPARSE_LIBS} ${UMFPACK_LIBRARIES} ${EIGEN_BLAS_LIBRARIES})
  set(UMFPACK_ALL_LIBS ${UMFPACK_LIBRARIES} ${EIGEN_BLAS_LIBRARIES})
  ei_add_property(EIGEN_TESTED_BACKENDS "UMFPACK, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS "UMFPACK, ")
endif()

find_package(KLU)
if(KLU_FOUND)
  add_definitions("-DEIGEN_KLU_SUPPORT")
  include_directories(${KLU_INCLUDES})
  set(SPARSE_LIBS ${SPARSE_LIBS} ${KLU_LIBRARIES} ${EIGEN_BLAS_LIBRARIES})
  set(KLU_ALL_LIBS ${KLU_LIBRARIES} ${EIGEN_BLAS_LIBRARIES})
  ei_add_property(EIGEN_TESTED_BACKENDS "KLU, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS "KLU, ")
endif()

find_package(SuperLU 4.0)
if(SuperLU_FOUND)
  add_definitions("-DEIGEN_SUPERLU_SUPPORT")
  include_directories(${SUPERLU_INCLUDES})
  set(SPARSE_LIBS ${SPARSE_LIBS} ${SUPERLU_LIBRARIES} ${EIGEN_BLAS_LIBRARIES})
  set(SUPERLU_ALL_LIBS ${SUPERLU_LIBRARIES} ${EIGEN_BLAS_LIBRARIES})
  ei_add_property(EIGEN_TESTED_BACKENDS  "SuperLU, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS  "SuperLU, ")
endif()


find_package(PASTIX QUIET COMPONENTS METIS SEQ)
# check that the PASTIX found is a version without MPI
find_path(PASTIX_pastix_nompi.h_INCLUDE_DIRS
  NAMES pastix_nompi.h
  HINTS ${PASTIX_INCLUDE_DIRS}
)
if (NOT PASTIX_pastix_nompi.h_INCLUDE_DIRS)
  message(STATUS "A version of Pastix has been found but pastix_nompi.h does not exist in the include directory."
                 " Because Eigen tests require a version without MPI, we disable the Pastix backend.")
endif()
if(PASTIX_FOUND AND PASTIX_pastix_nompi.h_INCLUDE_DIRS)
  add_definitions("-DEIGEN_PASTIX_SUPPORT")
  include_directories(${PASTIX_INCLUDE_DIRS_DEP})
  if(SCOTCH_FOUND)
    include_directories(${SCOTCH_INCLUDE_DIRS})
    set(PASTIX_LIBRARIES ${PASTIX_LIBRARIES} ${SCOTCH_LIBRARIES})
  elseif(METIS_FOUND)
    include_directories(${METIS_INCLUDE_DIRS})
    set(PASTIX_LIBRARIES ${PASTIX_LIBRARIES} ${METIS_LIBRARIES})
  else()
    ei_add_property(EIGEN_MISSING_BACKENDS  "PaStiX, ")
  endif()
  set(SPARSE_LIBS ${SPARSE_LIBS} ${PASTIX_LIBRARIES_DEP} ${ORDERING_LIBRARIES})
  set(PASTIX_ALL_LIBS ${PASTIX_LIBRARIES_DEP})
  ei_add_property(EIGEN_TESTED_BACKENDS  "PaStiX, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS  "PaStiX, ")
endif()

if(METIS_FOUND)
  add_definitions("-DEIGEN_METIS_SUPPORT")
  include_directories(${METIS_INCLUDE_DIRS})
  ei_add_property(EIGEN_TESTED_BACKENDS "METIS, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS "METIS, ")
endif()

find_package(SPQR)
if(SPQR_FOUND AND CHOLMOD_FOUND AND (EIGEN_Fortran_COMPILER_WORKS OR LAPACK_FOUND) )
  add_definitions("-DEIGEN_SPQR_SUPPORT")
  include_directories(${SPQR_INCLUDES})
  set(SPQR_ALL_LIBS ${SPQR_LIBRARIES} ${CHOLMOD_LIBRARIES} ${EIGEN_LAPACK_LIBRARIES} ${EIGEN_BLAS_LIBRARIES} ${LAPACK_LIBRARIES})
  set(SPARSE_LIBS ${SPARSE_LIBS} ${SPQR_ALL_LIBS})
  ei_add_property(EIGEN_TESTED_BACKENDS "SPQR, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS "SPQR, ")
endif()

option(EIGEN_TEST_NOQT "Disable Qt support in unit tests" OFF)
if(NOT EIGEN_TEST_NOQT)
  find_package(Qt4)
  if(QT4_FOUND)
    include(${QT_USE_FILE})
    ei_add_property(EIGEN_TESTED_BACKENDS  "Qt4 support, ")
  else()
    ei_add_property(EIGEN_MISSING_BACKENDS  "Qt4 support, ")
  endif()
endif()

if(TEST_LIB)
  add_definitions("-DEIGEN_EXTERN_INSTANTIATIONS=1")
endif()

set_property(GLOBAL PROPERTY EIGEN_CURRENT_SUBPROJECT "Official")
add_custom_target(BuildOfficial)

ei_add_test(rand)
ei_add_test(meta)
ei_add_test(numext)
ei_add_test(sizeof)
ei_add_test(dynalloc)
ei_add_test(nomalloc)
ei_add_test(first_aligned)
ei_add_test(type_alias)
ei_add_test(nullary)
ei_add_test(mixingtypes)
ei_add_test(io)
ei_add_test(packetmath "-DEIGEN_FAST_MATH=1")
ei_add_test(vectorization_logic)
ei_add_test(basicstuff)
ei_add_test(constructor)
ei_add_test(linearstructure)
ei_add_test(integer_types)
ei_add_test(unalignedcount)
if(NOT EIGEN_TEST_NO_EXCEPTIONS AND NOT EIGEN_TEST_OPENMP)
  ei_add_test(exceptions)
endif()
ei_add_test(redux)
ei_add_test(visitor)
ei_add_test(block)
ei_add_test(corners)
ei_add_test(symbolic_index)
ei_add_test(indexed_view)
ei_add_test(reshape)
ei_add_test(swap)
ei_add_test(resize)
ei_add_test(conservative_resize)
ei_add_test(product_small)
ei_add_test(product_large)
ei_add_test(product_extra)
ei_add_test(diagonalmatrices)
ei_add_test(adjoint)
ei_add_test(diagonal)
ei_add_test(miscmatrices)
ei_add_test(commainitializer)
ei_add_test(smallvectors)
ei_add_test(mapped_matrix)
ei_add_test(mapstride)
ei_add_test(mapstaticmethods)
ei_add_test(array_cwise)
ei_add_test(array_for_matrix)
ei_add_test(array_replicate)
ei_add_test(array_reverse)
ei_add_test(ref)
ei_add_test(is_same_dense)
ei_add_test(triangular)
ei_add_test(selfadjoint)
ei_add_test(product_selfadjoint)
ei_add_test(product_symm)
ei_add_test(product_syrk)
ei_add_test(product_trmv)
ei_add_test(product_trmm)
ei_add_test(product_trsolve)
ei_add_test(product_mmtr)
ei_add_test(product_notemporary)
ei_add_test(stable_norm)
ei_add_test(permutationmatrices)
ei_add_test(bandmatrix)
ei_add_test(cholesky)
ei_add_test(lu)
ei_add_test(determinant)
ei_add_test(inverse)
ei_add_test(qr)
ei_add_test(qr_colpivoting)
ei_add_test(qr_fullpivoting)
ei_add_test(upperbidiagonalization)
ei_add_test(hessenberg)
ei_add_test(schur_real)
ei_add_test(schur_complex)
ei_add_test(eigensolver_selfadjoint)
ei_add_test(eigensolver_generic)
ei_add_test(eigensolver_complex)
ei_add_test(real_qz)
ei_add_test(eigensolver_generalized_real)
ei_add_test(jacobi)
ei_add_test(jacobisvd)
ei_add_test(bdcsvd)
ei_add_test(householder)
ei_add_test(geo_orthomethods)
ei_add_test(geo_quaternion)
ei_add_test(geo_eulerangles)
ei_add_test(geo_parametrizedline)
ei_add_test(geo_alignedbox)
ei_add_test(geo_hyperplane)
ei_add_test(geo_transformations)
ei_add_test(geo_homogeneous)
ei_add_test(stdvector)
ei_add_test(stdvector_overload)
ei_add_test(stdlist)
ei_add_test(stdlist_overload)
ei_add_test(stddeque)
ei_add_test(stddeque_overload)
ei_add_test(sparse_basic)
ei_add_test(sparse_block)
ei_add_test(sparse_vector)
ei_add_test(sparse_product)
ei_add_test(sparse_ref)
ei_add_test(sparse_solvers)
ei_add_test(sparse_permutations)
ei_add_test(simplicial_cholesky)
ei_add_test(conjugate_gradient)
ei_add_test(incomplete_cholesky)
ei_add_test(bicgstab)
ei_add_test(lscg)
ei_add_test(sparselu)
ei_add_test(sparseqr)
ei_add_test(umeyama)
ei_add_test(nesting_ops "${CMAKE_CXX_FLAGS_DEBUG}")
ei_add_test(nestbyvalue)
ei_add_test(zerosized)
ei_add_test(dontalign)
ei_add_test(evaluators)
if(NOT EIGEN_TEST_NO_EXCEPTIONS)
  ei_add_test(sizeoverflow)
endif()
ei_add_test(prec_inverse_4x4)
ei_add_test(vectorwiseop)
ei_add_test(special_numbers)
ei_add_test(rvalue_types)
ei_add_test(dense_storage)
ei_add_test(ctorleak)
ei_add_test(mpl2only)
ei_add_test(inplace_decomposition)
ei_add_test(half_float)
ei_add_test(bfloat16_float)
ei_add_test(array_of_string)
ei_add_test(num_dimensions)
ei_add_test(stl_iterators)
ei_add_test(blasutil)
if(EIGEN_TEST_CXX11)
  ei_add_test(initializer_list_construction)
  ei_add_test(diagonal_matrix_variadic_ctor)
endif()

add_executable(bug1213 bug1213.cpp bug1213_main.cpp)

check_cxx_compiler_flag("-ffast-math" COMPILER_SUPPORT_FASTMATH)
if(COMPILER_SUPPORT_FASTMATH)
  set(EIGEN_FASTMATH_FLAGS "-ffast-math")
else()
  check_cxx_compiler_flag("/fp:fast" COMPILER_SUPPORT_FPFAST)
  if(COMPILER_SUPPORT_FPFAST)
    set(EIGEN_FASTMATH_FLAGS "/fp:fast")
  endif()
endif()

ei_add_test(fastmath " ${EIGEN_FASTMATH_FLAGS} ")

# # ei_add_test(denseLM)

if(QT4_FOUND)
  ei_add_test(qtvector "" "${QT_QTCORE_LIBRARY}")
endif()

if(UMFPACK_FOUND)
  ei_add_test(umfpack_support "" "${UMFPACK_ALL_LIBS}")
endif()

if(KLU_FOUND OR SuiteSparse_FOUND)
  ei_add_test(klu_support "" "${KLU_ALL_LIBS}")
endif()

if(SUPERLU_FOUND)
  ei_add_test(superlu_support "" "${SUPERLU_ALL_LIBS}")
endif()

if(CHOLMOD_FOUND)
  ei_add_test(cholmod_support "" "${CHOLMOD_ALL_LIBS}")
endif()

if(PARDISO_FOUND)
  ei_add_test(pardiso_support "" "${PARDISO_ALL_LIBS}")
endif()

if(PASTIX_FOUND AND (SCOTCH_FOUND OR METIS_FOUND))
  ei_add_test(pastix_support "" "${PASTIX_ALL_LIBS}")
endif()

if(SPQR_FOUND AND CHOLMOD_FOUND)
  ei_add_test(spqr_support "" "${SPQR_ALL_LIBS}")
endif()

if(METIS_FOUND)
ei_add_test(metis_support "" "${METIS_LIBRARIES}")
endif()

string(TOLOWER "${CMAKE_CXX_COMPILER}" cmake_cxx_compiler_tolower)
if(cmake_cxx_compiler_tolower MATCHES "qcc")
  set(CXX_IS_QCC "ON")
endif()

ei_add_property(EIGEN_TESTING_SUMMARY "CXX:               ${CMAKE_CXX_COMPILER}\n")
if(CMAKE_COMPILER_IS_GNUCXX AND NOT CXX_IS_QCC)
  execute_process(COMMAND ${CMAKE_CXX_COMPILER} --version COMMAND head -n 1 OUTPUT_VARIABLE EIGEN_CXX_VERSION_STRING OUTPUT_STRIP_TRAILING_WHITESPACE)
  ei_add_property(EIGEN_TESTING_SUMMARY "CXX_VERSION:       ${EIGEN_CXX_VERSION_STRING}\n")
endif()
ei_add_property(EIGEN_TESTING_SUMMARY "CXX_FLAGS:         ${CMAKE_CXX_FLAGS}\n")
if (EIGEN_TEST_CUSTOM_CXX_FLAGS)
  ei_add_property(EIGEN_TESTING_SUMMARY "Custom CXX flags:  ${EIGEN_TEST_CUSTOM_CXX_FLAGS}\n")
endif()
ei_add_property(EIGEN_TESTING_SUMMARY "Sparse lib flags:  ${SPARSE_LIBS}\n")

option(EIGEN_TEST_EIGEN2 "Run whole Eigen2 test suite against EIGEN2_SUPPORT" OFF)
mark_as_advanced(EIGEN_TEST_EIGEN2)
if(EIGEN_TEST_EIGEN2)
  message(WARNING "The Eigen2 test suite has been removed")
endif()

# boost MP unit test
find_package(Boost 1.53.0)
if(Boost_FOUND)
  include_directories(${Boost_INCLUDE_DIRS})
  ei_add_test(boostmultiprec "" "${Boost_LIBRARIES}")
  ei_add_property(EIGEN_TESTED_BACKENDS "Boost.Multiprecision, ")
else()
  ei_add_property(EIGEN_MISSING_BACKENDS "Boost.Multiprecision, ")
endif()


# CUDA unit tests
option(EIGEN_TEST_CUDA "Enable CUDA support in unit tests" OFF)
option(EIGEN_TEST_CUDA_CLANG "Use clang instead of nvcc to compile the CUDA tests" OFF)

if(EIGEN_TEST_CUDA_CLANG AND NOT CMAKE_CXX_COMPILER MATCHES "clang")
  message(WARNING "EIGEN_TEST_CUDA_CLANG is set, but CMAKE_CXX_COMPILER does not appear to be clang.")
endif()

if(EIGEN_TEST_CUDA)

find_package(CUDA 5.0)
if(CUDA_FOUND)
  
  set(CUDA_PROPAGATE_HOST_FLAGS OFF)
  
  set(EIGEN_CUDA_RELAXED_CONSTEXPR "--expt-relaxed-constexpr")
  if (${CUDA_VERSION} STREQUAL "7.0")
    set(EIGEN_CUDA_RELAXED_CONSTEXPR "--relaxed-constexpr")
  endif()
  
  if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "Clang") 
    set(CUDA_NVCC_FLAGS "-ccbin ${CMAKE_C_COMPILER}" CACHE STRING "nvcc flags" FORCE)
  endif()
  if(EIGEN_TEST_CUDA_CLANG)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
    string(APPEND CMAKE_CXX_FLAGS " --cuda-path=${CUDA_TOOLKIT_ROOT_DIR}")
    foreach(GPU IN LISTS EIGEN_CUDA_COMPUTE_ARCH)
      string(APPEND CMAKE_CXX_FLAGS " --cuda-gpu-arch=sm_${GPU}")
    endforeach()
  else()
    foreach(GPU IN LISTS EIGEN_CUDA_COMPUTE_ARCH)
      string(APPEND CUDA_NVCC_FLAGS " -gencode arch=compute_${GPU},code=sm_${GPU}")
    endforeach()
  endif()
  string(APPEND CUDA_NVCC_FLAGS " ${EIGEN_CUDA_RELAXED_CONSTEXPR}")
  set(EIGEN_ADD_TEST_FILENAME_EXTENSION  "cu")
  
  ei_add_test(gpu_basic)
  
  unset(EIGEN_ADD_TEST_FILENAME_EXTENSION)

endif()

endif()


# HIP unit tests
option(EIGEN_TEST_HIP "Add HIP support." OFF)
if (EIGEN_TEST_HIP)

  set(HIP_PATH "/opt/rocm/hip" CACHE STRING "Path to the HIP installation.")

  if (EXISTS ${HIP_PATH})
    
    list(APPEND CMAKE_MODULE_PATH ${HIP_PATH}/cmake) 

    find_package(HIP REQUIRED)
    if (HIP_FOUND)

      execute_process(COMMAND ${HIP_PATH}/bin/hipconfig --platform OUTPUT_VARIABLE HIP_PLATFORM)

      if ((${HIP_PLATFORM} STREQUAL "hcc") OR (${HIP_PLATFORM} STREQUAL "amd"))

	include_directories(${HIP_PATH}/include)

	set(EIGEN_ADD_TEST_FILENAME_EXTENSION  "cu")
	ei_add_test(gpu_basic)
	unset(EIGEN_ADD_TEST_FILENAME_EXTENSION)
	
      elseif ((${HIP_PLATFORM} STREQUAL "nvcc") OR (${HIP_PLATFORM} STREQUAL "nvidia"))
	message(FATAL_ERROR "HIP_PLATFORM = nvcc is not supported within Eigen")
      else ()
	message(FATAL_ERROR "Unknown HIP_PLATFORM = ${HIP_PLATFORM}")
      endif() 
    endif()
  else ()
    message(FATAL_ERROR "EIGEN_TEST_HIP is ON, but the specified HIP_PATH (${HIP_PATH}) does not exist")
  endif()
endif()

cmake_dependent_option(EIGEN_TEST_BUILD_DOCUMENTATION "Test building the doxygen documentation" OFF "EIGEN_BUILD_DOC" OFF)
if(EIGEN_TEST_BUILD_DOCUMENTATION)
  add_dependencies(buildtests doc)
endif()

# Register all smoke tests
include("EigenSmokeTestList")
ei_add_smoke_tests("${ei_smoke_test_list}")
