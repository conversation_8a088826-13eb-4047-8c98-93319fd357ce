// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
// Copyright (C) 2011 <PERSON><PERSON> <<EMAIL>>
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "sparse_solver.h"

template<typename T, typename I_, int flag> void test_simplicial_cholesky_T()
{
  typedef SparseMatrix<T,flag,I_> SparseMatrixType;
  SimplicialCholesky<SparseMatrixType, Lower> chol_colmajor_lower_amd;
  SimplicialCholesky<SparseMatrixType, Upper> chol_colmajor_upper_amd;
  SimplicialLLT<     SparseMatrixType, Lower> llt_colmajor_lower_amd;
  SimplicialLLT<     SparseMatrixType, Upper> llt_colmajor_upper_amd;
  SimplicialLDLT<    SparseMatrixType, Lower> ldlt_colmajor_lower_amd;
  SimplicialLDLT<    SparseMatrixType, Upper> ldlt_colmajor_upper_amd;
  SimplicialLDLT<    SparseMatrixType, Lower, NaturalOrdering<I_> > ldlt_colmajor_lower_nat;
  SimplicialLDLT<    SparseMatrixType, Upper, NaturalOrdering<I_> > ldlt_colmajor_upper_nat;

  check_sparse_spd_solving(chol_colmajor_lower_amd);
  check_sparse_spd_solving(chol_colmajor_upper_amd);
  check_sparse_spd_solving(llt_colmajor_lower_amd);
  check_sparse_spd_solving(llt_colmajor_upper_amd);
  check_sparse_spd_solving(ldlt_colmajor_lower_amd);
  check_sparse_spd_solving(ldlt_colmajor_upper_amd);
  
  check_sparse_spd_determinant(chol_colmajor_lower_amd);
  check_sparse_spd_determinant(chol_colmajor_upper_amd);
  check_sparse_spd_determinant(llt_colmajor_lower_amd);
  check_sparse_spd_determinant(llt_colmajor_upper_amd);
  check_sparse_spd_determinant(ldlt_colmajor_lower_amd);
  check_sparse_spd_determinant(ldlt_colmajor_upper_amd);
  
  check_sparse_spd_solving(ldlt_colmajor_lower_nat, (std::min)(300,EIGEN_TEST_MAX_SIZE), 1000);
  check_sparse_spd_solving(ldlt_colmajor_upper_nat, (std::min)(300,EIGEN_TEST_MAX_SIZE), 1000);
}

EIGEN_DECLARE_TEST(simplicial_cholesky)
{
  CALL_SUBTEST_11(( test_simplicial_cholesky_T<double,               int, ColMajor>() ));
  CALL_SUBTEST_12(( test_simplicial_cholesky_T<std::complex<double>, int, ColMajor>() ));
  CALL_SUBTEST_13(( test_simplicial_cholesky_T<double,          long int, ColMajor>() ));
  CALL_SUBTEST_21(( test_simplicial_cholesky_T<double,               int, RowMajor>() ));
  CALL_SUBTEST_22(( test_simplicial_cholesky_T<std::complex<double>, int, RowMajor>() ));
  CALL_SUBTEST_23(( test_simplicial_cholesky_T<double,          long int, RowMajor>() ));
}
