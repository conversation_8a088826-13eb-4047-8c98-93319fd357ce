# This is a comment.

version: build.{build}

os: Windows Server 2012 R2

clone_folder: c:\projects\jsoncpp

platform:
    - Win32
    - x64

configuration:
    - Debug
    - Release

# scripts to run before build
before_build:
    - echo "Running cmake..."
    - cd c:\projects\jsoncpp
    - cmake --version
    - set PATH=C:\Program Files (x86)\MSBuild\14.0\Bin;%PATH%
    - if %PLATFORM% == Win32 cmake .
    - if %PLATFORM% == x64 cmake -G "Visual Studio 12 2013 Win64" .

build:
    project: jsoncpp.sln        # path to Visual Studio solution or project

deploy:
    provider: GitHub
    auth_token:
        secure: K2Tp1q8pIZ7rs0Ot24ZMWuwr12Ev6Tc6QkhMjGQxoQG3ng1pXtgPasiJ45IDXGdg
    on:
        branch: master
        appveyor_repo_tag: true
