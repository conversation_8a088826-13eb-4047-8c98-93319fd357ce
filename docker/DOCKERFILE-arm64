# arm64 sysroot
FROM --platform=linux/arm64 docker.io/library/ros:humble-perception AS SYSROOT

RUN apt update && apt upgrade -y

RUN apt install -y wget \
    openssl \
    libssl-dev \
    libcairo2-dev \
    python3-ament-package \
    ros-humble-desktop=0.10.0-1* \
    ros-humble-desktop-full=0.10.0-1* \
    ros-humble-geographic-msgs \
    ros-humble-bond-core \
    libgraphicsmagick++-dev \
    ros-humble-diagnostic-updater \
    libxtensor-dev \
    libceres-dev \
    libompl-dev \
    ros-humble-test-msgs \
    ros-humble-libg2o \
    ros-humble-ompl \
    ros-humble-ament-cmake-black \
    libbenchmark-dev \
    graphicsmagick-libmagick-dev-compat \
    ros-humble-rcutils \
    libatlas3-base \
    libdraco-dev \
    qtbase5-dev \
    libtinyxml-dev \
    libblas-dev \
    liblapack-dev \
    libopenblas-dev \
    libatlas-base-dev \
    libboost-serialization-dev \
    libboost-filesystem-dev \
    libabsl-dev \
    liblua5.3-dev \
    && rm -rf /var/lib/apt/lists/*



# rslamware builder
FROM docker.io/library/ros:humble-perception

COPY --from=SYSROOT / /root/arm64-sysroot

RUN dpkg --add-architecture arm64

RUN echo "deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy main restricted\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy main restricted\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy-updates main restricted\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy-updates main restricted\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy universe\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy universe\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy-updates universe\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy-updates universe\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy multiverse\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy multiverse\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy-updates multiverse\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy-updates multiverse\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy-backports main restricted universe multiverse\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy-backports main restricted universe multiverse\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy-security main restricted\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy-security main restricted\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy-security universe\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy-security universe\n\
\n\
deb [arch=amd64] https://archive.ubuntu.com/ubuntu/ jammy-security multiverse\n\
deb [arch=arm64] https://ports.ubuntu.com/ubuntu-ports/ jammy-security multiverse\n\
" > /etc/apt/sources.list

RUN apt update && apt upgrade -y

RUN apt install -y build-essential \
    cmake \
    git \
    wget \
    python3-pip \
    g++-aarch64-linux-gnu \
    qemu-user-static \
    openssl \
    libssl-dev \
    libcairo2-dev \
    ros-humble-desktop=0.10.0-1* \
    ros-humble-desktop-full=0.10.0-1* \
    ros-humble-geographic-msgs \
    ros-humble-bond-core \
    libgraphicsmagick++-dev \
    ros-humble-diagnostic-updater \
    libxtensor-dev \
    libceres-dev \
    libompl-dev \
    ros-humble-test-msgs \
    ros-humble-libg2o \
    ros-humble-ompl \
    ros-humble-ament-cmake-black \
    libbenchmark-dev \
    graphicsmagick-libmagick-dev-compat \
    ros-humble-rcutils \
    libatlas3-base \
    libdraco-dev \
    qtbase5-dev \
    libtinyxml-dev \
    libblas-dev \
    liblapack-dev \
    libopenblas-dev \
    libatlas-base-dev \
    libboost-serialization-dev \
    libboost-filesystem-dev \
    liblua5.3-dev \
    libpython3-all-dev:arm64 \
    libatlas3-base:arm64 \
    libdraco-dev:arm64 \
    qtbase5-dev:arm64 \
    libtinyxml-dev:arm64 \
    libblas-dev:arm64 \
    liblapack-dev:arm64 \
    libopenblas-dev:arm64 \
    libatlas-base-dev:arm64 \
    libboost-serialization-dev:arm64 \
    libboost-filesystem-dev:arm64 \
    libabsl-dev:arm64 \
    liblua5.3-dev:arm64 \
    && rm -rf /var/lib/apt/lists/*