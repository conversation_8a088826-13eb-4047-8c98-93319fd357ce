#!/usr/bin/env bash
# This script runs the target ARM64 Python interpreter using QEMU.
# It expects the path to the target Python interpreter as its first argument.

# Ensure QEMU is in the PATH or specify its full path
QEMU_BIN="/usr/bin/qemu-aarch64-static"

# The actual Python interpreter path within the sysroot
TARGET_PYTHON_INTERPRETER="/root/arm64-sysroot/usr/bin/python3.10"

# The sysroot path for QEMU to find target libraries
SYSROOT_PATH="/root/arm64-sysroot"

# Execute QEMU with the sysroot, target Python interpreter, and pass all arguments
exec "$QEMU_BIN" -L "$SYSROOT_PATH" "$TARGET_PYTHON_INTERPRETER" "$@"
