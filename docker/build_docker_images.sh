#!/usr/bin/env bash

if command -v podman &> /dev/null; then
    podman build -f DOCKERFILE-arm64 -t localhost/rslamware-builder:arm64 .
    podman build -f DOCKERFILE-amd64 -t localhost/rslamware-builder:amd64 .
elif command -v docker &> /dev/null; then
    docker build -f DOCKERFILE-arm64 -t localhost/rslamware-builder:arm64 .
    docker build -f DOCKERFILE-amd64 -t localhost/rslamware-builder:amd64 .
else
    echo "Error: Neither podman nor docker is installed on this machine."
    exit 1
fi