set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)

set(TARGET_ARCH "arm64")

set(CMAKE_SYSTEM_SUPPORTS_DYNAMIC_LINKING TRUE CACHE BOOL "Force dynamic linking support for target" FORCE)

set(CROSS_COMPILE "aarch64-linux-gnu" CACHE STRING "Cross-compilation prefix" FORCE)
set(PYTHON_SOABI "cpython-310-aarch64-linux-gnu" CACHE STRING "Python 3 SOABI for ARM64 target" FORCE) # Adjust this value based on your target's actual SOABI

set(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)
set(CMAKE_AR aarch64-linux-gnu-ar)
set(CMAKE_RANLIB aarch64-linux-gnu-ranlib)

if (NOT DEFINED RSLAMWARE_ROOT_PATH)
    set(RSLAMWARE_ROOT_PATH ${CMAKE_SOURCE_DIR}/..)
endif()

set(CMAKE_SYSROOT /root/arm64-sysroot)
set(RSLAMWARE_INSTALL_DIR ${RSLAMWARE_ROOT_PATH}/install)
set(ROS2_INSTALL_PREFIX ${CMAKE_SYSROOT}/opt/ros/humble)

set(CMAKE_FIND_ROOT_PATH ${RSLAMWARE_INSTALL_DIR};${ROS2_INSTALL_PREFIX};${CMAKE_SYSROOT})
set(CMAKE_PREFIXPATH ${RSLAMWARE_INSTALL_DIR};${ROS2_INSTALL_PREFIX})

set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

set(Python3_FIND_FRAMEWORK "NEVER" CACHE STRING "Skip framework search" FORCE)
set(Python3_FIND_EMBEDDED "NEVER" CACHE STRING "Skip embedded search" FORCE)
set(Python3_FIND_VIRTUALENV "NEVER" CACHE STRING "Skip virtualenv search" FORCE)
set(Python3_FIND_REGISTRY "NEVER" CACHE STRING "Skip registry search" FORCE) # You already have this, but keep it.
set(Python3_FIND_STRATEGY "LOCATION" CACHE STRING "Prefer explicit location" FORCE) # This is key

set(Python3_EXECUTABLE "${RSLAMWARE_ROOT_PATH}/docker/qemu_python_wrapper.sh" CACHE FILEPATH "Python 3.10 executable for ARM64 sysroot via QEMU wrapper" FORCE)
set(Python3_LIBRARY "${CMAKE_SYSROOT}/usr/lib/aarch64-linux-gnu/libpython3.10.so" CACHE STRING "Python 3.10 library for ARM64 sysroot" FORCE)
set(Python3_LIBRARIES "${CMAKE_SYSROOT}/usr/lib/aarch64-linux-gnu/libpython3.10.so" CACHE STRING "Python 3.10 libraries for ARM64 sysroot" FORCE)
set(Python3_INCLUDE_DIRS "${CMAKE_SYSROOT}/usr/include/python3.10" CACHE PATH "Python 3.10 include directories for ARM64 sysroot" FORCE)

# Force the internal variables that FindPython3 should populate
set(Python3_FOUND TRUE CACHE BOOL "Python 3 found" FORCE)
set(Python3_VERSION "3.10.12" CACHE STRING "Python 3 version" FORCE)
set(Python3_LIBRARY_RELEASE "${CMAKE_SYSROOT}/usr/lib/aarch64-linux-gnu/libpython3.10.so" CACHE STRING "Python 3.10 release library for ARM64 sysroot" FORCE)
set(Python3_SO_VERSION "3.10" CACHE STRING "Python 3 SO version" FORCE) # Often needed for Python modules
set(Python3_SITEARCH_DIR "${CMAKE_SYSROOT}/usr/lib/python3/dist-packages" CACHE PATH "Python 3 site-packages directory for ARM64 sysroot" FORCE) # Adjust if your sysroot has it elsewhere

list(APPEND CMAKE_SHARED_LINKER_FLAGS "-L${CMAKE_SYSROOT}/usr/lib/aarch64-linux-gnu")
list(APPEND CMAKE_SHARED_LINKER_FLAGS "-lpython3.10")

set(CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG "-Wl,-rpath,")
set(CMAKE_SHARED_LIBRARY_RUNTIME_CXX_FLAG "-Wl,-rpath,")
list(APPEND CMAKE_INSTALL_RPATH "${CMAKE_SYSROOT}/usr/lib/aarch64-linux-gnu")
list(APPEND CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)

set(THREADS_PTHREAD_ARG "0" CACHE STRING "Result from TRY_RUN" FORCE)
