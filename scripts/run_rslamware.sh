#!/bin/bash

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

mapping=False
use_als=False
use_cartographer_localization=True
explore=False
scan_topic="fusion_scan"

for arg in "$@"
do
    if [ "$arg" == "mapping" ]; then
        mapping=True
    fi
    if [ "$arg" == "use_als" ]; then
        use_als=True
        use_cartographer_localization=False
    fi
    if [ "$arg" == "use_cartographer_localization" ]; then
        use_cartographer_localization=True
        use_als=False
    fi
    if [ "$arg" == "explore" ]; then
        explore=True
    fi
done

source "$RSLAMWARE_ROOT/install/setup.bash"
ros2 launch rslamware_bringup rslamware.launch.py mode:=real &
PID1=$!

if [ "$mapping" == "True" ]; then
    ros2 launch cartographer_ros mapping.launch.py mode:=real scan_topic:=$scan_topic explore:=$explore &
    PID2=$!
else
    ros2 launch nav2_bringup bringup_launch.py mode:=real scan_topic:=$scan_topic use_als:=$use_als use_cartographer_localization:=$use_cartographer_localization &
    PID2=$!
fi

wait $PID1
wait $PID2
