#!/bin/bash

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

simulator_map="$RSLAMWARE_ROOT/src/simulator/map/map.yaml" 
simulator_map_pbstream="$RSLAMWARE_ROOT/src/simulator/map/map.pbstream"
mask_file="$RSLAMWARE_ROOT/src/simulator/map/map.yaml"

mapping=False
use_als=False
use_cartographer_localization=True
explore=False

scan_topic="scan"

for arg in "$@"
do
    if [ "$arg" == "mapping" ]; then
        mapping=True
    fi
    if [ "$arg" == "use_als" ]; then
        use_als=True
        use_cartographer_localization=False
    fi
    if [ "$arg" == "use_cartographer_localization" ]; then
        use_cartographer_localization=True
        use_als=False
    fi
    if [ "$arg" == "explore" ]; then
        explore=True
    fi
done

source "$RSLAMWARE_ROOT/install/setup.bash"

ros2 launch rslamware_bringup rslamware.launch.py mode:=simulation &
PID1=$!

if [ "$mapping" == "True" ]; then
    ros2 launch cartographer_ros mapping.launch.py mode:=simulation scan_topic:=$scan_topic explore:=$explore &
    PID2=$!
else
    ros2 launch nav2_bringup bringup_launch.py mode:=simulation scan_topic:=$scan_topic map:=$simulator_map map_pbstream_file:=$simulator_map_pbstream mask_file:=$mask_file use_als:=$use_als use_cartographer_localization:=$use_cartographer_localization &
    PID2=$!
fi

ros2 run rviz2 rviz2 -d $RSLAMWARE_ROOT/src/bringup/rviz/nav2_default_view.rviz &

wait $PID1
wait $PID2