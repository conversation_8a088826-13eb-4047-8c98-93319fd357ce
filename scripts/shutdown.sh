#!/bin/bash

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

source "$RSLAMWARE_ROOT/install/setup.bash"
 
echo "正在查找 ROS 2 节点..."

# 获取所有 ROS 2 节点名称
ros2_nodes=$(ros2 node list 2>/dev/null)

manual_nodes=(
  "/rslamware"
  "/ascamera"
)

NODES=$(printf "%s\n" "${ros2_nodes[@]}" "${manual_nodes[@]}")

if [ -z "$NODES" ]; then
  echo "没有找到 ROS 2 节点。"
  exit 0
fi

# 转换节点名为匹配格式
echo "获取到以下节点："
echo "$NODES"
echo ""

for NODE in $NODES; do 
  NODE_BASENAME=$(basename "$NODE")
  
  echo "尝试查找进程包含 [$NODE_BASENAME] 的节点..."
 
  PIDS=$(ps -ef | grep "$NODE_BASENAME" | grep -v grep | awk '{print $2}')

  if [ -z "$PIDS" ]; then
    echo "没有找到进程包含 [$NODE_BASENAME]"
    continue
  fi

  for PID in $PIDS; do
    echo "尝试发送 SIGINT 给进程 PID=$PID"
    kill -2 $PID

    sleep 1

    if ps -p $PID > /dev/null; then
      echo "SIGINT 无效，强制 SIGKILL..."
      kill -9 $PID
    else
      echo "进程 $PID 已结束。"
    fi
  done
done

echo "所有处理完毕。"
