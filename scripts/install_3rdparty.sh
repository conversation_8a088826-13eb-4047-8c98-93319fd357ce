#! /bin/bash

#Usage
##############################################
# ./scripts/install_3rdparty.sh [TARGET_ARCH]

SELF="$(which $0)"
TARGET_ARCH=$1 
CC_NAME="gcc"

if [ "$TARGET_ARCH" == "" ]; then
    TARGET_ARCH=`uname -m`
fi

SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
PROJECT_ROOT="`realpath $SCRIPTS_ROOT/..`"

PYTHON_NAME=python
PROJECT_VERSION_FILE=""

function detect_python_version
{
    python_path=$(whereis python | grep "/usr/bin/python ")
    if [ "$python_path" = "" ]; then
        PYTHON_NAME=python3
        echo "use python3"
    fi
    $PYTHON_NAME --version
}

function detect_gcc_version
{
    gcc_ver=$(gcc -dumpversion | sed -E "s/(\\.0)*$//g")
    echo "$gcc_ver"
}

function install
{ 
    pushd $PROJECT_ROOT
 
    GCC_VERSION=`detect_gcc_version`
    echo "gcc version: ${GCC_VERSION}"
    
    CONAN_FILE="$SCRIPTS_ROOT/conanfile.linux-${TARGET_ARCH}-gcc${GCC_VERSION}.txt"
    if [ ! -f "$CONAN_FILE" ]; then
        echo "conan file ${CONAN_FILE} not found"
        exit 1
    fi
    
    PROFILE="${TARGET_ARCH}_gcc_${GCC_VERSION}"
    conan install $CONAN_FILE -r thirdparty -pr $PROFILE

    popd
}

install
