#!/bin/bash

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

pushd $SCRIPTS_ROOT

USER_UID=$(id -u)

function perf_record
{
    sudo ./perf record -F 99 -u $USER_UID -a -g -- sleep 30
}

function perf_script
{
    sudo ./perf script | ../../Flamegraph/stackcollapse-perf.pl | ../../Flamegraph/flamegraph.pl > flamegraph.svg
}

function usage
{
    echo "usage: $0 <cmd>"
    echo "record "
    echo "script "
}

case $1 in
record)
    perf_record
    ;;
script)
    perf_script
    ;;
*)
    usage
    ;;
esac
